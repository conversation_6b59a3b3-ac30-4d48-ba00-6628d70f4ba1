{"type": "project", "license": "proprietary", "minimum-stability": "dev", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "beberlei/doctrineextensions": "^1.5", "chillerlan/php-qrcode": "^5.0", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.12", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^3.2", "i2m/connectors": "dev-master", "bkf/connector": "dev-main", "i2m/iiot": "dev-master", "i2m/invoices": "dev-main", "i2m/payment": "dev-main", "i2m/reports": "dev-main", "league/oauth2-server-bundle": "^0.8.0", "mobiledetect/mobiledetectlib": "^4.8", "phpdocumentor/reflection-docblock": "^5.4", "phpstan/phpdoc-parser": "^1.29", "phpstan/phpstan": "^1.11", "sentry/sentry-symfony": "^5.0", "setasign/fpdi-fpdf": "^2.3", "symfony/asset": "6.4.*", "symfony/console": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/flex": "^2", "symfony/framework-bundle": "6.4.*", "symfony/lock": "6.4.*", "symfony/mailer": "6.4.*", "symfony/monolog-bundle": "^3.10", "symfony/property-access": "6.4.*", "symfony/property-info": "6.4.*", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/validator": "6.4.*", "symfony/yaml": "6.4.*", "ext-zip": "*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": true, "require": "6.4.*", "docker": true}}, "require-dev": {"dama/doctrine-test-bundle": "^8.2", "doctrine/doctrine-fixtures-bundle": "^3.6", "phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "*", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/debug-bundle": "6.4.*", "symfony/maker-bundle": "^1.60", "symfony/phpunit-bridge": "^7.1", "symfony/stopwatch": "6.4.*", "symfony/web-profiler-bundle": "6.4.*"}, "repositories": {"gitlab.bkf.pl/653": {"type": "composer", "url": "https://gitlab.bkf.pl/api/v4/group/653/-/packages/composer/packages.json"}, "gitlab.bkf.pl/166": {"type": "composer", "url": "https://gitlab.bkf.pl/api/v4/group/166/-/packages/composer/packages.json"}}}