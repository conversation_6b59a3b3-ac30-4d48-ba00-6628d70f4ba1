<?php

namespace App\Controller\Owner;

use App\Repository\CarwashRepository;
use App\Service\SyncData\SyncDataService;
use I2m\Connectors\Service\CwActionApi\ActionAdminService;
use I2m\Connectors\Model\CwActionApi\Carwash;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

class LoyaltyCarwashController extends AbstractController
{
    #[Route('/api/owner/{ownerId}/carwashes', methods: "GET")]
    public function getList(
        int $ownerId,
        SyncDataService $syncDataService,
        CarwashRepository $carwashRepository,
        ActionAdminService $apiCarwashService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $carwashes = $carwashRepository->findBy(['owner' => $owner]);

        $sns = array_map(function ($carwash) {
            return $carwash->getSn();
        }, $carwashes);

        $cwList = $apiCarwashService->getList($sns, 1, 999);

        foreach ($carwashes as $carwash) {
            $cw = array_filter(
                $cwList->getData(),
                function (Carwash $item) use ($carwash) {
                    return $item->getSn() == $carwash->getSn();
                }
            );
            $cw = reset($cw);
            if ($cw) {
                $carwash->setCwApi($this->actionApiToArray($cw));
            }
        }

        return $this->json(
            $carwashes,
            Response::HTTP_OK,
            [],
            [
                'groups' =>
                    [
                        'carwash:list',
                        "default:basic"
                    ],
                'datetime_format' => 'Y-m-d H:i:s']
        );
    }

    #[Route('/api/owner/{ownerId}/carwash/{id}', methods: "GET")]
    public function getCarwash(
        CarwashRepository $carwashRepository,
        ActionAdminService $apiCarwashService,
        int $id,
        SyncDataService $syncDataService,
        int $ownerId,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $carwash = $carwashRepository->findOneBy(
            [
                'id' => $id,
                'owner' => $owner
            ]
        );
        $carwash->setCwApi(
            $this->actionApiToArray($apiCarwashService->getList([$carwash->getSn()])->getData()[0])
        );

        return $this->json($carwash, Response::HTTP_OK, [], [
            'groups' =>
                [
                    'carwash:list',
                    "default:basic",
                ]
            ,'datetime_format' => 'Y-m-d H:i:s']);
    }

    #[Route('/api/owner/{ownerId}/carwash/{id}', methods: ["PATCH"])]
    public function setCarwash(
        SerializerInterface $serializer,
        Request $request,
        CarwashRepository $carwashRepository,
        int $id
    ): JsonResponse {
        $carwash = $carwashRepository->find($id);
        if (is_null($carwash)) {
            throw new NotFoundHttpException("carwash not found");
        }

        $carwash = $serializer->deserialize(
            $request->getContent(),
            \App\Entity\Carwash::class,
            'json',
            ['object_to_populate' => $carwash]
        );

        $carwashRepository->save($carwash);

        return $this->json(
            $carwash,
            JsonResponse::HTTP_OK,
            context: [
                'groups' =>
                    [
                        'carwash:list',
                        "default:basic"
                    ],
                ],
        );
    }

    private function actionApiToArray(Carwash $carwash): ?array
    {
        $ed = $carwash->getEdgeDevice();
        if (is_null($ed)) {
            return null;
        }

        $data =
            [
                'last_mobile_online' => $ed->getLastMobileOnline(),
                'mobile_ok' => $ed->isMobileOk(),
                'mobile_config' => $ed->getMobileType()?->value . (($ed->getMobileType() == "TCP") ?
                        " - {$ed->getIp()}:{$ed->getMobilePort()}" : null),
                'ip' => $ed->getIp(),
            ];
        $data['stands'] = [];

        foreach ($carwash->getStands() as $stand) {
            $data['stands'][] = [
                'id' => $stand->getId(),
                'bay_id' => $stand->getBayId(),
                'stand_code' => $stand->getStandCode(),
                'source' => $stand->getSource(),
                'mobile_enable' => $stand->isMobileEnable(),
            ];
        }

        return $data;
    }
}
