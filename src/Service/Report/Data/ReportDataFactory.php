<?php

namespace App\Service\Report\Data;

use App\Entity\Owners;
use App\Service\Report\ReportException;

class ReportDataFactory
{
    public function __construct(
        private LoyaltyClientUsageReport $loyaltyClientUsageReport,
    ) {
    }

    public function getReport(string $reportName, array $criteria, Owners $owner): ReportDataAbstract
    {

        return $this->getReportByName($reportName)
                ->setConfig(
                    $criteria,
                    $owner
                );
    }
    public function getReportByName(string $reportName): ReportDataAbstract
    {
        return match ($reportName) {
                LoyaltyClientUsageReport::class => $this->loyaltyClientUsageReport,
            default => throw new ReportException("Report not found: $reportName")
        };
    }
}
