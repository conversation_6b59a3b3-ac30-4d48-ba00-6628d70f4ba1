<?php

namespace App\Entity;

use App\Entity\Enum\Languages;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\Invoices\Enum\InvoiceGeneratorType;
use I2m\Invoices\Interface\IIssuer;
use I2m\StandardTypes\Enum\TimeZoneEnum;
use I2m\StandardTypes\Enum\Country;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: \App\Repository\OwnerConfigRepository::class)]
#[ORM\HasLifecycleCallbacks]
class OwnerConfig implements IIssuer
{
    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $mtime;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $ctime;

    #[ORM\Id]
    #[ORM\OneToOne(inversedBy: 'config', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private Owners $owner;

    #[ORM\Column(type: 'string', length: 64, nullable: true)]
    #[Groups(["owner:contact", 'default:basic'])]
    private ?string $supportEmail = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[Groups(["owner:contact", 'default:basic'])]
    private ?string $phone = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[Groups(["owner:contact", 'default:basic'])]
    private ?string $taxNumber = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[Groups(['default:basic'])]
    private ?string $regon = null;


    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(["owner:contact", 'default:basic'])]
    private ?string $address = null;

    #[ORM\Column(type: 'string', length: 16, nullable: true)]
    #[Groups(["owner:contact", 'default:basic'])]
    private ?string $postCode = null;

    #[ORM\Column(type: 'string', length: 128, nullable: true)]
    #[Groups(["owner:contact", 'default:basic'])]
    private ?string $city = null;

    #[ORM\Column(type: 'string', length: 128, nullable: true)]
    #[Groups(["owner:contact", 'default:basic'])]
    private ?string $name = null;

    #[ORM\Column(enumType: Country::class, length: 32, nullable: true)]
    #[Groups(['default:basic', "owner:contact"])]
    private ?Country $country = null;

    #[ORM\Column(type: 'string', length: 16, nullable: true)]
    #[Groups(['default:basic'])]
    private ?string $defaultPaymentTerms = null;

    #[ORM\Column(type: 'string', length: 16, nullable: true)]
    #[Groups(['default:basic'])]
    private ?string $defaultPaymentMethod = null;

    #[ORM\Column(length: 8, enumType: Languages::class)]
    #[Groups(['default:basic'])]
    private Languages $language = Languages::EN;

    #[ORM\Column(length: 64, enumType: TimeZoneEnum::class)]
    #[Groups(['default:basic'])]
    private TimeZoneEnum $timezone = TimeZoneEnum::GMT;

    #[ORM\Column(type: 'integer', nullable: true)]
    #[Groups(['default:basic'])]
    private ?int $vatTax = null;

    #[ORM\Column(type: 'text', nullable: true)]
    #[Groups(['owner:logo'])]
    private ?string $logo;

    #[Groups(['default:basic'])]
    #[ORM\Column(length: 16, enumType: InvoiceGeneratorType::class)]
    private InvoiceGeneratorType $invoiceType = InvoiceGeneratorType::Disabled;

    #[ORM\Column(nullable: true)]
    private ?array $invoiceConfig = null;

    #[ORM\Column(type: 'string', length: 64, nullable: true)]
    #[Groups(['default:basic'])]
    private ?string $invoiceCopyEmail = null;

    /**
     * @var string
     */
    #[Assert\Iban(message: 'This is not a valid International Bank Account Number (IBAN).')]
    #[ORM\Column(name: 'bank_account_number', type: 'string', length: 32, nullable: true)]
    #[Groups(['default:basic'])]
    protected ?string $bankAccountNumber = null;

    #[ORM\Column(nullable: true, type: "text")]
    private ?string $description = null;

    #[ORM\Column(nullable: true, type: "text")]
    private ?string $termOfUse = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['default:basic'])]
    private ?bool $autoRegisterCards = true;

    #[ORM\Column(nullable: true)]
    private ?bool $cardRegisterNotification = true;

    public function __construct(Owners $owner)
    {
        $this->owner = $owner;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps(): void
    {
        if (!isset($this->ctime)) {
            $this->ctime = new \DateTime('now');
        }
        $this->mtime = new \DateTime('now');
    }

    public function getId(): int
    {
        return $this->owner->getId();
    }

    public function getMtime(): \DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(\DateTimeInterface $mtime): static
    {
        $this->mtime = $mtime;
        return $this;
    }

    public function getCtime(): \DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(\DateTimeInterface $ctime): static
    {
        $this->ctime = $ctime;
        return $this;
    }

    public function getOwner(): Owners
    {
        return $this->owner;
    }

    public function setOwner(Owners $owner): static
    {
        $this->owner = $owner;
        return $this;
    }

    public function getSupportEmail(): ?string
    {
        return $this->supportEmail;
    }

    public function setSupportEmail(?string $supportEmail): static
    {
        $this->supportEmail = $supportEmail;
        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): static
    {
        $this->phone = $phone;
        return $this;
    }

    public function getTaxNumber(): string
    {
        return $this->taxNumber;
    }

    public function setTaxNumber(?string $taxNumber): static
    {
        $this->taxNumber = $taxNumber;
        return $this;
    }

    public function getRegon(): ?string
    {
        return $this->regon;
    }

    public function setRegon(?string $regon): static
    {
        $this->regon = $regon;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(?string $name): static
    {
        $this->name = $name;
        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): static
    {
        $this->address = $address;
        return $this;
    }

    public function getPostCode(): ?string
    {
        return $this->postCode;
    }

    public function setPostCode(?string $postCode): static
    {
        $this->postCode = $postCode;
        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): static
    {
        $this->city = $city;
        return $this;
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(?Country $country): static
    {
        $this->country = $country;
        return $this;
    }

    public function getDefaultPaymentTerms(): ?string
    {
        return $this->defaultPaymentTerms;
    }

    public function setDefaultPaymentTerms(?string $defaultPaymentTerms): static
    {
        $this->defaultPaymentTerms = $defaultPaymentTerms;
        return $this;
    }

    public function getDefaultPaymentMethod(): ?string
    {
        return $this->defaultPaymentMethod;
    }

    public function setDefaultPaymentMethod(?string $defaultPaymentMethod): static
    {
        $this->defaultPaymentMethod = $defaultPaymentMethod;
        return $this;
    }

    public function getVatTax(): ?int
    {
        return $this->vatTax;
    }

    public function setVatTax(?int $vatTax): static
    {
        $this->vatTax = $vatTax;
        return $this;
    }

    public function getLogo(): ?string
    {
        return $this->logo;
    }

    public function setLogo(?string $logo): static
    {
        $this->logo = $logo;
        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->getSupportEmail();
    }

    public function getType(): InvoiceGeneratorType
    {
        return $this->invoiceType;
    }

    public function getConfig(): array
    {
        return $this->invoiceConfig;
    }

    public function getLanguage(): ?Languages
    {
        return $this->language;
    }

    public function setLanguage(?Languages $language): static
    {
        $this->language = $language;
        return $this;
    }

    public function getInvoiceType(): InvoiceGeneratorType
    {
        return $this->invoiceType;
    }

    public function setInvoiceType(InvoiceGeneratorType $invoiceType): static
    {
        $this->invoiceType = $invoiceType;

        return $this;
    }

    public function getInvoiceConfig(): ?array
    {
        return $this->invoiceConfig;
    }

    public function setInvoiceConfig(?array $invoiceConfig): static
    {
        $this->invoiceConfig = $invoiceConfig;

        return $this;
    }

    public function getBankAccountNumber(): ?string
    {
        return $this->bankAccountNumber;
    }

    public function setBankAccountNumber(?string $bankAccountNumber): static
    {
        $this->bankAccountNumber = $bankAccountNumber;
        return $this;
    }

    public function getInvoiceCopyEmail(): ?string
    {
        return $this->invoiceCopyEmail;
    }

    public function setInvoiceCopyEmail(?string $invoiceCopyEmail): static
    {
        $this->invoiceCopyEmail = $invoiceCopyEmail;
        return $this;
    }

    public function getTimezone(): TimeZoneEnum
    {
        return $this->timezone;
    }

    public function setTimezone(TimeZoneEnum $timezone): void
    {
        $this->timezone = $timezone;
    }

    public function getTermOfUse(): ?string
    {
        return $this->termOfUse;
    }

    public function setTermOfUse(?string $termOfUse): static
    {
        $this->termOfUse = $termOfUse;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function isAutoRegisterCards(): ?bool
    {
        return $this->autoRegisterCards;
    }

    public function setAutoRegisterCards(?bool $autoRegisterCards): static
    {
        $this->autoRegisterCards = $autoRegisterCards;

        return $this;
    }

    public function isCardRegisterNotification(): ?bool
    {
        return $this->cardRegisterNotification;
    }

    public function setCardRegisterNotification(?bool $cardRegisterNotification): static
    {
        $this->cardRegisterNotification = $cardRegisterNotification;
        return $this;
    }

    public function setEmail(?string $email): static
    {
        $this->supportEmail = $email;
        return $this;
    }
}
