<?php

namespace App\Command;

use App\Service\Consumer\DataAnalyzer;
use I2m\IIot\Services\Protocols\Protocols;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Stopwatch\Stopwatch;
use Symfony\Component\Console\Command\LockableTrait;
use Psr\Log\LoggerInterface;
use DateTime;

use function Sentry\captureMessage;

#[AsCommand(
    name: 'loyalty:queue:consume',
)]
class BkfPullQueueCommand extends Command
{
    use LockableTrait;

    public function __construct(
        private LoggerInterface $logger,
        private Protocols $protocols,
        private DataAnalyzer $analyzer
    ) {
        parent::__construct();
    }

    protected function configure()
    {
        $this
                ->setDescription('Komenda pobiera pakiety z myjni.
            nastepnie analizuje alarmy i jesli uzna to za koniczne rozpoczyna nowy proces w JR')
            ->addOption(
                'queue',
                null,
                InputOption::VALUE_REQUIRED,
                'nazwa kolejki'
            )
        ->addOption(
            'thread',
            null,
            InputOption::VALUE_OPTIONAL,
            'numer wątku'
        );
        date_default_timezone_set('UTC');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $queue = $input->getOption('queue') .  "-" . getenv('APP_ENV');
        $thread = $input->getOption('thread');

        $lock_name = $this->getName() .  "-" . $queue ;

        if ($thread) {
            $lock_name .= "-$thread";
        }

        if (!$this->lock($lock_name)) {
            $this->logger->info('Trwa juz inna komenda: ' . $lock_name);
            return Command::SUCCESS;
        }

        $date = date_format(new DateTime(), 'Y-m-d H:i:s');
        $text = self::$defaultName . " - id: " . $date;

        $this->logger->notice($text);
        $io = new SymfonyStyle($input, $output);

        $stopwatch = new Stopwatch();
        $stopwatch->start('BkfPullQueue');
        $start = microtime(true);


        $packageCounter = 0;

        while ($dto = $this->protocols->pull($queue)) {
            ++$packageCounter;

            $this->analyzer->analyze($dto, $this->protocols->getTime());

            $execTime = microtime(true) - $start;
            if (($execTime > 10 * 60) || ($packageCounter > 500)) {
                $this->logger->notice("za dlugo tutaj siedze, id: $date");
                captureMessage("Nie udało się rozładować kolejki $lock_name");
                break;
            }
        }

        /**
         * podsumowanie.
         */
        $event = $stopwatch->stop('BkfPullQueue');

        $io->success('Ilosc odebranych pakietów ' . $packageCounter . " \n" . $event);
        $this->logger->notice(self::$defaultName .
                " - Zakonczylem, komende id: $date. Ilosc odebranych pakietów "
                . $packageCounter . " " . $event);
        return Command::SUCCESS;
    }
}
