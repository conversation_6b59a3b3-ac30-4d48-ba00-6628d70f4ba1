<?php

namespace App\Command;

use App\Repository\Invoice\InvoiceRepository;
use I2m\Invoices\Service\InvoiceService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'loyalty:invoices:fix',
    description: 'Add a short description for your command',
)]
class InvoiceFixCommand extends Command
{
    public function __construct(
        private InvoiceRepository $invoicesRepository,
        private InvoiceService $invoiceService,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'generate',
                'g',
                InputOption::VALUE_OPTIONAL,
                'generate missing documents',
                false
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $generateMissingInvoices = $input->getOption('generate');
        $invoices = $this->invoicesRepository->findBreakingInvoices();
        $count = count($invoices);

        foreach ($invoices as $invoice) {
            $io->writeln("Naprawiam dokument {$invoice->getId()}, number: {$invoice->getNumber()}, cloud: {$invoice->getCloudPath()}");
            if ($generateMissingInvoices) {
                $issuer = $invoice->getIssuer();
                $this->invoiceService->fixInvoice($issuer->getInvoiceType(), $issuer->getInvoiceConfig(), $invoice);
                $io->writeln("\t -> Naprawiony dokument {$invoice->getId()}, number: {$invoice->getNumber()}, cloud: {$invoice->getCloudPath()}");
            }
        }

        $count ? $io->caution("Znaleziono $count dokumentów") :
                $io->success("brak błędnych dokumentów")
            ;

        return Command::SUCCESS;
    }
}
