<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;

class HomeController extends AbstractController
{
    #[Route('/', methods: ['GET'])]
    public function index(Request $request)
    {
        $date =   new \DateTime();

        $data =  $this->file('version.h', 'version.txt', ResponseHeaderBag::DISPOSITION_INLINE);
        $data = str_replace("\n", "<br>", $data->getFile()->getContent());
        $data .= "Client IP: " . $request->getClientIp() . "<br>";
        $data .= "HOST: " . $request->getHost() . "<br>";
        $data .= "Date: " . $date->format("Y-m-d H:i:s") . "<br>";

        return new  Response($data);
    }

    public function error(): JsonResponse
    {
        // display the file contents in the browser instead of downloading it
        return new JsonResponse(["error" => "General error"]);
    }
}
