<?php

namespace App\Controller\User;

use App\Entity\Enum\Languages;
use App\Entity\User;
use App\Model\ClientApp\PayByCardToken;
use App\Model\ClientApp\SupportRequest;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\TransactionsRepository;
use App\Repository\UserRepository;
use App\Service\Loyalty\VirtualCardManager;
use App\Service\User\UserService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;

class ProfileController extends AbstractController
{
    #[Route('/api/user/profile', methods: ['GET'])]
    public function getProfile(): JsonResponse
    {

        /** @var User $user */
        $user = $this->getUser();

        return $this->json($user, JsonResponse::HTTP_OK, [], ['groups' => [
            'default:basic'
        ]]);
    }

    #[Route('/api/user/profile/language/{language}', methods: ['POST'])]
    public function setLang(string $language, UserRepository $repository): JsonResponse
    {

        /** @var User $user */
        $user = $this->getUser();
        $user->setLanguage(Languages::from($language));
        $repository->save($user);

        return $this->json($user, JsonResponse::HTTP_OK, [], ['groups' => [
            'default:basic'
        ]]);
    }

    #[Route('/api/user/profile', methods: ['DELETE'])]
    public function deleteAction(
        UserService $userService,
        #[MapRequestPayload] SupportRequest $supportRequest,
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $userService->deleteAccount($user, $supportRequest->getDescription());


        return $this->json(null, JsonResponse::HTTP_OK);
    }
}
