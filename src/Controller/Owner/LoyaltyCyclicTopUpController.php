<?php

namespace App\Controller\Owner;

use App\Entity\Loyalty\CyclicTopUp;
use App\Model\Date\DateTimeNullableRangeRequest;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\CyclicTopUpRepository;
use App\Repository\OwnerRepository;
use App\Service\SyncData\SyncDataService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

class LoyaltyCyclicTopUpController extends AbstractController
{
    #[Route('/api/owner/{ownerId}/cyclic_top_ups', methods: ['GET'])]
    public function getList(
        int $ownerId,
        Request $request,
        CyclicTopUpRepository $topUpRepository,
        SyncDataService $syncDataService,
        CardsRepository $cardsRepository
    ): JsonResponse {

        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $email = $request->query->get('email');
        $cardNumber = $request->query->get('card');
        $cardToken = $request->query->get('cardToken');
        $page = $request->query->get('page', 1);
        $itemsPerPage = $request->query->get('itemsPerPage', 50);
        $search = $request->query->get('search');
        $isActive = $request->query->get('isActive');

        // Check for invalid pagination and ordering
        if (!is_numeric($page) || $page < 1 || !is_numeric($itemsPerPage) || $itemsPerPage < 1) {
            return $this->json(['error' => 'Invalid pagination parameters'], Response::HTTP_BAD_REQUEST);
        }

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get('timezone')
        );

        $card = null;
        if ($cardNumber) {
            $card = $cardsRepository->getCardByNumber($cardNumber, $owner);
            if (!$card) {
                return $this->json(
                    ['error' => "Card by number not found"],
                    Response::HTTP_NOT_FOUND
                );
            }
        } elseif ($cardToken) {
            $card = $cardsRepository->getCardByToken($cardToken, $email);
            if (!$card) {
                return $this->json(
                    ['error' => "Card by token not found"],
                    Response::HTTP_NOT_FOUND
                );
            }
        }

        $topUps = $topUpRepository->getList(
            $owner,
            $card,
            $page,
            $itemsPerPage,
            $search,
            $isActive
        );

        return $this->json($topUps, JsonResponse::HTTP_OK, [], [
            'groups' => [
                "loyalty:cyclic_topup:list",
                'loyalty:cards:client',
                "default:basic",
            ],
            'datetime_format' => 'Y-m-d H:i:s',
            'timezone' => $dateRange->getTimezone()
        ]);
    }

    #[Route('/api/owner/{ownerId}/cyclic_top_up/{id}', methods: ['GET'])]
    public function getSingle(
        int $ownerId,
        CyclicTopUpRepository $topUpRepository,
        SyncDataService $syncDataService,
        int $id,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $cyclicTopUp = $topUpRepository->getConfig2(
            $owner,
            $id
        );

        return $this->json($cyclicTopUp, Response::HTTP_OK, [], [
            'groups' => [
                "loyalty:cyclic_topup:list",
                'loyalty:cards:client',
                "default:basic",
            ],
        ]);
    }

    #[Route('/api/owner/{ownerId}/cyclic_top_up/{id}', methods: ['PUT'])]
    public function editSingle(
        int $ownerId,
        Request $request,
        CyclicTopUpRepository $topUpRepository,
        SyncDataService $syncDataService,
        SerializerInterface $serializer,
        int $id
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $cyclicTopUp = $topUpRepository->getConfig2(
            $owner,
            $id
        );

        $serializer->deserialize(
            $request->getContent(),
            CyclicTopUp::class,
            'json',
            [
                'object_to_populate' => $cyclicTopUp,
                'groups' =>
                    [
                        'loyalty:cyclic_topup:edit'
                    ]
            ]
        );

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get('timezone')
        );

        $topUpRepository->save($cyclicTopUp);

        return $this->json($cyclicTopUp, Response::HTTP_OK, [], [
            'groups' => [
                "loyalty:cyclic_topup:list",
                'loyalty:cards:client',
                "default:basic",
            ],
            'datetime_format' => 'Y-m-d H:i:s',
            'timezone' => $dateRange->getTimezone()
        ]);
    }
}
