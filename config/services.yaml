# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    bkf.queue.ip: '%env(resolve:QUEUE_IP)%'
    bkf.queue.port: '%env(resolve:QUEUE_PORT)%'
    bkf.queue.user: '%env(resolve:QUEUE_USER)%'
    bkf.queue.pass: '%env(resolve:QUEUE_PASS)%'
    bkf.queue.exchange: '%env(resolve:QUEUE_EXCHANGE)%'
    bkf.queue.income: '%env(resolve:QUEUE_INCOME)%'

    carwash_api_url: '%env(CARDS_API_URL)%'

    i2m.cw-action-api.token: '%env(CW_ACTION_API_TOKEN)%'
    i2m.payment.url: '%env(WEB_URL)%/external_payment/gate'

    mailer_from: '%env(MAILER_FROM)%'

    app_name: '%env(APP_NAME)%'
    company_name: '%env(COMPANY_NAME)%'
    company_address: '%env(COMPANY_ADDRESS)%'
    company_city: '%env(COMPANY_CITY)%'
    company_postcode: '%env(COMPANY_POSTCODE)%'
    company_email: '%env(COMPANY_EMAIL)%'
    appLink_apple: '%env(APP_LINK_APPLE)%'
    appLink_google: '%env(APP_LINK_GOOGLE)%'

    i2m.storage.container: 'bkfpay'
    i2m.storage.connection_string: '%env(STORAGE_CONNECTION)%'

    i2m.invoice.storage-path: '%env(APP_ENV)%/invoices'
    i2m.reports.storage-path: '%env(APP_ENV)%/reports'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    I2m\Invoices\Interface\I2mInvoiceRepositoryInterface: '@App\Repository\Invoice\InvoiceRepository'
