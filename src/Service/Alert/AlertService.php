<?php

namespace App\Service\Alert;

use App\Entity\Enum\Languages;
use App\Entity\Loyalty\Clients;
use App\Service\Alert\Model\Alert;
use App\Service\Alert\Model\AlertLevel;
use Symfony\Component\Translation\Translator;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Constraints\Email;
use I2m\Invoices\Service\Validator\TaxNumberValidator;

class AlertService
{
    private ValidatorInterface $validator;

    public function __construct(
        private TranslatorInterface $translator
    ) {
        $this->validator = Validation::createValidator();
    }

    public function setLocale(Languages $lang): self
    {
        /**
         * @var Translator $translator
         */
        $translator = $this->translator;
        $translator->setLocale($lang->value);
        return $this;
    }

    /**
     * @return Alert[]
     */
    public function getClientAlerts(Clients $client): array
    {
        $alerts = [];

        if ($client->isSendSummaryReport() && empty($client->getEmail())) {
            $alerts[] =
                (new Alert())
                    ->setLevel(AlertLevel::WARNING)
                    ->setTitle($this->translator->trans('alert.client_report_without_email.title'))
                    ->setText($this->translator->trans('alert.client_report_without_email.text'))
            ;
        }

        if (!empty($client->getEmail()) && !$this->isEmailValid($client->getEmail())) {
            $alerts[] =
                (new Alert())
                    ->setLevel(AlertLevel::WARNING)
                    ->setTitle($this->translator->trans('alert_incorrect_email_title'))
                    ->setText($this->translator->trans('alert_incorrect_email_text'))
            ;
        }

        if (
            $client->invoicingEnabled()
            && $client->getTaxNumber() !== null
            && $client->getCountry() !== null
            && !TaxNumberValidator::validate($client->getCountry(), $client->getTaxNumber())
        ) {
            $alerts[] =
                (new Alert())
                    ->setLevel(AlertLevel::WARNING)
                    ->setTitle($this->translator->trans('alert_incorrect_tax_number_title'))
                    ->setText($this->translator->trans('alert_incorrect_tax_number_text'))
            ;
        }

        return $alerts;
    }

    private function isEmailValid($email): bool
    {
        $violations = $this->validator->validate($email, [
            new Email(['message' => 'Invalid email address.'])
        ]);

        return $violations->count() === 0;
    }
}
