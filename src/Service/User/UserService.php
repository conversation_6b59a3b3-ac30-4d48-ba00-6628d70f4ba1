<?php

namespace App\Service\User;

use App\Entity\Enum\Languages;
use App\Entity\Owners;
use App\Entity\User;
use App\Exception\User\InvalidTokenException;
use App\Model\User\UserRegister;
use App\Repository\UserRepository;
use App\Service\Email\EmailSender;
use App\Service\Support\SupportService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Translation\LocaleSwitcher;
use Symfony\Component\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Constraints;

class UserService
{
    public function __construct(
        private readonly UserPasswordHasherInterface $userPasswordHasher,
        private readonly UserRepository $userRepository,
        private readonly EmailSender $emailSender,
        private readonly TranslatorInterface $translator,
        private readonly UrlGeneratorInterface $urlGenerator,
        private readonly RequestStack $requestStack,
        private readonly LocaleSwitcher $localeSwitcher,
        private readonly ParameterBagInterface $parameterBag,
        private readonly SupportService $supportService
    ) {
    }

    public function create(UserRegister $userRegister): void
    {
        $user = $this->userRepository->findOneByEmail($userRegister->getEmail());

        if (!is_null($user)) {
            throw new UserExistException();
        }


        $token = sha1($userRegister->getEmail() . $userRegister->getPlainPassword() . time());
        $user = (new User())
            ->setEmail($userRegister->getEmail())
            ->setRegistrationToken($token)
            ->setLanguage($userRegister->getLang())
        ;

        $user->setPassword(
            $this->userPasswordHasher->hashPassword($user, $userRegister->getPlainPassword())
        );

        $this->userRepository->save($user);

        /** @var Translator $translator */
        $translator = $this->translator;
        $translator->setLocale($userRegister->getLang()->value);

        $this->emailSender->sendHtml(
            $user->getEmail(),
            $this->translator->trans('registration.title'),
            'registration/registration_email.html.twig',
            context: [
                'registration_confirm_link' => $this->buildRegistrationConfirmLink($token, 'register_confirm'),
            ]
        );
    }

    public function inviteUserIfNotExist(?string $email, Owners $owner): ?User
    {
        if ($this->isEmailInvalid($email)) {
            return null;
        }

        $user = $this->userRepository->findOneByEmail($email);
        if ($user) {
            return $user;
        }

        $user = $this->createUserWithToken($email, $owner->getConfig()->getLanguage());

        $this->sendInvitationEmail($email, $user->getRegistrationToken(), $owner);

        return null;
    }

    public function confirmRegistration(string $token): User
    {
        $user = $this->findUserByRegistrationToken($token);
        if ($user === null) {
            throw new InvalidTokenException();
        }

        $user->setVerified(true);
        $user->setRegistrationToken(null);
        $this->userRepository->save($user);

        return $user;
    }

    public function findUserByRegistrationToken(string $token): ?User
    {
        return $this->userRepository->findOneBy(['registrationToken' => $token]);
    }

    public function confirmRegistrationFromInvitation(string $token, string $password): User
    {
        $user = $this->findUserByRegistrationToken($token);
        if ($user === null) {
            throw new InvalidTokenException();
        }

        if (!$user->isVerified()) {
            $user->setVerified(true);
            $user->setPassword(
                $this->userPasswordHasher->hashPassword($user, $password)
            );
            $user->setRegistrationToken(null);

            $this->userRepository->save($user);
        }

        return $user;
    }

    private function buildRegistrationConfirmLink(string $token, string $route): string
    {
        $url = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();

        return $url . $this->urlGenerator->generate(
            $route,
            [
                'token' => $token,
                ],
            UrlGeneratorInterface::ABSOLUTE_PATH,
        );
    }

    private function isEmailInvalid(?string $email): bool
    {
        if (empty($email)) {
            return true;
        }

        $validator = Validation::createValidator();
        return count($validator->validate($email, new Constraints\Email())) > 0;
    }

    private function createUserWithToken(string $email, Languages $language): User
    {
        $token = $this->generateToken($email);

        $user = (new User())
        ->setEmail($email)
        ->setRegistrationToken($token)
        ->setPassword('TO_INITIALIZE')
        ->setLanguage($language);

        $this->userRepository->save($user);

        return $user;
    }

    private function generateToken(string $email): string
    {
        return sha1(uniqid($email, true));
    }

    private function sendInvitationEmail(string $email, string $token, Owners $owner): void
    {
        $ownerName = $owner->getName();
        $locale = $owner->getConfig()->getLanguage()->value;

        $this->localeSwitcher->runWithLocale($locale, function ($locale) use ($email, $ownerName, $token) {
            $this->emailSender->sendHtml(
                $email,
                $this->translator->trans('invitation.to_app', ['%appName%' => $this->parameterBag->get('app_name')], locale: $locale),
                'registration/invite_email.html.twig',
                context: [
                'registration_link' => $this->buildRegistrationConfirmLink($token, 'invite_register'),
                'ownerName' => $ownerName,
                ]
            );
        });
    }

    public function deleteAccount(User $user, string $description)
    {
        $user->setEnabled(false);
        $user->setEmail(time() . '_' . $user->getEmail());
        $this->userRepository->save($user);
        $this->supportService->createTicketFromDeleteAccount($user, $description);
    }
}
