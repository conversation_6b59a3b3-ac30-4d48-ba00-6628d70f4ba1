<?php

namespace App\Service\QrPrintGenerator;

use App\Entity\Enum\Languages;
use I2m\IIot\Tools\Ean8Helper;
use Knp\Snappy\Pdf;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class StickerService extends AbstractQrPrintGenerator
{
    public function __construct(
        readonly Environment $templating,
        readonly TranslatorInterface $translator,
        readonly Pdf $pdfPrinter,
        private ParameterBagInterface $parameterBag,
    ) {
        parent::__construct($templating, $translator, $pdfPrinter);
    }

    protected function qrCodeLink($code): string
    {
        $host = "https://vc.i2m.pl";

        return "$host/stand/$code";
    }

    protected function overlayTemplate(): string
    {
        return 'sticker/sticker.pdf.html.twig';
    }

    protected function overlayPageParameter(): array
    {
        return [
            'page-width' => '8.4cm',
            'page-height' => '11.4cm',
            'margin-bottom' => 0,
            'margin-left' => '1.8cm',
            'margin-right' => 0,
            'margin-top' => '2cm',
        ];
    }

    protected function pdfBackgroundPath(Languages $language): ?string
    {
        return
            $this->parameterBag->get('kernel.project_dir') . DIRECTORY_SEPARATOR .
                "printables" . DIRECTORY_SEPARATOR .
                $language->value . DIRECTORY_SEPARATOR .
                "stand.pdf";
    }

    public function genrateSeries(
        int $start,
        int $end,
        Languages $language,
        string $exportPath
    ) {

        $filesystem = new Filesystem();

        $filesystem->mkdir($exportPath);

        for ($i = $start; $i <= $end; $i++) {
            $code = $i . Ean8Helper::checkSumEAN8((string)$i);
            $this->validateStandCode($code);
            $file = $this->generateSingle(
                [
                    'code' => $code
                ],
                $language
            );
            $filesystem->copy($file, $exportPath . DIRECTORY_SEPARATOR . $code . ".pdf");
        }
    }

    /**
     * @param string $ena8StandCode
     * throws \Exception
     */
    private function validateStandCode(string $ena8StandCode)
    {
        $ean8withoutValidationCode = $this->getStandCode($ena8StandCode);

        $calculatedEan = Ean8Helper::checkSumEAN8($ean8withoutValidationCode);

        if ($ena8StandCode != ($ean8withoutValidationCode . $calculatedEan)) {
            throw new \Exception('Invalid stand code. Check sum not correct');
        }
    }

    private function getStandCode(string $ena8StandCode): string
    {
        return substr($ena8StandCode, 0, 7);
    }
}
