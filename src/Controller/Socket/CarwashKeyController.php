<?php

namespace App\Controller\Socket;

use App\Model\Loyalty\AskRevalue;
use App\Repository\Carwash\CarwashConnectionStatusRepository;
use App\Repository\Carwash\CarwashLogRepository;
use App\Repository\Carwash\EdgeDeviceRepository;
use App\Service\CarwashCrypto;
use App\Service\Connectors\CwProtocolsConnector;
use App\Service\Loyalty\CardsManager;
use I2m\StandardTypes\Enum\EdgeDeviceType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Psr\Log\LoggerInterface;
use App\Entity\Carwash;
use Doctrine\ORM\EntityManagerInterface;
use DateTime;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

class CarwashKeyController extends AbstractController
{
    public function __construct(
        private CardsManager $cardsManager,
        private LoggerInterface $logger,
        private EntityManagerInterface $em,
        private CwProtocolsConnector $cwProtocolsConnector,
        private CarwashLogRepository $carwashLogRepository,
        private SerializerInterface $serializer,
        private EdgeDeviceRepository $edgeDeviceRepository,
        private CarwashConnectionStatusRepository $carwashConnectionStatusRepository,
    ) {
    }

    #[Route('/plc/{plc}')]
    public function index(Request $request, string $plc): Response
    {
        $key = $request->query->get('key');
        $cmd = $request->query->get('cmd');
        $method = $request->getMethod();
        $body = $request->getContent();

        // do usuniecia gdy myjnie zaczną nadawać bezpośrednio
        $ip = empty($request->headers->get('X-BKF-Source-Ip')) ?
                $request->getClientIp() : $request->headers->get('X-BKF-Source-Ip');

        if (is_null($key)) {
            $resp = $this
                    ->cwProtocolsConnector
                    ->socket(
                        'http://carwash-key-proxy.1.v1.bkf.pl/plc/card/transactions/' . $plc,
                        $body,
                        $ip
                    );

            return new Response($resp['body'], $resp['code']);
        }

        /*
         * sprawdzam czy request jest poprawny
         * tzn. ma numer plc i body
         */
        if (!$plc) {
            $msg = json_encode(['error' => 'unspecified plc']);
            $this->log($request, $msg);
            return new Response($msg, Response::HTTP_BAD_REQUEST);
        }

        /*
         * szukam myjni o danym numerze PLC w bazie
         */
        $edgeDevice = $this->edgeDeviceRepository->findOneBy(['type' => EdgeDeviceType::PLC, 'uid' => $plc]);
        if (is_null($edgeDevice)) {
            $msg = json_encode(['error' => 'unknown plc']);
            $this->log($request, $msg);
            return new Response($msg, Response::HTTP_FORBIDDEN);
        }

        $carwash = $edgeDevice->getCarwash();
        if (!$carwash) {
            $msg = json_encode(['error' => 'unknown carwash']);
            $this->log($request, $msg);
            return new Response($msg, Response::HTTP_FORBIDDEN);
        }
        $this->carwashConnectionStatusRepository->update($carwash, "SOCKET", true, new \DateTime());
        $decrypted =  CarwashCrypto::decrypt($edgeDevice, $body, CarwashCrypto::PASS_SOCKET);
        //$this->log($request, $decrypted, $carwash);


        if ($cmd == 'info') {
            return $this->info($edgeDevice, $key);
        }

        if ($method == 'POST') {
            return $this->ackRevalue($edgeDevice, $key, $decrypted);
        }

        if ($method == 'GET') {
            $askMessage = !empty($decrypted) ?
                $this->serializer->deserialize($decrypted, AskRevalue::class, 'json') :
                null;
            return $this->askRevalue($edgeDevice, $key, $askMessage);
        }
        $msg = json_encode(['error' => 'method not avalible']);
        return new Response($msg, Response::HTTP_BAD_REQUEST);
    }

    public function askRevalue(Carwash\EdgeDevice $edgeDevice, $key, ?AskRevalue $askRevalue): Response
    {
        $carwash = $edgeDevice->getCarwash();
        $revalue = $this->cardsManager->getRevalue($edgeDevice, $key, $askRevalue);

        $data = json_encode(['g_i2m_rev' => $revalue]);

        $encrypted = CarwashCrypto::encrypt($edgeDevice, $data, CarwashCrypto::PASS_SOCKET);
        $digest = CarwashCrypto::digest($edgeDevice, $encrypted, CarwashCrypto::PASS_SOCKET);

        $response = new Response($encrypted, Response::HTTP_OK);
        $response->headers->set('X-Hmac-md5-digest', $digest);
        $response->headers->set('Content-Length', strval(strlen($encrypted)));


        $this->carwashLogRepository->log(
            $carwash,
            "LOYALTY",
            $this->cardsManager->convert($key),
            "askRevalue: "
                                         . json_encode($askRevalue)
            . " -> $data"
        );
        $this->em->flush();

        return $response;
    }

    public function ackRevalue(Carwash\EdgeDevice $edgeDevice, $key, $decrypted): Response
    {
        $carwash = $edgeDevice->getCarwash();

        $data = json_decode($decrypted, true);

        $value = $data['g_i2m_rev']['value'];
        $currency = !empty($data['g_i2m_rev']['sCurrency']) ? $data['g_i2m_rev']['sCurrency'] : null;
        $cmd = !empty($data['g_i2m_rev']['cmd']) ? $data['g_i2m_rev']['cmd'] : null;
        $left = $this->cardsManager->ackRevalue($carwash, $key, $value, $currency, $cmd);

        $this->carwashLogRepository->log($carwash, "LOYALTY", $this->cardsManager->convert($key), "ackRevalue: " . $decrypted);
        $this->em->flush();
        return new Response(CarwashCrypto::encrypt($edgeDevice, "left: $left", CarwashCrypto::PASS_SOCKET));
    }

    public function info(Carwash\EdgeDevice $edgeDevice, $key): Response
    {
        $carwash = $edgeDevice->getCarwash();
        $client = $this->cardsManager->getClientInfo($carwash, $key);
        $json = $this->serializer->serialize($client, 'json');
        $this->carwashLogRepository->log($carwash, "LOYALTY", $this->cardsManager->convert($key), "ask info: $json");
        $this->em->flush();
        return new Response(CarwashCrypto::encrypt($edgeDevice, $json, CarwashCrypto::PASS_SOCKET));
    }

    private function log(Request $request, string $msg = null, ?Carwash $carwash = null)
    {
        $sn = is_null($carwash) ? "unknown" : $carwash->getSn();

        $this->logger->notice(
            "URL: " . $request->getUri() .
                " carwash: " . $sn .
                " IP: " . $request->getClientIp()
        );
    }
}
