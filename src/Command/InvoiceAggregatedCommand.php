<?php

namespace App\Command;

use App\Repository\Loyalty\ClientsRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Service\Loyalty\InvoiceTopUpService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'loyalty:invoices:aggregated2',
    description: 'Add a short description for your command',
)]
class InvoiceAggregatedCommand extends Command
{
    public function __construct(
        private ClientsRepository $clientsRepository,
        private TopUpRepository $topUpRepository,
        private InvoiceTopUpService $invoiceTopUpService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('arg1', InputArgument::OPTIONAL, 'Argument description')
            ->addOption('option1', null, InputOption::VALUE_NONE, 'Option description')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $periodStart = (new \DateTimeImmutable("first day of previous month"))->setTime(0, 0, 0);
        $periodEnd =  (new \DateTimeImmutable("last day of previous month"))->setTime(23, 59, 59);

        $clients = $this->clientsRepository->getForInvoicing();
        foreach ($clients as $client) {
            $io->note("Owner {$client->getOwner()->getName()}, Client: {$client->getName()}");
            $topUps = $this
                ->topUpRepository
                ->getForInvoicing(
                    date_from: $periodStart,
                    date_to: $periodEnd,
                    client: $client
                );
            if ($topUps) {
                $invoice = $this->invoiceTopUpService->generateList(
                    client: $client,
                    topUps: $topUps,
                    serviceDate: $periodEnd,
                    paymentTerm: $client->getPaymentTerm()
                );
                $io->note("\t{$invoice->getNumber()}");
            }
        }

        $io->success('Success');

        return Command::SUCCESS;
    }
}
