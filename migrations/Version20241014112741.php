<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241014112741 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE top_up ADD invoice2_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE top_up ADD CONSTRAINT FK_34813B767FC852D FOREIGN KEY (invoice2_id) REFERENCES invoice (id)');
        $this->addSql('CREATE INDEX IDX_34813B767FC852D ON top_up (invoice2_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE top_up DROP FOREIGN KEY FK_34813B767FC852D');
        $this->addSql('DROP INDEX IDX_34813B767FC852D ON top_up');
        $this->addSql('ALTER TABLE top_up DROP invoice2_id');
    }
}
