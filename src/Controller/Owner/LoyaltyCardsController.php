<?php

namespace App\Controller\Owner;

use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Loyalty\Enum\TopUpStatus;
use App\Model\Date\DateTimeNullableRangeRequest;
use App\Model\Loyalty\Card;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\ClientsRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Repository\Loyalty\TransactionsRepository;
use App\Repository\UserRepository;
use App\Service\Loyalty\CardsManager;
use App\Service\SyncData\SyncDataService;
use App\Service\User\UserService;
use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

use function Sentry\captureMessage;

#[Route('/api/owner/{ownerId}/cards')]
class LoyaltyCardsController extends AbstractController
{
    #[Route('', methods: ['GET'])]
    public function getCards(
        int $ownerId,
        Request $request,
        CardsRepository $cardsRepository,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $order =  $request->query->get('order', 'lastContact');
        $orderDir =  $request->query->get('orderDir', 'DESC');
        $email = $request->query->get('email');
        $clientId = $request->query->getInt('clientId');
        $isVirtual = $request->query->get('isVirtual');
        $isActive = $request->query->get('isActive');
        $search = $request->query->get('search');
        $hasAlias = $request->query->get('hasAlias');
        $hasBalance = $request->query->get('hasBalance');
        $details = $request->query->getBoolean('details');
        $stats = $request->query->getBoolean('stats');

        $page = $request->query->get('page', 1);
        $itemsPerPage = $request->query->get('itemsPerPage', 50);
        $showTotal = $request->query->getBoolean('showTotal', true);

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get('timezone')
        );


        $cards = $cardsRepository->getCards(
            $search,
            $email,
            $owner,
            $clientId,
            $isVirtual,
            $isActive,
            $hasAlias,
            $hasBalance,
            $page,
            $itemsPerPage,
            $order,
            $orderDir,
            $dateRange->getDateFrom(),
            $dateRange->getDateTo(),
            $showTotal
        );

        $groups = [
            "loyalty:cards:list",
            "loyalty:client:owner",
            "default:basic",
        ];

        if ($details) {
            $groups[] = "owner:logo";
        }
        $context = [
            'groups' => $groups,
            'datetime_format' => 'Y-m-d H:i:s',
            'timezone' => $dateRange->getTimezone()
        ];

        if ($stats) {
            $context['stats'] = [
                'from' => $dateRange->getDateFrom(),
                'to' => $dateRange->getDateTo(),
                'timezone' => $dateRange->getTimezone(),
            ];
        }

        return $this->json($cards, JsonResponse::HTTP_OK, [], $context);
    }

    #[Route('', methods: ['POST'])]
    public function createCard(
        int $ownerId,
        ClientsRepository $clientsRepository,
        SyncDataService $syncDataService,
        CardsRepository $cardsRepository,
        UserService $userService,
        CardsManager $cardsManager,
        #[MapRequestPayload] Card $cardModel,
        Request $request,
    ): JsonResponse {

        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        /** @var Cards $card */
        $card = (new Cards())
            ->setOwner($owner)
            ->setCurrency($owner->getCurrency())
            ->setEmail($cardModel->getEmail())
            ->setAlias($cardModel->getAlias())
            ->setNumber($cardModel->getNumber())
            ->setDescription($cardModel->getDescription())
        ;

        $card
            ->setStatus($cardModel->getStatus() ?? CardStatus::ACTIVE);
        $card
            ->setClient($clientsRepository->getClient($cardModel->getClientId(), $owner));

        try {
            $card = $cardsRepository->save($card);
        } catch (UniqueConstraintViolationException $exception) {
            return $this->json(
                ["error" => "card with number {$card->getNumber()} alerady exist"],
                JsonResponse::HTTP_CONFLICT
            );
        }

        $userService->inviteUserIfNotExist($card->getEmail(), $owner);

        if ($cardModel->getValue()) {
            $cardsManager->addTopUpWithBonus(
                $card,
                $cardModel->getValue(),
                TopUpStatus::ACTIVE,
                $request->headers->get('X-User-Email')
            );
        }

        return $this->json(
            $card,
            JsonResponse::HTTP_CREATED,
            [],
            [
                'groups' => [
                    "loyalty:cards:list",
                    "default:basic"
                ]]
        );
    }
    #[Route('/stats', methods: ['GET'])]
    public function getCardsStats(
        int $ownerId,
        Request $request,
        CardsRepository $cardsRepository,
        SyncDataService $syncDataService,
        TopUpRepository $topUpRepository,
        TransactionsRepository $transactionsRepository
    ): JsonResponse {

        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            return $this->json(
                [],
                JsonResponse::HTTP_OK,
                [],
                [
                    'groups' => [
                        "default:basic"
                    ]]
            );
            // throw new NotFoundHttpException("owner not found"); <- aby nie rzucać błędów w CM
        }

        $email = $request->query->get('email');
        $cardNumber = $request->query->get('card');
        $cardToken = $request->query->get('cardToken');

        $card = null;
        if ($cardNumber) {
            $card = $cardsRepository->getCardByNumber($cardNumber, $owner);
            if (!$card) {
                return $this->json(
                    ['error' => "Card by number not found"],
                    JsonResponse::HTTP_NOT_FOUND
                );
            }
        } elseif ($cardToken) {
            $card = $cardsRepository->getCardByToken($cardToken, $email);
            if (!$card) {
                return $this->json(
                    ['error' => "Card by token not found"],
                    JsonResponse::HTTP_NOT_FOUND
                );
            }
        }

        $dr = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get('timezone')
        );
        $currency = $card?->getCurrency() ?? $owner->getCurrency();
        $data = [
            'topUp' =>
                $topUpRepository->getStats($card, $owner, $dr->getDateFrom(), $dr->getDateTo()),
            'transaction' =>
                $transactionsRepository->getStats($currency, $card, $owner, $dr->getDateFrom(), $dr->getDateTo()),
            'currency' => $currency
        ];

        return $this->json(
            $data,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    "default:basic"
                ]]
        );
    }
}
