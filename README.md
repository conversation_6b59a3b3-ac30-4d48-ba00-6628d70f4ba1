 
 ```
 sudo add-apt-repository ppa:ondrej/php
 sudo add-apt-repository ppa:ondrej/nginx
 sudo apt-get update -y && sudo apt-get upgrade -y && sudo apt-get dist-upgrade -y && sudo apt-get autoremove -y && sudo apt-get clean -y
 sudo apt install php-cli php-intl php-xml php-curl php-mbstring php-zip php-gd php-intl php-cli unzip nginx php-mysql php-fpm mariadb-server certbot python3-certbot-nginx -y
 ```

### Generowanie PDF z HTML - wkhtml2pdf

Aplikacja wymaga wkhtml2pdf w wersji z pachem do qt ze strony https://wkhtmltopdf.org/downloads.html

### Jak sprawdzić wersję wkhtml2pdf
``` wkhtmltopdf --version```
powinno zw<PERSON><PERSON><PERSON><PERSON> wersję z dopisekiem (with patched qt)

### Instalacja

```wget https://github.com/wkhtmltopdf/packaging/releases/download/********-2/wkhtmltox_********-2.jammy_amd64.deb```

```dpkg -i ./wkhtmltox_********-2.jammy_amd64.deb```


### Tworzenie klienta

```
php bin\console league:oauth2-server:create-client --grant-type=password --scope=USER vc-app
```

### Utworzenie użytkownika

POST /register
Content-Type: application/json


{"email": "<EMAIL>", "plainPassword": "qaz12wsx"}

### logowanie:

POST /token
Content-Type: application/json

```json
{
    "username": "<EMAIL>",
    "password": "qaz12wsx",
    "grant_type":"password",
    "client_id":"8978ce9318e2a8e4c92f5c248e7fcf5b",
    "client_secret":"86e91bc83cc9d422798337986a2753d1b...."
}
```

Response:

```json
{
    "token_type": "Bearer",
    "expires_in": 2591999,
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
    "refresh_token": "def502000e009dc01de0f515d3660378819..."
}
```

### karty

* Lista kart uzytkownika:  GET /api/user/cards
* Lista kart użytkownika dla stanowiska: GET /api/user/stand/{standCode}/cards
* lista transakcji dla karty GET /api/user/card/{cardToken}/transactions?page=1&itemsPerPage=10

Płatność kartą na stanowisku

POST /api/user/card/{cardToken}/pay

```json
{"value": 5, "standCode" : "00150019"}
```




