<?php

namespace App\Controller\Owner;

use App\Entity\ExternalPayment\PaymentGate;
use App\Repository\ExternalPayment\PaymentGateRepository;
use App\Repository\OwnerRepository;
use App\Service\SyncData\SyncDataService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

class LoyaltyGateController extends AbstractController
{
    #[Route('/api/owner/{ownerId}/gate/config', methods: ["PUT"])]
    public function saveConfig(
        Request $request,
        PaymentGateRepository $paymentGateRepository,
        OwnerRepository $ownerRepository,
        int $ownerId,
        SyncDataService $syncDataService,
        SerializerInterface $serializer,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $paymentGate = $owner->getGate();

        if ($paymentGate === null) {
            $paymentGate = new PaymentGate();
            $paymentGate->setCurrency($owner->getCurrency()->getCode())
                ->setOwner($owner);
        }

        $paymentGate = $serializer->deserialize(
            $request->getContent(),
            PaymentGate::class,
            'json',
            ['object_to_populate' => $paymentGate]
        );

        $paymentGateRepository->save($paymentGate);
        $owner->setGate($paymentGate);
        $ownerRepository->save($owner);

        return $this->json(
            $paymentGate,
            context: [
                'groups' => [
                    'gate:config'
                ],
            ]
        );
    }

    #[Route('/api/owner/{ownerId}/gate/config', methods: ["GET"])]
    public function getConfig(
        int $ownerId,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $config = $owner->getGate();

        return $this->json(
            $config,
            context: [
                'groups' => [
                    'gate:config'
                ],
            ]
        );
    }
}
