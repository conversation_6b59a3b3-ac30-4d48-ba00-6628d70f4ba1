<?php

namespace App\Repository\ExternalPayment;

use App\Entity\ExternalPayment\ExternalPayment;
use App\Entity\ExternalPayment\ExternalPaymentLog;
use App\Entity\Loyalty\Enum\TransactionType;
use App\Entity\Loyalty\TopUp;
use App\Entity\Owners;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use I2m\StandardTypes\Enum\Source;
use I2m\Payment\Enum\GateList;
use I2m\Payment\Enum\Status;

/**
 * @extends ServiceEntityRepository<ExternalPayment>
 */
class ExternalPaymentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ExternalPayment::class);
    }


    public function confirm(ExternalPayment $ep, ?string $externalId = null, ?array $additionalData = null): ExternalPayment
    {
        $ep
            ->setConfirmedTimestamp(new \DateTime())
            ->setStatus(Status::CONFIRMED)
        ;

        if ($externalId) {
            $ep->setExternalId($externalId);
        }
        $this->log($ep, "ep confirm", $additionalData);

        $this->getEntityManager()->flush();
        return $ep;
    }

    public function reject(ExternalPayment $ep, ?string $externalId = null): ExternalPayment
    {
        $ep
            ->setStatus(Status::REJECTED)
        ;

        if ($externalId) {
            $ep->setExternalId($externalId);
        }
        $this->log($ep, "ep reject");
        $this->getEntityManager()->flush();
        return $ep;
    }

    public function refund(ExternalPayment $ep, ?array $additionalData = null): ExternalPayment
    {
        $ep
            ->setStatus(Status::REFUNDED)
        ;

        if ($additionalData) {
            $ep->setAdditionalData($additionalData);
        }
        $this->log($ep, "ep refund", $additionalData);
        $this->getEntityManager()->flush();
        return $ep;
    }

    public function wait(ExternalPayment $ep, ?string $externalId = null, ?array $additionalData = null): ExternalPayment
    {
        $ep
            ->setStatus(Status::WAITING)
        ;

        if ($additionalData) {
            $ep->setAdditionalData($additionalData);
        }

        if ($externalId) {
            $ep->setExternalId($externalId);
        }
        $this->log($ep, "ep wait", $additionalData);
        $this->getEntityManager()->flush();
        return $ep;
    }

    public function log(ExternalPayment $ep, string $comment, ?array $info = null)
    {
        $log =
            (new ExternalPaymentLog())
                ->setExternalPayment($ep)
                ->setCtime(new \DateTime())
                ->setComment($comment)
                ->setAddInfo($info);

        $this->getEntityManager()->persist($log);
        $this->getEntityManager()->flush();
    }

    public function getList(
        ?Owners $owner = null,
        \DateTimeInterface $startDate = null,
        \DateTimeInterface $endDate = null,
        ?array $statusArray = null,
        ?array $typeArray = null,
        int $page = 1,
        int $perPage = 50,
        ?string $search = null,
    ): array {
        $baseQuery = $this->createQueryBuilder('ep');

        if ($typeArray) {
            $baseQuery->leftJoin('ep.gate', 'ga');
        }

        if ($search) {
            $baseQuery->leftJoin('ep.user', 'usr');
        }

        if ($startDate) {
            $baseQuery
                ->andWhere('ep.initiatedTimestamp >= :start_date')
                ->setParameter('start_date', $startDate);
        }

        if ($endDate) {
            $baseQuery
                ->andWhere('ep.initiatedTimestamp <= :end_date')
                ->setParameter('end_date', $endDate);
        }

        if ($statusArray) {
            $baseQuery
                ->andWhere('ep.status IN (:statuses)')
                ->setParameter('statuses', $statusArray);
        }

        if ($typeArray) {
            $baseQuery
                ->andWhere('ga.type IN (:type)')
                ->setParameter('type', $typeArray);
        }

        if ($owner) {
            $baseQuery
                ->andWhere('ep.owner IN (:owner)')
                ->setParameter('owner', $owner);
        }

        if ($search) {
            $orX = $baseQuery->expr()->orX();

            $orX->add($baseQuery->expr()->like('LOWER(usr.email)', ':search'));
            $orX->add($baseQuery->expr()->like('LOWER(ep.externalId)', ':search'));

            $baseQuery->andWhere($orX)
                ->setParameter('search', '%' . strtolower($search) . '%');
        }


        $countQuery = clone $baseQuery;
        $countQuery->select("COUNT(ep.id) as count, SUM(ep.value) as sum");
        $summary = $countQuery->getQuery()->getScalarResult();

        $baseQuery->orderBy('ep.id', 'DESC');
        $baseQuery->setFirstResult(($page - 1) * $perPage)
            ->setMaxResults($perPage);

        return [
            'data' => $baseQuery->getQuery()->getResult(),
            'total' => $summary[0]['count'],
            'sum' => (int) $summary[0]['sum'],
            'status' => Status::cases(),
            'type' => GateList::cases(),
        ];
    }

    public function save(ExternalPayment $ep): ExternalPayment
    {
        $this->getEntityManager()->persist($ep);
        $this->getEntityManager()->flush();
        return $ep;
    }
}
