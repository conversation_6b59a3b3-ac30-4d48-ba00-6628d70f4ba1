<?php

namespace App\Repository\Loyalty;

use App\Entity\Currency;
use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\Clients;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Loyalty\Enum\TopUpStatus;
use App\Entity\Loyalty\Enum\TopUpType;
use App\Entity\Loyalty\TopUp;
use App\Entity\Owners;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use I2m\StandardTypes\Enum\Source;

/**
 * @method TopUp|null find($id, $lockMode = null, $lockVersion = null)
 * @method TopUp|null findOneBy(array $criteria, array $orderBy = null)
 * @method TopUp[]    findAll()
 * @method TopUp[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TopUpRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TopUp::class);
    }

    public function getTotalTopUpCredits(Cards $card): float
    {
        $result = $this->createQueryBuilder('t')
                ->where('t.card = :card')
                ->setParameter('card', $card)
                ->select('SUM(t.value)')
                ->getQuery()
                ->getSingleScalarResult();


        return $result ?: 0;
    }

    public function getTotalTopUp(Cards $card): float
    {
        return $this->getTotalTopUpCredits($card) * $card->getCurrency()->getRate();
    }

    public function accountTopUp(Cards $card, float $value)
    {
        $activeTopUp = $this->createQueryBuilder('t')
            ->where('t.card = :card and (t.value) > 0')
            ->setParameter('card', $card);


        $topUps =  $activeTopUp
            ->orderBy('t.id', 'ASC')
            ->getQuery()
            ->getResult();


        foreach ($topUps as $curTopUp) {
            if (!$value) {
                break;
            }
            /** @var TopUp $curTopUp */
            $curValue = min($curTopUp->getValueInCredit(), $value);
            $curTopUp->setValueInCredit($curTopUp->getValueInCredit() - $curValue);
            $value -= $curValue;
        }
        $this->getEntityManager()->flush();
        return $value;
    }

    public function getStats(
        ?Cards $card,
        Owners $owner,
        ?\DateTimeInterface $from = null,
        ?\DateTimeInterface $to = null
    ): array {
        $currency = $card?->getCurrency() ?? $owner->getCurrency();

        $query = $this->createQueryBuilder('t')
            ->leftJoin('t.card', "crd")
            ->groupBy('t.source, t.type')
            ->setParameter('rate', $currency->getRate())
            ;
        $query
            ->andWhere('t.owner = :owner')
            ->setParameter('owner', $owner);

        if ($card) {
            $query
                ->andWhere('t.card = :card')
                ->setParameter('card', $card);
        }



        if (isset($from, $to)) {
            $query->andWhere('t.ctime >= :dateFrom')
                ->andWhere('t.ctime <= :dateTo')
                ->setParameter('dateFrom', $from)
                ->setParameter('dateTo', $to);
        }

        $query->select(
            't.source,t.type, 
            SUM(t.canceled) * :rate as canceled, 
            SUM(t.value) * :rate as value, 
            SUM(t.topUpValue) * :rate as topUpValue',
        );
        $result = $query->getQuery()->getResult();


        $data = [
            'ADDITION' => [
                "INTERNET" => 0,
                "MONEY_CHANGER" => 0,
            ],
            'PROMOTION' => [
                "INTERNET" => 0,
                "MONEY_CHANGER" => 0,
            ]
        ];
        foreach ($result as $item) {
            $type = $item['type']->value;
            $source = $item['source']->value;

            if (
                array_key_exists($type, $data) &&
                array_key_exists($source, $data[$type])
            ) {
                    $data[$type][$source] = $item['topUpValue'] - $item['canceled'];
            }
        }

        return $data;
    }

    public function getHistory(
        ?Owners $owner = null,
        ?Cards $card = null,
        ?\DateTimeInterface $date_from = null,
        ?\DateTimeInterface $date_to = null,
        ?int $page = 1,
        ?int $perPage = 10,
        ?array $types = null,
        ?array $sources = null,
        ?array $statuses = null,
        ?string $orderBy = null,
        ?string $orderDir = null,
        ?string $search = null,
        ?bool $hasInvoice = null,
        ?Clients $client = null,
        ?bool $showTotal = null,
        bool $toInvoice2 = false // exprymentalne
    ) {

        $queryBuilder = $this->createQueryBuilder('t')
            ->leftJoin('t.card', 'card')
            ->leftJoin('t.carwash', 'carwash')
            ->leftJoin('card.client', 'client')
            ->addSelect('card')
            ->addSelect('client')
            ->addSelect('carwash')
        ;
        $owner = $owner ?? $card?->getOwner();
        if ($owner) {
            $queryBuilder
                ->andWhere('t.owner = :owner')
                ->setParameter('owner', $owner);
        }

        if ($card) {
            $queryBuilder
                ->andWhere('t.card = :card')
                ->setParameter('card', $card);
        }

        if ($client) {
            $queryBuilder
                ->andWhere('card.client = :client')
                ->setParameter('client', $client);
        }

        if ($date_from !== null) {
            $queryBuilder->andWhere('t.ctime >= :date_from')
                ->setParameter('date_from', $date_from);
        }
        if ($date_to !== null) {
            $queryBuilder->andWhere('t.ctime <= :date_to')
                ->setParameter('date_to', $date_to);
        }

        if ($types) {
            $queryBuilder
                ->andWhere('t.type IN (:types)')
                ->setParameter('types', $types);
        }

        if ($sources) {
            $queryBuilder
                ->andWhere('t.source IN (:sources)')
                ->setParameter('sources', $sources);
        }

        if ($statuses) {
            $orStatus = $queryBuilder->expr()->orX();

            if (in_array('NOT_FULLY_REFILLED', $statuses)) {
                $orStatus->add(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->gt("t.value", 0),
                        $queryBuilder->expr()->not(
                            $queryBuilder->expr()->eq("t.value - t.topUpValue - t.canceled", 0)
                        )
                    )
                );
            }
            if (in_array('REFILLED', $statuses)) {
                $orStatus->add($queryBuilder->expr()->eq("t.value", 0));
            }

            if (in_array('WAITING', $statuses)) {
                $orStatus->add($queryBuilder->expr()->eq("t.value - t.topUpValue", 0));
            }

            if (in_array('CANCELED', $statuses)) {
                $orStatus->add($queryBuilder->expr()->gt("t.canceled", 0));
            }

            $queryBuilder->andWhere($orStatus);
        }

//        $queryBuilder
//            ->andWhere('card.status != :cardStatus')
//            ->setParameter('cardStatus', CardStatus::DELETED);

        if (!is_null($hasInvoice)) {
            $hasInvoice ?
                $queryBuilder->andWhere('t.invoice2 is not null') :
                $queryBuilder->andWhere('t.invoice2 is null');
        }
        if ($toInvoice2) {
                $queryBuilder->andWhere('t.invoice2 is null');
        }

        if ($search) {
            $orX = $queryBuilder->expr()->orX();

            $orX->add($queryBuilder->expr()->like('LOWER(card.number)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(card.email)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(card.alias)', ':search'));

            $queryBuilder->andWhere($orX)
                ->setParameter('search', '%' . strtolower($search) . '%');
        }

        // zapytanie o całkowitą ilość rekordów jest bardzo długie
        // więc dajemy możliwość wyłączenia
        if ($showTotal) {
            $countQueryBuilder = clone $queryBuilder;
            $total = $countQueryBuilder->select('COUNT(t)')->getQuery()->getSingleScalarResult();
        } else {
            $total = null;
        }

        if ($orderBy) {
            $queryBuilder->orderBy("t.$orderBy", $orderDir);
        }


        if ($page && $perPage) {
            $queryBuilder->setFirstResult(($page - 1) * $perPage)
                ->setMaxResults($perPage);
            ;
        }


        return [
            'data' => $queryBuilder->getQuery()->getResult(),
            'total' => $total,
            'type' => TopUpType::cases(),
            'source' => [
                Source::MONEY_CHANGER,
                Source::INTERNET
            ]
        ];
    }

    public function deleteTopUps(Cards $card): array
    {
        $activeTopUp = $this->createQueryBuilder('t')
            ->where('t.card = :card and (t.value) > 0')
            ->setParameter('card', $card);


        $topUps =  $activeTopUp
            ->orderBy('t.id', 'ASC')
            ->getQuery()
            ->getResult();

        $sum = 0;
        foreach ($topUps as $curTopUp) {
            /** @var TopUp $curTopUp */
            $sum += $curTopUp->getValueInCredit();
            $curTopUp->setCanceledInCredit($curTopUp->getValueInCredit());
            $curTopUp->setValueInCredit(0);
            $curTopUp->setStatus(TopUpStatus::CANCELED);
        }
        $this->getEntityManager()->flush();
        return [
            "canceled_value" => $sum * ($card->getCurrency()?->getRate() ?? 1),
            "currency" => $card->getCurrency()
            ]
            ;
    }

    public function save(TopUp $topUp)
    {
        $this->getEntityManager()->persist($topUp);
        $this->getEntityManager()->flush();
    }

    public function getByToken(string $token, TopUpStatus $status = TopUpStatus::ACTIVE): ?TopUp
    {
        $tokenArray = explode('-', $token);
        $id = hexdec($tokenArray[1]);
        $password = $tokenArray[2];

        return $this->findOneBy(
            [
                'id' => $id,
                'token' => $password,
                'status' => $status
            ]
        );
    }

    /**
     * @return TopUp[]
     */
    public function getForInvoicing(
        \DateTimeInterface $date_from,
        \DateTimeInterface $date_to,
        Clients $client
    ): array {
        $queryBuilder = $this->createQueryBuilder('t')
            ->leftJoin('t.card', 'card')
            ->leftJoin('t.carwash', 'carwash')
            ->leftJoin('card.client', 'client')
            ->addSelect('card')
            ->addSelect('client')
            ->addSelect('carwash')
        ;


        $queryBuilder
            ->andWhere('card.client = :client')
            ->setParameter('client', $client)

            ->andWhere('t.ctime >= :date_from')
            ->setParameter('date_from', $date_from)

            ->andWhere('t.ctime <= :date_to')
            ->setParameter('date_to', $date_to)

            ->andWhere('t.status <= :status')
            ->setParameter('status', TopUpStatus::ACTIVE)

            ->andWhere('t.type <= :type')
            ->setParameter('type', TopUpType::ADDITION)

            ->andWhere('t.invoice2 is null')
        ;


        return $queryBuilder->getQuery()->getResult();
    }

    public function addTopUp(
        float $value,
        Currency $currency,
        TopUpType $type,
        Source $source,
        ?Owners $owner = null,
        ?Cards $card = null,
        ?string $comment = null
    ) {
        $topUp = (new TopUp())
            ->setCard($card)
            ->setOwner($owner)
            ->setCurrency($currency) // musi być przed value!!!
            ->setType($type)
            ->setSource($source)
            ->setTopUpValue($value)
            ->setValue($value)
            ->setStatus($owner ? TopUpStatus::ACTIVE : TopUpStatus::INITIATED)
            ->setAddedBy($comment);

        $this->save($topUp);
        return $topUp;
    }
}
