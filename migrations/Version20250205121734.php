<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250205121734 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE license_plate (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, number VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, INDEX IDX_F5AA79D0A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE license_plate ADD CONSTRAINT FK_F5AA79D0A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE cards ADD description LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE clients ADD description LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE top_up ADD description LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE invoice ADD send_date DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE owners ADD description LONGTEXT DEFAULT NULL, ADD term_of_use LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {

    }
}
