<?php

namespace App\Service\Report\Data;

use App\Entity\Owners;
use I2m\StandardTypes\Enum\CMSubscription;
use I2m\Reports\Interface\ReportDataInterface;
use Symfony\Bundle\FrameworkBundle\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;

abstract class ReportDataAbstract implements ReportDataInterface
{
    private Owners $owner;
    private array $criteria;

    public function __construct(
        private TranslatorInterface $translator,
    ) {
    }

    public function setConfig(array $criteria, Owners $owner): ReportDataAbstract
    {
        $this->owner = $owner;
        $this->criteria = $criteria;
        return $this;
    }

    public function getTwigTemplate(): string
    {
        return 'Reports/default.html.twig';
    }

    public function getCriteria(): array
    {
        return $this->criteria;
    }

    public function getOwner(): Owners
    {
        return $this->owner;
    }

    public function getDateCriteria(string $name, ?\DateTimeZone $timezone = null): ?\DateTime
    {
        return $this->getCriteria()[$name] ?? null ? new \DateTime($this->getCriteria()[$name], $timezone) : null;
    }

    protected function trans($nameOrg)
    {
        $lang = $this->getOwner()->getConfig()->getLanguage();
        /** @var Translator $translator */
        $translator = $this->translator;
        $translator->setLocale($lang->value);
        return $translator->trans($nameOrg);
    }

    protected function checkPermision(CMSubscription $subscription): bool
    {
        if (!$this->getOwner()->getSubscription()->isGreaterEqualThan($subscription)) {
            return false;
        }

        return true;
    }

    public function getHeader(): array
    {
        if ($this->getCriteria()['dateFrom'] ?? null) {
            return [
                $this->translator->trans("table.date"),
                $this->getCriteria()['dateFrom'],
                ' - ',
                $this->getCriteria()['dateTo'],
            ];
        }

        return [];
    }

    public function getInfo(): array
    {
        return
            [
                'owner' => $this->getOwner()->getConfig(),
                'criterias' => $this->getCriteria(),
            ];
    }

    public function getPdfOptions(): array
    {
        return [];
        //return [
        //    'orientation' => 'Landscape',
        //    'page-size' => 'A4',
        //    'margin-top' => '10mm',
        //    'margin-bottom' => '10mm',
        //    'margin-left' => '10mm',
        //    'margin-right' => '10mm'
        //];
    }
}
