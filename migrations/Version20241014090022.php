<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241014090022 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE invoice CHANGE total_gross total_gross DOUBLE PRECISION DEFAULT NULL, CHANGE total_net total_net DOUBLE PRECISION DEFAULT NULL, CHANGE total_tax total_tax DOUBLE PRECISION DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE transactions DROP FOREIGN KEY FK_EAA81A4CA76ED395');
        $this->addSql('ALTER TABLE invoice CHANGE total_gross total_gross DOUBLE PRECISION NOT NULL, CHANGE total_net total_net DOUBLE PRECISION NOT NULL, CHANGE total_tax total_tax DOUBLE PRECISION NOT NULL');
    }
}
