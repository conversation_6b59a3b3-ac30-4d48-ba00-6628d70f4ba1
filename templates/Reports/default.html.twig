<!DOCTYPE html>
<html lang="pl">
    <head>
        <meta charset="UTF-8" />
        {% include 'Reports/report.styles.html.twig' %}
    </head>
    <body>
        <div class="row">
            <div class="col s9 align-left">
                <table class="table-borderless table-subscriber">
                    <tr>
                        <td><div class="bold-2">{{ 'reports_client_cards_end_time'|trans }}:</div></td><td>{{ "now"|date('Y-m-d H:i', info.owner.timezone.value) }}</td>
                    </tr>
                    {% if info.criterias.dateFrom is not null and info.criterias.dateTo is not null  %}
                        <tr>
                            <td><div class="bold-2">{{ 'common_inPeriod'|trans }}:</div></td><td>{{ info.criterias.dateFrom|date('Y-m-d', info.owner.timezone.value) }} - {{ info.criterias.dateTo|date('Y-m-d', info.owner.timezone.value) }}</td>
                        </tr>
                    {% endif %}
                    <tr>
                        <td><div class="bold-2">{{ 'reports_client_cards_company_name'|trans }}:</div></td><td>{{ info.owner.name }}</td>
                    </tr>
                    <tr>
                        <td><div class="bold-2">{{ 'reports_client_cards_company_nip'|trans }}:</div></td><td>{{ info.owner.taxNumber }}</td>
                    </tr>
                    <tr>
                        <td><div class="bold-2">{{ 'reports_client_cards_company_address'|trans }}:</div></td><td>{{ info.owner.city }} {{ info.owner.address }}</td>
                    </tr>
                </table>
            </div>
            <div class="col s3">
                <div class="row right">
                    <img src="{{ projectDir ~ '/public/assets/images/logo2.png' }}" class="thumbnail-200" />
                </div>
                <div class="row right">
                    <img src="data:image/png;base64,{{ info.owner.logo }}" class="thumbnail-200" />
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <h2 class="bold">{{ reportName|trans }}</h2>
            </div>
        </div>
        {% for table in tables %}
            <div style="page-break-inside:avoid">
                <h3 class="bold">{{ table.name }}</h3>
                <div class="bold">{{ table.description }}</div>
                <div class="row">
                    <div class="col s12">
                        <div id="pdf-turnover-table" class="pdf-table">
                            <table class="display table no-footer pdf-table">
                                <thead>
                                    <tr class="table-header">
                                        {% for column in table.columns  %}
                                            <th class="{{ column.class }}">{{ column.name|trans }}</th>
                                        {% endfor %}
                                    </tr>
                                </thead>
                                <tbody>
                                {% for item in table.items %}
                                    <tr>
                                        {% for column in table.columns  %}
                                            {% if item[column.key] is not null %}
                                                <td class="{{ column.class }}">
                                                    {% if column.numberFormat is not null %}
                                                        {{ item[column.key] | number_format(column.numberFormat) }}
                                                    {% else %}
                                                        {{ item[column.key] }}
                                                    {% endif %}
                                                    {{ column.unit }}
                                                </td>
                                            {% else %}
                                                <td class="{{ column.class }}"> - {{ column.unit }}</td>
                                            {% endif %}
                                        {% endfor %}
                                    </tr>
                                {% endfor %}
                                {% if table.summary is null %}
                                {% else %}
                                    <tr class="table-header">
                                        {% for column in table.columns  %}
                                            {% if  table.summary[column.key] is not null %}
                                                <th class="{{ column.class }}">
                                                    {% if column.numberFormat is not null %}
                                                        {{ table.summary[column.key] | number_format(column.numberFormat) }}
                                                    {% else %}
                                                        {{ table.summary[column.key] }}
                                                    {% endif %}
                                                    {{ column.unit }}
                                                </th>
                                            {% else %}
                                                <th class="{{ column.class }}"></th>
                                            {% endif %}
                                        {% endfor %}
                                    </tr>
                                {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </body>
</html>
