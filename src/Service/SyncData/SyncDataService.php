<?php

namespace App\Service\SyncData;

use App\Entity\Carwash;
use App\Entity\Carwash\EdgeDevice;
use App\Entity\Currency;
use App\Entity\Owners;
use App\Repository\Carwash\EdgeDeviceRepository;
use App\Repository\CarwashRepository;
use App\Repository\CurrencyRepository;
use App\Repository\OwnerRepository;
use I2m\Connectors\Service\CarwashApi\CarwashApiCarwashService;
use I2m\Connectors\Service\CarwashApi\CarwashApiOwnerService;
use I2m\Connectors\Model\CarwashApi as CwApi;
use I2m\Connectors\Service\CMApi\CMApi;
use I2m\StandardTypes\Enum\EdgeDeviceType;

class SyncDataService
{
    public function __construct(
        private CarwashRepository $carwashRepository,
        private CarwashApiCarwashService $carwashApiCarwashService,
        private CarwashApiOwnerService $carwashApiOwnerService,
        private OwnerRepository $ownerRepository,
        private CurrencyRepository $currencyRepository,
        private EdgeDeviceRepository $edgeDeviceRepository,
        private CMApi $CMApi
    ) {
    }

    public function getCarwash(int $sn): ?Carwash
    {
        // szukam myjni w bazie
        $carwash = $this->carwashRepository->findOneBy(
            ['sn' => $sn]
        );

        if ($carwash) {
            return $carwash;
        }

        // to może jest ta myjnie w cw-api
        $cwApiCarwash = $this->carwashApiCarwashService->getList([$sn])->getData()[0] ?? null;


        if ($cwApiCarwash) {
            $carwash = (new Carwash())
                ->setId($cwApiCarwash->getId())
                ->setSn($cwApiCarwash->getSn())
                ->setOwner($this->getOwner($cwApiCarwash->getOwner()?->getId()));
            $this->carwashRepository->save($carwash);
            $this->getEdgeDevice($cwApiCarwash, $carwash);
            return $carwash;
        }
        return null;
    }

    public function getOwner(?int $id)
    {
        if (is_null($id)) {
            return null;
        }

        $owner = $this->ownerRepository->find($id);

        if ($owner) {
            return $owner;
        }
        try {
            $cwOwner = $this->carwashApiOwnerService->getItem($id);
        } catch (\Exception $e) {
            return null;
        }

        $cmOwner = $this->CMApi->getSubscriberByOwner($id);
        if (is_null($cmOwner)) {
            return null;
        }
        $currency = $this->getCurrency($cmOwner->getCurrency());
        if (is_null($currency)) {
            return null;
        }

        $owner = (new Owners())
            ->setId($id)
            ->setName($cmOwner->getName())
            ->setCurrency($currency)
            ->setSubscription($cmOwner->getSubscription())

        ;
        $owner = $this->ownerRepository->save($owner);
        return $owner;
    }

    public function getCurrency(?\I2m\Connectors\Model\CMApi\Currency $cwCurrency): ?Currency
    {
        if (is_null($cwCurrency)) {
            return null;
        }
        $currency = $this->currencyRepository->getCurrency($cwCurrency->getCode());
        if ($currency) {
            return $currency;
        }

        return null;
    }

    private function getEdgeDevice(CwApi\Carwash $device, Carwash $carwash): ?EdgeDevice
    {
        $plcSn = $device->getPlcSn();
        if (is_null($plcSn)) {
            return null;
        }

        $edgeDevice =
            $this->edgeDeviceRepository->create(EdgeDeviceType::PLC, $plcSn);

        $edgeDevice
            ->setMac($device->getPlcMac())
            ->setCarwash($carwash)
        ;
        $this->edgeDeviceRepository->save($edgeDevice);

        return $edgeDevice;
    }
}
