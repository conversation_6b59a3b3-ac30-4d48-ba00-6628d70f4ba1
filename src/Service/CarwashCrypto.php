<?php

namespace App\Service;

use App\Entity\Carwash;

class CarwashCrypto extends \I2m\IIot\Tools\CarwashCrypto
{
    public static function createKey2(Carwash\EdgeDevice $device, string $digest): string
    {
        return parent::createKey(
            $device->getCarwash()->getSn(),
            $device->getUid(),
            $device->getMac(),
            $digest
        );
    }
    public static function decrypt(Carwash\EdgeDevice $device, string $data, string $digest)
    {
        $key = self::createKey2($device, $digest);

        return rtrim(self::aes128ecbDecrypt($key, $data));
    }

    public static function encrypt(Carwash\EdgeDevice $device, string $data, string $digest)
    {
        $key = self::createKey2($device, $digest);

        return self::aes128ecbEncrypt($key, $data);
    }


    public static function digest(Carwash\EdgeDevice $device, string $body, string $digest)
    {
        $key = self::createKey2($device, $digest);
        return strtoupper(hash_hmac(
            'md5',
            $body,
            hex2bin($key)
        ));
    }
}
