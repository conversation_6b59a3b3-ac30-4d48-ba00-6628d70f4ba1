<?php

namespace App\Command;

use App\Repository\Loyalty\ClientsRepository;
use App\Service\Loyalty\CardReportEmailService;
use App\Service\Report\Data\LoyaltyClientUsageReport;
use App\Service\Report\ReportService;
use I2m\Reports\Enum\FileExtention;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

use function Sentry\captureMessage;

#[AsCommand(
    name: 'loyalty:report:clients',
    description: 'Add a short description for your command',
)]
class ClientReportCommand extends Command
{
    public function __construct(
        private ClientsRepository $clientsRepository,
        private ReportService $reportService,
        private CardReportEmailService $cardEmailService,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('arg1', InputArgument::OPTIONAL, 'Argument description')
            ->addOption('option1', null, InputOption::VALUE_NONE, 'Option description')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $periodStart = (new \DateTimeImmutable("first day of previous month"))->setTime(0, 0, 0);
        $periodEnd =  (new \DateTimeImmutable("last day of previous month"))->setTime(23, 59, 59);

        $clients = $this->clientsRepository->getForReport();

        foreach ($clients as $client) {
            $io->note("Owner {$client->getOwner()->getName()}, Client: {$client->getName()}");
            $criteria = [
                'dateFrom' => $periodStart->format('Y-m-d'),
                'dateTo' => $periodEnd->format('Y-m-d'),
                'clientId' => $client->getId(),
            ];

            if (empty($client->getEmail())) {
                captureMessage(
                    "Klient {$client->getId()} nie posiada adresu email. Nie generuję raportu o kartach w komendzie loyalty:report:clients"
                );
                continue;
            }

            $path = $this->reportService->getFile(
                LoyaltyClientUsageReport::class,
                $criteria,
                $client->getOwner(),
                FileExtention::PDF
            );

            $this->cardEmailService->sendEmail(
                $path,
                $client,
                $criteria
            );
        }

        $io->success('Success');

        return Command::SUCCESS;
    }
}
