<?php

namespace App\Tests\Socket;

use PHPUnit\Framework\TestCase;
use App\Service\CarwashCrypto;
use App\Entity\Carwash;

class CarwashCryptoTest extends TestCase {

    public function provideReqestData() {
        return [
            [
                'sn' => 33703,
                'plc' => 'CE530170037',
                'mac' => '00:60:65:60:B1:96',
                'encrypted' => '832F2EBBEE1F485E144DF899B54CFF73BF350BA99AFA4849C7E8A6641AA0CB500BAF006C3C9EC042E27963A312F67EBE7C25C3632D79051A53C84FA1120A0CD601583C0110AF799A4A92CBFBCDDF83D807B2C18D5906B122EABA0A048ACBFD8F2200EBCA933A31D101A37A3F6075B20CC2FE8735BEBA596CF2EF9247F781A3AE',
                'decrypted' => '{"g_i2m_key[0]":{"iTimeStamp":**********,"iSn":**********,"iBay":2,"rVend":-11.8,"rCredit":37,"iType":0}}',
            ],
            [
                'sn' => 141,
                'plc' => 'CE530168729',
                'mac' => '00:60:65:1F:01:1B',
                'encrypted' => '55E459BE1CE18772E3222195BD111ADA57AD6CDDBA200254326B21AAF38BED455BBC67E5450D410D730BD332BA7ED2491FE833007A21F2F2A4969005E52D5BE08E4768B22654B44D295B8D5DC83D870DFB4A9C6686C0C605D08C492EE4F9FCF1',
                'decrypted' => '{"g_i2m_ala[0]":{"iTimeStamp":**********,"iGrp":8,"iId":4,"bAck":1,"bAlarm":1}}',
            ]
        ];
    }

    /**
     * @test
     * @dataProvider provideReqestData
     */
    public function TestEncrypt($sn, $plc, $mac, $encrypted, $decrypted) {
        $carwash = (new Carwash())
                ->setSn($sn);

        $edgeDevice = (new Carwash\EdgeDevice())
            ->setUid($plc)
            ->setMac($mac)
            ->setCarwash($carwash)
        ;

        $crypto = new CarwashCrypto();
        $result = $crypto->decrypt($edgeDevice, $encrypted, CarwashCrypto::PASS_SOCKET);

        $this->assertEquals($decrypted, $result);
    }

    /**
     * @test
     * @dataProvider provideReqestData
     */
    public function TestDecrypt($sn, $plc, $mac, $encrypted, $decrypted) {

        $carwash = (new Carwash())
            ->setSn($sn);

        $edgeDevice = (new Carwash\EdgeDevice())
            ->setUid($plc)
            ->setMac($mac)
            ->setCarwash($carwash)
        ;
        $crypto = new CarwashCrypto();

        $result = $crypto->encrypt($edgeDevice, $decrypted, CarwashCrypto::PASS_SOCKET);
        $digest = $crypto->digest($edgeDevice, $decrypted, CarwashCrypto::PASS_SOCKET);
        $this->assertEquals($encrypted, $result);
    }

    /**
     * @test
     */
    public function TestDigest() {

        $carwash = (new Carwash())
            ->setSn(14511);

        $edgeDevice = (new Carwash\EdgeDevice())
            ->setUid('CE530169197')
            ->setMac('00:60:65:31:C3:66')
            ->setCarwash($carwash)
        ;

        $body = '356B5EC61E853902423A2F00D833A2A769085371C655F2985EBDCE7FB317CD7'
                . 'FA4BC609ABE31D2969669870CE0294CA7E36B909FE320845CDA141BA57B3E'
                . 'F1CDBBA966AC14C60879162B89863E7FF866CBDC780B60A3767C1EAE8729F'
                . '7919FF95F93293FA3FAE0884FDB5CA57772AA2F6508A84F0D988AFAADCFD6'
                . 'FA01F4CC831C1A67E0805FBD918B6FB5AD44B0CC8E8FE6C4540B0BE8D5400'
                . '09750A89515486F0DEB97E6899554CBA24E69DF99A7902DD8DE9605A4B47C'
                . '622526A3502EF4E7F66C0C9CB58FE2FE9D3AE28CA38BD483A196368F58739'
                . '54F95DCE63084A33F4E178340185DAFBFE3315A3B7445AF91654901E79380'
                . 'C2B467FDC05D86F1AA2293B5F14322B28953416E7D89CA7C19EF73F3017CD'
                . 'C09A514F94B4E0DF719E0BD14A3E938BCF65128FF17EE4EFAE25A301EE715'
                . '5BAA5DF07113B4F2D5410537FDFB3D022CB76221B7783745F51AD18573553'
                . '0452E1351A0B2A7A4814F07778BDDAF4200175181CE231B1A22E2AF01F98F'
                . '8F1029664BF1A4561A548F46AB0618D2E210544CF4E4263A292D2989129CA'
                . '28D41A692213E7969E12F4B9B7B70B9A238B6C7D554DE6632F308BA2C545A'
                . 'E0EE7591D939AEA63B7A60E54C8DF01ED7BDEFC47FC7333ACDA3D9FE30B56'
                . '6DA7ED2A7187E8CD16B9947B7210DD7B531ADAE10DF2EC3017C362EC62120'
                . 'FACCCE5F850BFEB3EBDEFA57B66268F77309BD11FAAE6875FDC9DB64BC831'
                . 'BB18B5FEACC2A2827B6CEDAE518B08DB859514B22757390868BE18177E4B0'
                . '4270B72263B87A14D41640215B127B3A6EB567ED0C43CCE495469E75FF500'
                . 'AD17F73512A57E73670F52730BA806BDF5CA9B337D122FB2420E13A5EB503'
                . '230F92229F2CD77C969EC18FC524FB5EE15DAD632AF53DE170FC50EA3A43D'
                . 'F3EEAD1A996E59B28B73C00E9DDE67E09320EB47DEC157D7EE9F0FAE061F7'
                . 'C7D04A27F56F5D3626DE755AD9B1A56F20FCD7292153C3C9F2D0E5B9E0954'
                . 'FF4C8DA5956F76078302F8256E285619C14E624E14F080F9E940F83B0A71D'
                . '4ED529CB3449B184C0033F6B4D298F27EA8ACE9B634A4E2815E97D83A7A8B'
                . 'CE118B0F01814B5CDEB6A526ADF8D352678B1581AC4F4681405F450BFF967'
                . '306C445067BAA70521561FEB5DC4DE8A70D4E93145FB00216C2F48C379E43'
                . '92F0C41110F4F95E591E90AAAC5E1210D6F2605186725E66071514643CD50'
                . 'B495DEB1328EE8374CDCCBAE69E6A712C0C1EE83FB6178315757C8B7E1201'
                . '0A9583F372A705B035E0E30AA5367612B07F167C94D61D967CBA99E3930FB'
                . '7D3BC50D28F489A13E381A16F1D71E98482B0F9B20C09D107B8F536BA90AB'
                . '589523FD610B5A57B5E0D5738E0DE8C5969EC677E5740D2DD47AE9B236440'
                . '215B127B3A6EB567ED0C43CCE49546A8A99F58083F0CE47AB50C1432B3F19'
                . '6B8FB07BA5E70546BC8A6598A0303DAB538BF1F905AAA12CE59A0AC99FC4F'
                . 'BB2CF9E01F2989BF707B68DCFD9C45463806602F2D4C690BDFE032DA5828B'
                . '87E6C9DA28963A3C0EC5F3F930BF213446FC600929C73D899F71020FD5271'
                . '1F5F6E17A3A999B81959DB05DE04EEC8DCBF06971D477E466D266FA7C5F69'
                . 'D0D2B91BB932374B4DF0763EC52878C26FDC96F0097D6193EA3D67BC68529'
                . '16C937696CEE2BF7813D2FFC9A80C796D4B24B5CDF79F3BE3FB7C55321396'
                . 'F4A6EA46C09FD90D2FF592A3C02BAD4A47A8735DD561F64D3CE5EB503230F'
                . '92229F2CD77C969EC18FC585A2B73CE87A3D59DE93F654BDAC9E83F905769'
                . 'D6EA248584CB4D4ED578C3CA6E427803ED36BD4D82A0DDB84E6EC498582CC'
                . '5DCBDFA5675E764BE35A16A41716A999B81959DB05DE04EEC8DCBF06971D4'
                . '77E466D266FA7C5F69D0D2B91BB932374B4DF0763EC52878C26FDC96F0097'
                . 'D67D08DC0F0F8743D223B08FF7A7EC897D9688157BD0F87B8589FA63D216D'
                . '498D402724504C132584828E74C47FB35FC0263E5DB2E12060411754438FF'
                . '129DCD77B903ACCB1022CC1829181D696DACB8BF8F49FFD4E3B7DE2160EDC'
                . '2DBD86131CC4F556732341AD6E74753241B2F25A5F63FDA1ECB1F77585FA1'
                . '742159F40A27073EEDB61A6771B60A623B317AA1AFBF2D2A0483E15984FBA'
                . '8809C1F144F0EC2922815A9ACE0B73C4B390DEAEA48D98CFDE993608FEA7F'
                . '64775970260D1D993F7203596052B64551F2E881924A0ACA830FAC72F3178'
                . '28E4933D80375FA865F842BFBAD0DE37D844935A25CFB8F2B3CE5AC77E8BC'
                . 'CB63F311AD00F1C6072B533390F6FCA7C11BA6FD2D57A5D6978883524BCFD'
                . '8E39AD5C9577319E43B4AC59FBCD6A86C9B2460EAE74C25699E875B21371F'
                . '18CB9FA97DB574FF30EBEAA91F1D14487C9FECC09E99352630034F752658F'
                . 'C33887B3CE29083D6562C8E1FBAE60AD2E977177471F05ED71D2B97FE257E'
                . 'A92176A2D91566E557FF15961B2767D697E5FAACA2A390BD027500937FA08'
                . '16E60AFA611C66519579835C92DBDE4085BC7B1166AF5AB3781A1A6E0F715'
                . '43A44F478F4A8CD594BD25875FED197D922F0FCFB604E97EF353CC160C3BD'
                . '2FABD48472AA0EF569127EEA4AAC3732FA5DF85FC613A26B1CAFD82938BC4'
                . '7002A79D66A518FE07283A3445EE206C87CFDC4D6F5B3D2B5D8FE173D035F'
                . '5BFEA1A5EA50BDC3E799A249C7D844479AA45E571B96BA79FD9625F2900CF'
                . '4101128455D51554417EF3BADD4D100711B88F93D28266821CD51875C5D9A'
                . '8983B822A8F70B3F1B77F7A383A237BA5B0D6528C8123D2B147B6D7117462'
                . 'B24D6042594B8B3CAE7D2153D6E3E5834779A9D4FEAE5B59BDE1FE435C394'
                . '6C4CAB7AD8B520906D18308509189AC4C5913601913A6F4BA02548CDD57FF'
                . '47B828E6C02486E0EE0FB5FA7BD4438D8738CF0CC2229FF39B9F207D4E0AA'
                . '2624E5DABF6D4BC50022DBFD184789C996B931756A2D98EC4FD6BAA5EF351'
                . 'F6C17361E2DB74B29A7886D3E6A45A76EC5C6224196001D3EDF69BE328047'
                . '6C83F78B3C3C8361E9B7B6D9E70C8F20782EC7CECE02AD0A08F221D0AE3A2'
                . '4D17BD59461231F03D022CB76221B7783745F51AD18573559EB6CFFEFE1F3'
                . 'ADC0AA8EF3A0CEF6F0B6A72E04BEF9AA932C435A39905274CC003BBAE272C'
                . 'AA1754B07C790CBC0900EFC4F4681405F450BFF967306C445067BA76E4FBB'
                . 'BB8AE54DE0C8CB513529CEF66A28963A3C0EC5F3F930BF213446FC600CE16'
                . 'BEE1851F36DD5A707205749868F2C524E91B6F864D43EE3AC92A2C6606B0B'
                . '1303EE109EE23B5DD2991E0E8AB511B3ACF9734A44BB035979A63F41E90E1'
                . '5E6343FCACC86190E7E3DD9931B71DBA7A2734CF5B86B7715E821F1D1C3BE'
                . '9D919D0589C4DDDBC713CB9018D04F467126ED2B938EA9DC8DC6E229DA995'
                . '5D35FF4E3F1ABEB4D0DB7700D10FF00A87C21FF0156F424FB764D0A8DF62D'
                . 'D3CABA8E815C51C8006DF5E925B309C6276D449C400794C55BF98B4191E56'
                . '4BDF0836664C642D3F2B74B17C8EA027C29FD73A269F66BDA5EC2E4D49BAE'
                . '1260F0ADD1A262CFCDA80B0D28F5864C3EF80316ACB3625B57C1C47E094FE'
                . '65DB32F0EA2AE0B020CEA7B3EB0A70382A2F4B1773E99358D97A703D32643'
                . '16A8C0885D40BF264160545A692213E7969E12F4B9B7B70B9A238B6C7D554'
                . 'DE6632F308BA2C545AE0EE7591D939AEA63B7A60E54C8DF01ED7BDEFC47FC'
                . '7333ACDA3D9FE30B566DA7ED2A7187321AA4F805C11C29F119AB06859B1DF'
                . '4EFF8C637DC9993B4B543424B863CA5262A05B11E7A5003BC70EDF3B988CD'
                . '1A628776AB46EEBF724C7BD669149C34F41215ADAD846F46FDB863833CEA8'
                . '9875ADFF9EB3F96599CF73D7E783C2E2638640E0ACBB5A72414C799BC409B'
                . 'EE8EEB6D36000C1231021FFD246C2313B69A4C6CC00216C2F48C379E4392F'
                . '0C41110F4F95A15C418E41EDEEAF792D77632E818464DB74B29A7886D3E6A'
                . '45A76EC5C6224196001D3EDF69BE3280476C83F78B3C3C82AAB1B3A57F546'
                . 'D280088D24AD181548D8FEFE94CB522902B91F3DAD9FFEBBDA3FC059E5C52'
                . '78637CE914728EADFAB7CB5AE92293B2B8B5F714D4042C2F44E79B0296F51'
                . '3548036F0D0630A1E613D0B137D508DCA251CDCC13F2490532D6A97FECAC7'
                . 'FD6E298DD504976CFF326114B9FEBA977FB20703D7A7E90148630BF4D39EF'
                . 'A11DF4E3FDCF195BF6EA07540620C640215B127B3A6EB567ED0C43CCE4954'
                . '622094A1977619871403AA554E3A0B8EA02FC6AAFCDFE80782F69332E7BE3'
                . 'ABDAF66C0C9CB58FE2FE9D3AE28CA38BD483032B1BB14DA9D641DA5EA078E'
                . '0B5BB39B81281457A129C6153B686CDDFEF7922A28963A3C0EC5F3F930BF2'
                . '13446FC600929C73D899F71020FD52711F5F6E17A3A999B81959DB05DE04E'
                . 'EC8DCBF06971D5AA1E8F224ED71C33C407DCA1EBBA75D715E597B9AEE4A91'
                . 'B2E0ABD02B8C44F4815215315FE00E498035404051064CB4CCFCD239EAFA1'
                . 'A345E1B374F07929A233FB7C55321396F4A6EA46C09FD90D2FFC5B49220F4'
                . 'FDCA28BCF95FB77683D7185EB503230F92229F2CD77C969EC18FC51A5C92B'
                . '47039E2ADC89732BAB94661E6F905769D6EA248584CB4D4ED578C3CA6E427'
                . '803ED36BD4D82A0DDB84E6EC498582CC5DCBDFA5675E764BE35A16A41716A'
                . '999B81959DB05DE04EEC8DCBF06971D5AA1E8F224ED71C33C407DCA1EBBA7'
                . '5D715E597B9AEE4A91B2E0ABD02B8C44F42EC3017C362EC62120FACCCE5F8'
                . '50BFEFC725735D5016D3C47144996F048B889B002A688760D12E1DE0B695B'
                . '4D1A061E9823A75B04579A7484A77DD43D8BFAAC8AAB4592280040A81EC72'
                . 'EF02BB9533D5D306CB0CA30118B7B7A7E15625097ABA77BBB459E5608F6BA'
                . 'ED792E4784DC2963F6FE8F46689324C15F98120C9C6B292539E0A4DD83C12'
                . '932283BF88CEB6EB043B59D079CFA9D471A1E0B90CA0FE409B12032884DFD'
                . 'AA040C775C0D7288AE79D766F0FB895F1171CA38DF55D05A0E250E60F4B74'
                . '49501D97F6FB90A6CF5D510C43AB3F144D26C06B276473290A4328A6EEB8A'
                . '224187E3CBB49FE5BDB4DC814228FB3308DC1DDD47FB24E26045D9E5BBE97'
                . 'EF353CC160C3BD2FABD48472AA0EF35A692EBDDBB55BD092FDA49C303DECD'
                . '9324D551716B093F6D1C2BCC961DC441D619D59FE849E31F0C2652D529D3B'
                . '030ED112193BF2B54352E6E40D79776097F46E6739CDD6EABDF7F7E2A67A2'
                . '6A856338548C57532CB9A807C54AEFED15FE73A2D91566E557FF15961B276'
                . '7D697E5FA837EB3832E15E907BD24BF7080876E2B11C66519579835C92DBD'
                . 'E4085BC7B1166AF5AB3781A1A6E0F71543A44F478F4A8CD594BD25875FED1'
                . '97D922F0FCFB604E97EF353CC160C3BD2FABD48472AA0EF35A692EBDDBB55'
                . 'BD092FDA49C303DECD9324D551716B093F6D1C2BCC961DC4412734CF5B86B'
                . '7715E821F1D1C3BE9D919A8317F2C7CC6469F1BECE987896D2DC82BD1ACB2'
                . 'E36E2C998BD7E448D2D2E7307C373B05170F526502C3EA13A02FAE01B7AF8'
                . 'B18BEDC5BB2CFB147976B2FDF5E0737FBA2844FC7EA5ED385481E73936D37'
                . 'CABFEEA5D1843641554CF4CF117D9D40215B127B3A6EB567ED0C43CCE4954'
                . '69E75FF500AD17F73512A57E73670F52730BA806BDF5CA9B337D122FB2420'
                . 'E13A5EB503230F92229F2CD77C969EC18FC524FB5EE15DAD632AF53DE170F'
                . 'C50EA3A43DF3EEAD1A996E59B28B73C00E9DDE67E09320EB47DEC157D7EE9'
                . 'F0FAE061F7C7D04A27F56F5D3626DE755AD9B1A56F20FCD7292153C3C9F2D'
                . '0E5B9E0954FF4C8DA5956F76078302F8256E285619C14E624E14F080F9E94'
                . '0F83B0A71D4ED529CB3449B184C0033F6B4D298F27EA8ACE9B634A4E2815E'
                . '97D83A7A8BCE118B0F01814B5CDEB6A526ADF8D352678B1581AC4F4681405'
                . 'F450BFF967306C445067BAA70521561FEB5DC4DE8A70D4E93145FB00216C2'
                . 'F48C379E4392F0C41110F4F95A15C418E41EDEEAF792D77632E818464DB74'
                . 'B29A7886D3E6A45A76EC5C6224196001D3EDF69BE3280476C83F78B3C3C8E'
                . 'D22CA8D8FE18BB4A8F742B563927BF6D568E1F9AC7DBE59EDF854F984C3FA'
                . 'C50EAED13E3505A085AEDCFAD8032CA3E5388F4F5B727510BFEA9748F81BF'
                . '6B8B430415850EBC8AB50B8DA1329ABA8504E26D6A065D8DC419AB867D49E'
                . '0555E5A4FF6DF24ABCCD3BAFBE2CD62647AD825B30452E1351A0B2A7A4814'
                . 'F07778BDDAF4200175181CE231B1A22E2AF01F98F8F1029664BF1A4561A54'
                . '8F46AB0618D2E210544CF4E4263A292D2989129CA28D41A692213E7969E12'
                . 'F4B9B7B70B9A238B6C7D554DE6632F308BA2C545AE0EE7591D939AEA63B7A'
                . '60E54C8DF01ED7BDEFC47FC7333ACDA3D9FE30B566DA7ED2A7187E8CD16B9'
                . '947B7210DD7B531ADAE10DF815215315FE00E498035404051064CB4F60697'
                . 'B851869068770E632F4233440D2A0483E15984FBA8809C1F144F0EC292C7B'
                . '0C464BAF67DF9008F1199A7DE36C8A2D91566E557FF15961B2767D697E5FA'
                . '806232BCF661287CD955217033CB59D0301B4C432D41736CD77B93C60DF56'
                . '8503E0010D2CDAEEAC35553F14A120122426453B1FE36E345DCD09FDA1737'
                . 'AADD6AD90EF012BBB1F87BCA2730572EB51400269B276C11F5B821088DC45'
                . 'F6FCFB6F232EEFDEA6B32C42DFC4FCA17A41B2222E76C592EA5FD5EFF158B'
                . 'EC6778A4DE1DD3A6F78756EE97B821116ABF627D7DB5F5F49E2C733A0C50F'
                . '83149DF2157BE898FFD88A78522F68370633E558A2B4EEF71C8E26FB00AE9'
                . '7BEE00A4FEF0F281F7AE5F4C1B992A24B7762135AF21F22DF856C441D0E85'
                . '8BF43BED18E10C843AD20F98DB64BE9B25BF421EC7C38D50036588FCBF399'
                . '4509F919294145E5DEB6CC1FE7FF09776161CA232B2CC789DDCEA43470239'
                . '01BC0D08CC1642C6CA3C246FA6CD01F35265F93A57FC4A19333D212114580'
                . '769F91D0249618C4346912E41B0D8D945938695DD81E75999A578E9FC44C8'
                . 'F96FED782852FD13B6C67B8D9CABA4404FAE3B8282DC96163005F217A2FE8'
                . 'E4EF4AD0AD8821F0D6C501DDF974F4DDC731EE9FB05A6135AB04BC5D567A6'
                . 'E1A398DDAF7F0921641E30E78F4E8FE035199095204DE55DEA5B05343B6B9'
                . '656FAE5C2B9A9ECE32F461F5F4C50AFC19D650D0F1DDF36ED60AAFA9E1EC8'
                . '62897E57A52012CDD73FC5DC1CF645FFA3540BF88EC9CBC1CE0B10DA3022C'
                . '4A41FEFB717D598530BA806BDF5CA9B337D122FB2420E13ACA56CA59C5A5C'
                . '81C201443832D6EE33A57EB0914D693AA7DBF310BF27B58ED6617B692FC5C'
                . 'B6C2EB0A41FEF930D55EFF55E09B1199FBA891611827D48F60C1394E104C4'
                . '9E07DC422F9D90D1146ED5A267EB3FDFF69C7D21AA65CDA2B725FE33C51D9'
                . 'BDDF4ECD35E2F7A2B923B6DC6DF356A2D98EC4FD6BAA5EF351F6C17361E24'
                . '5A389B250CE23DD9587E82CB39D4EDFB7182D7915B14E514AD6A34803839A'
                . '2768E0A0EC4ACF1461C1BD39B0476D5E13FCF56C11F45404B2B3F6AC8A1FE'
                . '8273538BED687335AA9C25344017CDF20C717C8DC3219BE349A0A73A8BDAD'
                . '5CEDAD3B33BDEA19287A30AECAB2DD05EC96A66CF2AB758FF3F5D458C155E'
                . '0F670783320FBAD0DE37D844935A25CFB8F2B3CE5ACCE7C3E1D7CD11D3CE2'
                . '497CDE478789FA8DA46A38794D24491BC0A7D39ECE1DAC852E090ADA999AF'
                . 'FFC5C87B808D5D6BB76F1A9B797F2C4176C5EC06B15E7552A8617D08A9795'
                . '5CC93DDCAC5B9065F5888343D9EE30403EE72C05063C1B3791DF';
        $crypto = new CarwashCrypto();
        $digest = $crypto->digest($edgeDevice, $body, CarwashCrypto::PASS_SOCKET);

        $this->assertEquals("DD95EA0517CA7BA6BD9F3B700C7BAAFA", $digest);
    }

    /**
     * @test
     */
    public function TestDigest2() {
        //2021-04-27 08:30:23 sn: 141 key: 141|CE530168729|00:60:65:1F:01:1B|uxqtcsxbh6 digest: FA027D4C182D41A4CE8E77F5E8BE199D alg: 
        //md5 key: 7552aeb7d4f85e0bd39e2da41fc99f3f body: 60808C778DFDDCFDF2961A7C4507523698C387BF72512E661D3EC1639AC6852B|

        $carwash = (new Carwash())
            ->setSn(141);

        $edgeDevice = (new Carwash\EdgeDevice())
            ->setUid('CE530168729')
            ->setMac('00:60:65:1F:01:1B')
            ->setCarwash($carwash)
        ;

        $body = '60808C778DFDDCFDF2961A7C4507523698C387BF72512E661D3EC1639AC6852B';
        $crypto = new CarwashCrypto();
        
        $digest = $crypto->digest($edgeDevice, $body, CarwashCrypto::PASS_SOCKET);
        
                //print_r(['decrypted' => $crypto->decrypt($carwash, $body), 'digest' => $digest]);
                
        

        $this->assertEquals("FA027D4C182D41A4CE8E77F5E8BE199D", $digest);
    }

    /**
     * @test
     */
    public function TestDigest3() {

//POST /plc/CE530168729 HTTP/1.1Host: carwash-key.1.v1.bkf.pl
//Version: 1.0.0
//Connection: close
//User-Agent: BKF Carwash Firmware (2.9.18)(+http://bkf.pl/user-agent/bkf-carwash-firmware)
//Content-Type: application/json
//Pragma: no-cache
//Cache-Control: no-cache, no-store
//X-Hmac-md5-digest: 618A5B94BC0B7F526AB39AE54B478D3D
//Content-Length: 224
//
//669452654C0B2417DAAE9E9D5A338C97152C568299973ECD08D6B0A6669E454780C1509298F0E1
//945F13F9624D0BA6F4C609644152461C8CBA8140AEB4D5E37CABCE4AF572AEDFA8CB79F6617925
//27EA24D3AAA7025A98C95963676A274339BAD6F2DBC117D05903C4AF7C5A338E2D8B 

        $carwash = (new Carwash())
            ->setSn(141);

        $edgeDevice = (new Carwash\EdgeDevice())
            ->setUid('CE530168729')
            ->setMac('00:60:65:1F:01:1B')
            ->setCarwash($carwash);

        $body = '669452654C0B2417DAAE9E9D5A338C97152C568299973ECD08D6B0A6669E454'
                . '780C1509298F0E1945F13F9624D0BA6F4C609644152461C8CBA8140AEB4D5'
                . 'E37CABCE4AF572AEDFA8CB79F661792527EA24D3AAA7025A98C95963676A2'
                . '74339BAD6F2DBC117D05903C4AF7C5A338E2D8B';
        $crypto = new CarwashCrypto();

        $digest = $crypto->digest($edgeDevice, $body, CarwashCrypto::PASS_SOCKET);
        //print_r(['decrypted' => $crypto->decrypt($carwash, $body), 'digest' => $digest]);

        $this->assertEquals("618A5B94BC0B7F526AB39AE54B478D3D", $digest);
    }

    
   
    
      /**
     * @test
     */
    public function TestDigest4() {

    /// {"g_i2m_rev":{"value":"0.00"}}... digest: 00B2340A8AABE5543B54DC6D59ED051A encrypt: 60808C778DFDDCFDF2961A7C45075236E85EBD4232C6C74ACECC8D24C6C61131

        $carwash = (new Carwash())
            ->setSn(141);

        $edgeDevice = (new Carwash\EdgeDevice())
            ->setUid('CE530168729')
            ->setMac('00:60:65:1F:01:1B')
            ->setCarwash($carwash);

        $body = '60808C778DFDDCFDF2961A7C45075236E85EBD4232C6C74ACECC8D24C6C61131';
        $crypto = new CarwashCrypto();

        $digest = $crypto->digest($edgeDevice, $body, CarwashCrypto::PASS_SOCKET);
        //print_r(['decrypted' => $crypto->decrypt($carwash, $body), 'digest' => $digest]);

        $this->assertEquals("00B2340A8AABE5543B54DC6D59ED051A", $digest);
    }
    
}
