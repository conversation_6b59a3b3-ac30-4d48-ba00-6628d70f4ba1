<?php

namespace App\Controller\Owner;

use App\Model\Date\DateTimeNullableRangeRequest;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\TransactionsRepository;
use App\Repository\OwnerRepository;
use App\Service\SyncData\SyncDataService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;

class LoyaltyTransactionsController extends AbstractController
{
    #[Route('/api/owner/{ownerId}/transactions', methods: ['GET'])]
    public function getTransactions(
        int $ownerId,
        Request $request,
        TransactionsRepository $transactionsRepository,
        CardsRepository $cardsRepository,
        SyncDataService $syncDataService
    ): JsonResponse {

        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $email = $request->query->get('email');
        $cardNumber = $request->query->get('card');
        $cardToken = $request->query->get('cardToken');
        $page = $request->query->getInt('page', 1);
        $hasAlias = $request->query->get('hasAlias');
        $isVirtual = $request->query->get('isVirtual');
        $itemsPerPage = $request->query->getInt('itemsPerPage', 50);
        $orderBy = $request->query->get('orderBy', 'id');
        $orderDir = $request->query->get('orderDir', 'DESC');
        $search = $request->query->get('search');
        $sn = $request->get('sn') ? explode(',', $request->get('sn')) : null;
        $sources = $request->get('source') ? explode(',', $request->get('source')) : null;
        $types = $request->get('type') ? explode(',', $request->get('type')) : null;
        $showTotal = $request->query->getBoolean('showTotal', true);

        // Check for invalid pagination and ordering
        if (!is_numeric($page) || $page < 1 || !is_numeric($itemsPerPage) || $itemsPerPage < 1) {
            return $this->json(['error' => 'Invalid pagination parameters'], JsonResponse::HTTP_BAD_REQUEST);
        }
        if (!in_array($orderDir, ['ASC', 'DESC'])) {
            return $this->json(['error' => 'Invalid order direction'], JsonResponse::HTTP_BAD_REQUEST);
        }

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get('timezone')
        );

        $card = null;

        if ($cardNumber) {
            $card = $cardsRepository->getCardByNumber($cardNumber, $owner);
            if (!$card) {
                return $this->json(
                    ['error' => "Card by number not found"],
                    JsonResponse::HTTP_NOT_FOUND
                );
            }
        } elseif ($cardToken) {
            $card = $cardsRepository->getCardByToken($cardToken, $email);
            if (!$card) {
                return $this->json(
                    ['error' => "Card by token not found"],
                    JsonResponse::HTTP_NOT_FOUND
                );
            }
        }

        $transactions = $transactionsRepository->getHistory(
            $owner,
            $card,
            $dateRange->getDateFrom(),
            $dateRange->getDateTo(),
            $page,
            $itemsPerPage,
            $types,
            $sources,
            $orderBy,
            $orderDir,
            $search,
            $showTotal,
            $hasAlias,
            $isVirtual,
            $sn
        );

        return $this->json($transactions, JsonResponse::HTTP_OK, [], [
            'groups' => [
                "loyalty:transaction:owner",
                "default:basic",
            ],
            'datetime_format' => 'Y-m-d H:i:s',
            'timezone' => $dateRange->getTimezone()
        ]);
    }
}
