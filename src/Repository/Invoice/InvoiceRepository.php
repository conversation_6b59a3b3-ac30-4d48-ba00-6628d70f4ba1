<?php

namespace App\Repository\Invoice;

use App\Entity\Carwash;
use App\Entity\Invoice\Invoice;
use App\Entity\Invoices;
use App\Entity\Loyalty\Clients;
use App\Entity\Owners;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\ResultSetMapping;
use Doctrine\Persistence\ManagerRegistry;
use I2m\Invoices\Interface\I2mInvoiceRepositoryInterface;
use I2m\Invoices\Interface\IInvoice;
use I2m\Invoices\Interface\IIssuer;

/**
 * @extends ServiceEntityRepository<Invoice>
 */
class InvoiceRepository extends ServiceEntityRepository implements I2mInvoiceRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Invoice::class);
    }

//    /**
//     * @return Invoice[] Returns an array of Invoice objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('i')
//            ->andWhere('i.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('i.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?Invoice
//    {
//        return $this->createQueryBuilder('i')
//            ->andWhere('i.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
    public function save(IInvoice $invoice): IInvoice
    {
        $this->getEntityManager()->persist($invoice);
        $this->getEntityManager()->flush();
        return $invoice;
    }

    public function getNextNumber(IIssuer $issuer, string $schema): int
    {
        return $this->createQueryBuilder('i')
                ->leftJoin('i.issuer', 'issuer')
                ->select('COALESCE(MAX(i.sequentialNumber),0)')
                ->where('i.numberFormatted = :schema')
                ->andWhere('i.issuer = :issuer')
                ->setParameter('schema', $schema)
                ->setParameter('issuer', $issuer)
                ->getQuery()
                ->getSingleScalarResult() + 1;
    }

    public function getList(
        ?Owners $owner = null,
        ?int $page = 1,
        ?int $perPage = 10,
        ?string $search = null,
        ?\DateTimeInterface $date_from = null,
        ?\DateTimeInterface $date_to = null,
        ?Clients $client = null
    ) {

        $queryBuilder = $this->createQueryBuilder('e')
            ->leftJoin('e.issuer', 'o')
            ->leftJoin('e.client', 'c');

        if ($date_from !== null) {
            $queryBuilder->andWhere('e.createdAt >= :date_from')
                ->setParameter('date_from', $date_from);
        }
        if ($date_to !== null) {
            $queryBuilder->andWhere('e.createdAt <= :date_to')
                ->setParameter('date_to', $date_to);
        }


        if ($search) {
            $orX = $queryBuilder->expr()->orX();

            $orX->add($queryBuilder->expr()->like('LOWER(e.number)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(c.email)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(c.companyName)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(c.taxNumber)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(c.regon)', ':search'));

            $queryBuilder->andWhere($orX)
                ->setParameter('search', '%' . strtolower($search) . '%');
        }


        if ($owner) {
            $queryBuilder
                ->andWhere('e.issuer = :owner')
                ->setParameter('owner', $owner);
        }

        if ($client) {
            $queryBuilder
                ->andWhere('e.client = :client')
                ->setParameter('client', $client);
        }

        $countQueryBuilder = clone $queryBuilder;
        $total = $countQueryBuilder->select('COUNT(e)')->getQuery()->getSingleScalarResult();
        $queryBuilder->orderBy('e.id', 'DESC');


        $qb = $queryBuilder->setFirstResult(($page - 1) * $perPage)
            ->setMaxResults($perPage);
        ;


        return [
            'data' => $qb->getQuery()->getResult(),
            'total' => $total
        ];
    }


    /**
     * @return Invoice[]
     */
    public function findBreakingInvoices(?int $limit = null)
    {
        $baseQuery = $this->createQueryBuilder('i');

        $baseQuery->select('i')
            ->where('i.cloudPath is null')
            ->orWhere('i.number is null')
            ->setMaxResults($limit);

        return $baseQuery->getQuery()->getResult();
    }
}
