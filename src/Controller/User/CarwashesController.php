<?php

namespace App\Controller\User;

use App\Entity\User;
use App\Repository\CarwashRepository;
use I2m\Connectors\Service\CwActionApi\ActionAdminService;
use I2m\Connectors\Model\CwActionApi\Carwash;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

use function Sentry\captureMessage;

class CarwashesController extends AbstractController
{
    #[Route('/api/user/carwashes', methods: ['GET'])]
    public function getCarwashes(
        CarwashRepository $carwashRepository,
        ActionAdminService $actionAdminService,
        Request $request
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $email = $user->getEmail();

        $lat = $request->get('lat');
        $lon = $request->get('lon');
        $radius = $request->get('radius', 0.2);

        $carwashes = $carwashRepository->findNearby($lat, $lon, $radius, $email);

        $sns = array_map(function ($carwash) {
            return $carwash->getSn();
        }, $carwashes);

        $cwList = $actionAdminService->getList($sns, 1, 999);

        foreach ($carwashes as $carwash) {
            $cw = array_filter(
                $cwList->getData(),
                function (Carwash $item) use ($carwash) {
                    return $item->getSn() == $carwash->getSn();
                }
            );
            $cw = reset($cw);

            // nie znaleziono tej myjni
            if (!$cw) {
                captureMessage(
                    "Brak myjni {$carwash->getLongName()} w cw-action a jest w cw-loyalty" .
                    " prawdopodobnie trzeba ją usunąć z cw-loyalty"
                );
                continue;
            }

            foreach ($cw->getStands() as $stand) {
                if (!$stand->isMobileEnable()) {
                    continue;
                }
                $carwash->addStand(
                    [
                        'standCode' => $stand->getStandCode(),
                        'source' => $stand->getSource()->value,
                        'bayId' => $stand->getBayId(),
                    ]
                );
            }
        }

        return $this->json(
            data: $carwashes,
            status: JsonResponse::HTTP_OK,
            context: [
                'groups' =>
                    [
                        'default:basic',
                        'carwash:list'
                    ]
            ]
        );
    }
}
