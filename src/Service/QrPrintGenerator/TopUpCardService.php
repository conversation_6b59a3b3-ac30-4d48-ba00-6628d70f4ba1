<?php

namespace App\Service\QrPrintGenerator;

use App\Entity\Enum\Languages;
use Knp\Snappy\Pdf;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class TopUpCardService extends AbstractQrPrintGenerator
{
    public function __construct(
        readonly Environment $templating,
        readonly TranslatorInterface $translator,
        readonly Pdf $pdfPrinter,
        private ParameterBagInterface $parameterBag,
    ) {
        parent::__construct($templating, $translator, $pdfPrinter);
    }

    protected function qrCodeLink($code): string
    {
        $host = "https://vc.i2m.pl";

        return "$host/topup/$code";
    }

    protected function overlayTemplate(): string
    {
        return 'coupon/coupon.pdf.html.twig';
    }

    protected function overlayPageParameter(): array
    {
        return [
            'page-width' => '9.2cm',
            'page-height' => '6.1cm',
            'margin-bottom' => 0,
            'margin-left' => 0,
            'margin-right' => 0,
            'margin-top' => 0,
        ];
    }

    protected function pdfBackgroundPath(Languages $language): ?string
    {
        return
            $this->parameterBag->get('kernel.project_dir') .  DIRECTORY_SEPARATOR .
                "printables" . DIRECTORY_SEPARATOR .
                $language->value . DIRECTORY_SEPARATOR .
                "prepaid_front.pdf";
    }
}
