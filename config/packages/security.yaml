security:
    enable_authenticator_manager: true
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        app_user_provider:
            entity:
                class: App\Entity\User
                property: email
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        api_token:
            pattern: ^/token$
            security: false

        api_register:
            pattern: ^/register
            security: false

        api:
            pattern: ^/api
            security: true
            stateless: true
            oauth2: true
    access_control:
     - { path: ^/api/owner, roles: ROLE_OAUTH2_API }
     - { path: ^/api/user, roles: ROLE_OAUTH2_USER }

