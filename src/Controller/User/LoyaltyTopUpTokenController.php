<?php

namespace App\Controller\User;

use App\Entity\User;
use App\Model\ClientApp\RegisterTopUp;
use App\Model\ClientApp\SupportRequest;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Service\Loyalty\VirtualCardManager;
use App\Service\Support\SupportService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;

class LoyaltyTopUpTokenController extends AbstractController
{
    #[Route('/api/user/top_up/{token}/cards', methods: ['GET'])]
    public function getCardsForTopUp(
        string $token,
        Request $request,
        TopUpRepository $topUpRepository,
        CardsRepository $cardsRepository,
    ) {
        /** @var User $user */
        $user = $this->getUser();
        $topUp = $topUpRepository->getByToken($token);
        $cvv = $request->query->getInt('cvv');

        if (is_null($topUp)) {
            return $this->json(["error" => "top up not found" ], JsonResponse::HTTP_NOT_FOUND);
        }
        if ($cvv && $topUp->getCvv() != $cvv) {
            return $this->json(["error" => "wrong cvv" ], JsonResponse::HTTP_FORBIDDEN);
        }

        if ($topUp->getCard()) {
            return $this->json(["error" => "top up used" ], JsonResponse::HTTP_CONFLICT);
        }


        $cards = $cardsRepository->getCards(
            email: $user->getEmail(),
            owner: $topUp->getOwner()
        );

        return $this->json($cards, JsonResponse::HTTP_OK, [], [
            'groups' => [
                "loyalty:cards:user",
                "default:basic",
                "owner:logo",
                "owner:contact"
            ],
            'datetime_format' => 'Y-m-d H:i:s',
        ]);
    }

    #[Route('/api/user/top_up/{token}/register', methods: ['POST'])]
    public function register(
        string $token,
        Request $request,
        TopUpRepository $topUpRepository,
        CardsRepository $cardsRepository,
        VirtualCardManager $virtualCardManager,
        #[MapRequestPayload] RegisterTopUp $registerTopUp,
    ) {
        /** @var User $user */
        $user = $this->getUser();
        $topUp = $topUpRepository->getByToken($token);
        $cvv = (int)$request->query->get('cvv');

        if (empty($cvv)) {
            return $this->json(["error" => "empty cvv" ], JsonResponse::HTTP_BAD_REQUEST);
        }

        if (is_null($topUp)) {
            return $this->json(["error" => "top up not found" ], JsonResponse::HTTP_NOT_FOUND);
        }
        if ($topUp->getCvv() != $cvv) {
            return $this->json(["error" => "wrong cvv" ], JsonResponse::HTTP_FORBIDDEN);
        }

        if ($topUp->getCard()) {
            return $this->json(["error" => "top up already used" ], JsonResponse::HTTP_CONFLICT);
        }

        // jesli został przekazany token karty wtedy szukam jej w bazie
        // jak nie ma takiej karty wtedy tworze nowa
        $card = $registerTopUp->getCardToken() ? $cardsRepository->getCardByToken(
            cardToken: $registerTopUp->getCardToken(),
            email: $user->getEmail(),
            owner: $topUp->getOwner()
        ) : null;

        if (is_null($card)) {
            $card = $virtualCardManager->createCardForOwner($topUp->getOwner(), $user->getEmail());
        }

        $topUp->setCard($card);

        $topUpRepository->save($topUp);


        return $this->json($card, JsonResponse::HTTP_OK, [], [
            'groups' => [
                "loyalty:cards:user",
                "default:basic",
                "owner:logo",
                "owner:contact"
            ],
            'datetime_format' => 'Y-m-d H:i:s',
        ]);
    }

    #[Route('/api/user/top_up/{token}/support', methods: ['POST'])]
    public function sendSupport(
        string $token,
        TopUpRepository $topUpRepository,
        SupportService $supportService,
        #[MapRequestPayload] SupportRequest $supportRequest,
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();

        $topUp = $topUpRepository->getByToken($token);

        $supportService->createTicketFromTopUp($user, $topUp, $supportRequest->getDescription());

        return $this->json([], JsonResponse::HTTP_OK);
    }
}
