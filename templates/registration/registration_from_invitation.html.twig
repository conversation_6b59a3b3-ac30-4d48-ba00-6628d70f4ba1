{% extends 'base.html.twig' %}

{% block title %}{{ 'sign_up' | trans }} - {{ app_name }}{% endblock %}

{% block fos_user_content %}
    <main class="registration-page">
        <div class="registration-container">
            <div class="brand-header">
                <img src="{{ asset('assets/images/logo.png') }}" class="brand-logo" alt="{{ app_name }} Logo"/>
            </div>
            
            <div class="registration-card">
                <div class="card-header">
                    <h1>{{ 'registration.set_password' | trans }}</h1>
                    <div class="email-badge">
                        <svg class="email-icon" viewBox="0 0 24 24" width="16" height="16" stroke="currentColor" stroke-width="2" fill="none">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                            <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                        <span>{{ email }}</span>
                    </div>
                </div>

                <div class="card-body">

                    <form id="registration-form" method="post" class="registration-form" action="{{ path('invite_confirm') }}?token={{ token }}">
                        <input type="hidden" name="email" value="{{ email }}">

                        <div class="form-group">
                            <label for="password">
                                <svg class="input-icon" viewBox="0 0 24 24" width="18" height="18" stroke="currentColor" stroke-width="2" fill="none">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                    <path d="M7 11V7a5 5 0 0110 0v4"></path>
                                </svg>
                                {{ 'password' | trans }}
                            </label>
                            <div class="password-input-wrapper">
                                <input type="password" 
                                       id="password" 
                                       name="plainPassword"
                                       minlength="8"
                                       required>
                                <button type="button" class="password-toggle" data-target="password">
                                    <svg class="eye-icon" viewBox="0 0 24 24" width="18" height="18" stroke="currentColor" stroke-width="2" fill="none">
                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg>
                                    <svg class="eye-off-icon hidden" viewBox="0 0 24 24" width="18" height="18" stroke="currentColor" stroke-width="2" fill="none">
                                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                                        <line x1="1" y1="1" x2="23" y2="23"></line>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="password_repeat">
                                <svg class="input-icon" viewBox="0 0 24 24" width="18" height="18" stroke="currentColor" stroke-width="2" fill="none">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                    <path d="M7 11V7a5 5 0 0110 0v4"></path>
                                </svg>
                                {{ 'repeat_password' | trans }}
                            </label>
                            <div class="password-input-wrapper">
                                <input type="password" 
                                       id="password_repeat" 
                                       name="password_repeat"
                                       minlength="8"
                                       required>
                                <button type="button" class="password-toggle" data-target="password_repeat">
                                    <svg class="eye-icon" viewBox="0 0 24 24" width="18" height="18" stroke="currentColor" stroke-width="2" fill="none">
                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg>
                                    <svg class="eye-off-icon hidden" viewBox="0 0 24 24" width="18" height="18" stroke="currentColor" stroke-width="2" fill="none">
                                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                                        <line x1="1" y1="1" x2="23" y2="23"></line>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="form-group checkbox-group">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" 
                                       id="terms" 
                                       name="terms"
                                       required
                                       >
                                <label for="terms">
                                    {{ 'registration.i_accept' | trans }} 
                                    <a href="{{ path('terms_of_use') }}" target="_blank">{{ 'registration.terms_of_use' | trans }}</a>
                                </label>
                            </div>
                        </div>

                        <div class="form-group checkbox-group" style="margin-top: 0;">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" 
                                       id="privacy" 
                                       name="privacy"
                                       required
                                       >
                                <label for="privacy">
                                    {{ 'registration.i_accept' | trans }} 
                                    <a href="{{ path('privacy_policy') }}" target="_blank">{{ 'registration.privacy_policy' | trans }}</a>
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="submit-button">
                            {{ 'registration.complete' | trans }}
                            <svg class="button-icon" viewBox="0 0 24 24" width="18" height="18" stroke="currentColor" stroke-width="2" fill="none">
                                <path d="M5 12h14"></path>
                                <path d="M12 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <style>
        .registration-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #ef5350 0%, #e53935 100%);
            display: flex;
            justify-content: center;
            font-family: system-ui, -apple-system, sans-serif;
        }

        .registration-container {
            padding-top: 10vh;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }

        .brand-header {
            text-align: center;
            margin-bottom: 1rem;
        }

        .brand-logo {
            height: 60px;
            width: auto;
        }

        .registration-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            overflow: hidden;
        }

        .card-header {
            padding: 2rem 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }

        .card-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            padding-bottom: 0.5rem;
        }

        .subtitle {
            margin: 0.5rem 0 0;
            color: #666;
            font-size: 0.925rem;
        }

        .card-body {
            padding: 2rem;
        }

        .email-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: #f8f9fa;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            color: #2c3e50;
            font-size: 1rem;
            margin-top: 1rem;
            border: 1.5px solid #e5e7eb;
            border-radius: 8px;
        }

        .email-icon {
            color: #64748b;
        }

        .registration-form {
            display: flex;
            flex-direction: column;
            gap: 1.25rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            color: #374151;
            font-size: 0.925rem;
            font-weight: 500;
        }

        .input-icon {
            color: #64748b;
        }

        .password-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            width: 100%;
        }

        .password-input-wrapper input {
            width: 100%;
            box-sizing: border-box;
            padding: 0.75rem 1rem;
            border: 1.5px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.2s ease;
            background: #f8fafc;
            padding-right: 40px;
        }

        .password-input-wrapper input:focus {
            outline: none;
            border-color: #ef5350;
            background: white;
            box-shadow: 0 0 0 4px rgba(239, 83, 80, 0.1);
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6B7280;
            height: 100%;
        }

        .password-toggle:hover {
            color: #374151;
        }

        .password-toggle svg {
            transition: all 0.2s ease;
        }

        .hidden {
            display: none;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            margin-top: 0.75rem;
            gap: 0.25rem;
        }

        .checkbox-wrapper {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin-bottom: 0;
        }

        .checkbox-wrapper input[type="checkbox"] {
            width: 1.25rem;
            height: 1.25rem;
            margin: 0;
            margin-top: 0.125rem;
        }

        .checkbox-wrapper label {
            font-size: 0.925rem;
            color: #374151;
            font-weight: 400;
            line-height: 1.5;
        }

        .checkbox-wrapper label a {
            color: #ef5350;
            text-decoration: none;
            font-weight: 500;
        }

        .checkbox-wrapper label a:hover {
            text-decoration: underline;
        }

        .submit-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            background: #ff0000;
            color: white;
            border: none;
            padding: 0.875rem 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 0.5rem;
        }

        .button-icon {
            transition: transform 0.2s ease;
        }

        .submit-button:hover .button-icon {
            transform: translateX(4px);
        }

        .registration-footer {
            text-align: center;
            margin-top: 2rem;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.925rem;
        }

        .registration-footer a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            border-bottom: 1px solid rgba(255, 255, 255, 0.5);
            transition: all 0.2s ease;
        }

        .registration-footer a:hover {
            border-bottom-color: white;
        }

        .error-message {
            color: #dc2626;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: block;
        }

        .error {
            border-color: #dc2626;
        }

        .checkbox-wrapper .error-message {
            margin-left: 1.75rem;
        }

        @media (max-width: 480px) {
            .registration-page {
                padding: 1rem;
            }

            .card-header, .card-body {
                padding: 1.5rem;
            }

            .brand-logo {
                height: 40px;
            }
        }
    </style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('registration-form');
        const password = document.getElementById('password');
        const passwordRepeat = document.getElementById('password_repeat');
        const termsCheckbox = document.getElementById('terms');
        const privacyCheckbox = document.getElementById('privacy');

        // Password visibility toggle
        document.querySelectorAll('.password-toggle').forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const input = document.getElementById(targetId);
                const eyeIcon = this.querySelector('.eye-icon');
                const eyeOffIcon = this.querySelector('.eye-off-icon');

                if (input.type === 'password') {
                    input.type = 'text';
                    eyeIcon.classList.add('hidden');
                    eyeOffIcon.classList.remove('hidden');
                } else {
                    input.type = 'password';
                    eyeIcon.classList.remove('hidden');
                    eyeOffIcon.classList.add('hidden');
                }
            });
        });

        function showError(element, message) {
            const errorMessage = document.createElement('div');
            errorMessage.className = 'error-message';
            errorMessage.textContent = message;
            element.parentNode.appendChild(errorMessage);
        }

        const passwordMismatchMessage = "{{ 'passwords_mismatch' | trans | e('js') }}";
        form.addEventListener('submit', function(e) {
            
            if (password.value !== passwordRepeat.value) {
                e.preventDefault();  // Zatrzymujemy domyślne wysyłanie formularza
                showError(passwordRepeat, passwordMismatchMessage);
            }
        });

        password.addEventListener('input', function() {
            const errorMessages = document.querySelectorAll('.error-message');
            errorMessages.forEach(message => message.remove());
        });

        passwordRepeat.addEventListener('input', function() {
            const errorMessages = document.querySelectorAll('.error-message');
            errorMessages.forEach(message => message.remove());
        });
    });
</script>

{% endblock fos_user_content %}