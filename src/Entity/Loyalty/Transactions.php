<?php

namespace App\Entity\Loyalty;

use App\Entity\Carwash;
use App\Entity\Currency;
use App\Entity\Loyalty\Enum\TransactionType;
use App\Entity\Owners;
use App\Entity\User;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\StandardTypes\Enum\Source;
use Symfony\Component\Serializer\Annotation\Groups;

//#[ORM\Index(name: 'raport', columns: ['type', 'card', 'ctime'])]
//#[ORM\Index(name: 'ctime', columns: ['ctime'])]
#[ORM\UniqueConstraint(name: 'uniq_transactions', columns: ['carwash_id', 'card_id', 'type', 'source', 'value', 'balance', 'time', 'bay_id'])]
#[ORM\Index(name: 'raport_trans_owner', columns: ['owner_id', 'time'])]
#[ORM\Index(name: 'raport_trans_card', columns: ['card_id', 'time'])]
#[ORM\Entity(repositoryClass: \App\Repository\Loyalty\TransactionsRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Transactions
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['loyalty:transaction:owner',"loyalty:transaction:user"])]
    private int $id;

    #[ORM\ManyToOne(targetEntity: Owners::class)]
    #[Groups(["loyalty:transaction:user"])]
    private ?Owners $owner;

    #[ORM\Column(name: 'value', type: 'decimal', precision: 10, scale: 2, nullable: true)]
    private $value;

    /**
     * @var float|null
     */
    #[ORM\Column(name: 'balance', type: 'decimal', precision: 10, scale: 2, nullable: true)]
    private $balance;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $ctime;

    #[ORM\Column(type: 'datetime')]
    #[Groups(['loyalty:transaction:owner',"loyalty:transaction:user"])]
    private \DateTimeInterface $time;

    #[ORM\ManyToOne(targetEntity: Cards::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['loyalty:transaction:owner',"loyalty:transaction:user"])]
    private Cards $card;

    #[ORM\ManyToOne(targetEntity: Carwash::class)]
    #[Groups(['loyalty:transaction:owner',"loyalty:transaction:user"])]
    private ?Carwash $carwash;

    #[ORM\Column(type: 'string', enumType: TransactionType::class)]
    #[Groups(['loyalty:transaction:owner',"loyalty:transaction:user"])]
    private TransactionType $type;

    #[ORM\Column(enumType: Source::class)]
    #[Groups(['loyalty:transaction:owner',"loyalty:transaction:user"])]
    private Source $source;

    #[ORM\Column(type: 'integer', nullable: true)]
    #[Groups(['loyalty:transaction:owner',"loyalty:transaction:user"])]
    private ?int $bayId = null;

    #[ORM\ManyToOne(targetEntity: Currency::class)]
    #[Groups(['loyalty:transaction:owner',"loyalty:transaction:user"])]
    private ?Currency $currency;

    #[ORM\ManyToOne()]
    #[Groups(['loyalty:transaction:owner'])]
    private ?User $user = null;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $licensePlate = null;

    /**
     * Transactions constructor.
     * @throws \Exception
     */
    public function __construct()
    {
        $this->ctime = new \DateTime();
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return float|null
     */
    public function getValueInCredit(): ?float
    {
        return $this->value;
    }

    /**
     * @param float|null $value
     * @return Transactions
     */
    public function setValueInCredit(?float $value): self
    {
        $this->value = $value;

        return $this;
    }

    #[Groups(['loyalty:transaction:owner',"loyalty:transaction:user"])]
    public function getValue(): float
    {
        return $this->getValueInCredit() * $this->getCurrency()->getRate();
    }

    public function setValue($value): self
    {
        $this->setValueInCredit($value / $this->getCurrency()->getRate());
        return $this;
    }

    /**
     * @return float|null
     */
    public function getBalanceInCredit(): ?float
    {
        return $this->balance;
    }

    /**
     * @param float|null $balance
     * @return Transactions
     */
    public function setBalanceInCredit(?float $balance): self
    {
        $this->balance = $balance;

        return $this;
    }

    #[Groups(['loyalty:transaction:owner',"loyalty:transaction:user"])]
    public function getBalance(): float
    {
        return $this->getBalanceInCredit() * $this->getCurrency()->getRate();
    }

    /**
     * @return \DateTimeInterface|null
     */
    public function getCtime(): ?\DateTimeInterface
    {
        return $this->ctime;
    }

    /**
     * @param \DateTimeInterface|null $ctime
     * @return Transactions
     */
    public function setCtime(?\DateTimeInterface $ctime): self
    {
        $this->ctime = $ctime;

        return $this;
    }


    /**
     * @return Cards|null
     */
    public function getCard(): ?Cards
    {
        return $this->card;
    }

    /**
     * @param Cards|null $card
     * @return Transactions
     */
    public function setCard(?Cards $card): self
    {
        $this->card = $card;

        return $this;
    }

    /**
     * @return Carwash|null
     */
    public function getCarwash(): ?Carwash
    {
        return $this->carwash;
    }

    /**
     * @param Carwash|null $carwash
     * @return Transactions
     */
    public function setCarwash(?Carwash $carwash): self
    {
        $this->carwash = $carwash;

        return $this;
    }


    public function getType(): ?TransactionType
    {
        return $this->type;
    }

    public function setType(?TransactionType $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getSource(): ?Source
    {
        return $this->source;
    }

    public function setSource(?Source $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function getBayId(): ?int
    {
        return $this->bayId;
    }

    public function setBayId(?int $bayId): self
    {
        $this->bayId = $bayId;

        return $this;
    }

    #[ORM\PrePersist]
    public function updatedTimestamps(): void
    {
        if ($this->getCtime() == null) {
            $this->setCtime(new \DateTime('now'));
        }
    }

    #[Groups(['loyalty:transaction:owner',"loyalty:transaction:user"])]
    public function getCurrency(): ?Currency
    {
        return
            $this->currency ??
            $this->getCard()->getCurrency() ??
            $this->getCard()->getOwner()->getCurrency();
    }


    public function setCurrency(?Currency $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getTime(): \DateTimeInterface
    {
        return $this->time;
    }

    public function setTime(\DateTimeInterface $time): self
    {
        $this->time = $time;
        return $this;
    }

    public function getOwner(): Owners
    {
        return $this->owner;
    }

    public function setOwner(?Owners $owner): self
    {
        $this->owner = $owner;
        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): Transactions
    {
        $this->user = $user;
        return $this;
    }

    public function getLicensePlate(): ?string
    {
        return $this->licensePlate;
    }

    public function setLicensePlate(?string $licensePlate): static
    {
        $this->licensePlate = $licensePlate;

        return $this;
    }
}
