<?php

namespace App\Service\Loyalty;

use App\Entity\Loyalty\Clients;
use App\Service\Email\EmailSender;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Contracts\Translation\TranslatorInterface;

class CardReportEmailService
{
    public function __construct(
        private TranslatorInterface $translator,
        private EmailSender $emailSender,
    ) {
    }

    public function sendEmail(string $path, Clients $client, array $criterias = [], array $cc = [])
    {
        $file = new File($path);

        $owner = $client->getOwner();

        if (!$owner) {
            throw new \Exception('No owner');
        }

        $this->emailSender->sendHtml(
            to: $client->getEmail(),
            subject: $this->translator->trans(
                id: 'reports_client_cards_clients_email_header',
                parameters: ['%issuer%' => $owner->getName()],
                locale: $owner->getConfig()->getLanguage()->value,
            ),
            templateName: 'Reports/report_email.html.twig',
            replyTo: $owner->getConfig()->getSupportEmail(),
            context: [
                'criterias' => $criterias,
            ],
            attachments: [$file],
            cc: $cc,
        );
    }
}
