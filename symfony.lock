{"bkf/connector": {"version": "dev-main"}, "dama/doctrine-test-bundle": {"version": "8.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "7.2", "ref": "896306d79d4ee143af9eadf9b09fd34a8c391b70"}, "files": ["./config/packages/dama_doctrine_test_bundle.yaml"]}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.10", "ref": "c170ded8fc587d6bd670550c43dafcf093762245"}, "files": ["./config/packages/doctrine.yaml", "./src/Entity/.gitignore", "./src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["./src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["./config/packages/doctrine_migrations.yaml", "./migrations/.gitignore"]}, "i2m/connectors": {"version": "dev-master"}, "i2m/iiot": {"version": "dev-master"}, "i2m/invoices": {"version": "dev-main"}, "i2m/payment": {"version": "dev-main"}, "i2m/reports": {"version": "dev-main"}, "i2m/storage": {"version": "dev-main"}, "knplabs/knp-snappy-bundle": {"version": "1.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.5", "ref": "c81bdcf4a9d4e7b1959071457f9608631865d381"}, "files": ["./config/packages/knp_snappy.yaml"]}, "league/oauth2-server-bundle": {"version": "0.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "0.4", "ref": "7f26963037fce3b69c1b3f6a75a3265edb1e1caa"}, "files": ["./config/packages/league_oauth2_server.yaml", "./config/routes/league_oauth2_server.yaml"]}, "nyholm/psr7": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "4a8c0345442dcca1d8a2c65633dcf0285dd5a5a2"}, "files": ["./config/packages/nyholm_psr7.yaml"]}, "phpstan/phpstan": {"version": "1.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}, "files": ["./phpstan.dist.neon"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": ["./.env.test", "./phpunit.xml.dist", "./tests/bootstrap.php"]}, "sentry/sentry-symfony": {"version": "5.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "5.0", "ref": "76afa07d23e76f678942f00af5a6a417ba0816d0"}, "files": ["./config/packages/sentry.yaml"]}, "squizlabs/php_codesniffer": {"version": "3.11", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.6", "ref": "1019e5c08d4821cb9b77f4891f8e9c31ff20ac6f"}, "files": ["./phpcs.xml.dist"]}, "symfony/console": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["./bin/console"]}, "symfony/debug-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["./config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": ["./.env"]}, "symfony/framework-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "32126346f25e1cee607cc4aa6783d46034920554"}, "files": ["./config/packages/cache.yaml", "./config/packages/framework.yaml", "./config/preload.php", "./config/routes/framework.yaml", "./config/services.yaml", "./public/index.php", "./src/Controller/.gitignore", "./src/Kernel.php"]}, "symfony/lock": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "8e937ff2b4735d110af1770f242c1107fdab4c8e"}, "files": ["./config/packages/lock.yaml"]}, "symfony/mailer": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["./config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.61", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["./config/packages/monolog.yaml"]}, "symfony/phpunit-bridge": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": ["./.env.test", "./bin/phpunit", "./phpunit.xml.dist", "./tests/bootstrap.php"]}, "symfony/routing": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "e0a11b4ccb8c9e70b574ff5ad3dfdcd41dec5aa6"}, "files": ["./config/packages/routing.yaml", "./config/routes.yaml"]}, "symfony/security-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["./config/packages/security.yaml", "./config/routes/security.yaml"]}, "symfony/translation": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["./config/packages/translation.yaml", "./translations/.gitignore"]}, "symfony/twig-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["./config/packages/twig.yaml", "./templates/base.html.twig"]}, "symfony/validator": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["./config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["./config/packages/web_profiler.yaml", "./config/routes/web_profiler.yaml"]}, "twig/extra-bundle": {"version": "v3.15.0"}}