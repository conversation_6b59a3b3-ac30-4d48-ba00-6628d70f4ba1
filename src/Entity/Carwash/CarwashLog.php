<?php

namespace App\Entity\Carwash;

use App\Entity\Carwash;
use App\Repository\Carwash\CarwashLogRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table]
#[ORM\Index(name: 'carwash_id_type', columns: ['carwash_id', 'type'])]
#[ORM\Index(columns: ['level'])]
#[ORM\Entity(repositoryClass: CarwashLogRepository::class)]
#[ORM\HasLifecycleCallbacks]
class CarwashLog
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private int $id;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: Carwash::class)]
    private ?Carwash $carwash;

    #[ORM\Column(type: 'datetime')]
    private ?\DateTimeInterface $ctime = null;

    #[ORM\Column(type: 'string', length: 255)]
    private string $type;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $identifier;

    #[ORM\Column(type: 'text', nullable: true)]
    private string $comment;

    #[ORM\Column(type: 'string', length: 16, nullable: true)]
    private string $level;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCarwash(): ?Carwash
    {
        return $this->carwash;
    }

    public function setCarwash(?Carwash $carwash): self
    {
        $this->carwash = $carwash;

        return $this;
    }

    public function getCtime(): ?\DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(\DateTimeInterface $ctime): self
    {
        $this->ctime = $ctime;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getIdentifier(): ?string
    {
        return $this->identifier;
    }

    public function setIdentifier(?string $identifier): self
    {
        $this->identifier = $identifier;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getLevel(): string
    {
        return $this->level;
    }

    public function setLevel($level): self
    {
        $this->level = $level;
        return $this;
    }

    #[ORM\PrePersist]
    public function updatedTimestamps(): void
    {
        if (!isset($this->ctime)) {
            $this->setCtime(new \DateTime('now'));
        }
    }
}
