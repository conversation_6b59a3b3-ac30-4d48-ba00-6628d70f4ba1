<?php

namespace App\Entity\Loyalty;

use App\Entity\Carwash;
use App\Entity\Currency;
use App\Entity\ExternalPayment\ExternalPayment;
use App\Entity\Invoice\Invoice;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Loyalty\Enum\ClientsInvoiceType;
use App\Entity\Loyalty\Enum\TopUpStatus;
use App\Entity\Loyalty\Enum\TopUpType;
use App\Entity\Owners;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\StandardTypes\Enum\Source;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: \App\Repository\Loyalty\TopUpRepository::class)]
#[ORM\Index(name: 'raport_topup_owner', columns: ['owner_id', 'ctime'])]
#[ORM\Index(name: 'raport_topup_card', columns: ['card_id', 'ctime'])]
class TopUp
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['loyalty:topup:list'])]
    private int $id;

    #[ORM\Column(name: 'top_up_value', type: 'decimal', precision: 20, scale: 2)]
    private string $topUpValue;

    #[ORM\Column(enumType: TopUpStatus::class)]
    #[Groups(['loyalty:topup:list','loyalty:topup:user'])]
    private TopUpStatus $status = TopUpStatus::ACTIVE;

    #[ORM\Column(name: 'ctime', type: 'datetime', options: ['default' => 'CURRENT_TIMESTAMP'])]
    #[Groups(['loyalty:topup:list'])]
    private \DateTimeInterface $ctime;

    #[ORM\Column(name: 'mtime', type: 'datetime', nullable: true, columnDefinition: 'DATETIME on update CURRENT_TIMESTAMP')]
    #[Groups(['loyalty:topup:list'])]
    private ?\DateTimeInterface $mtime;

    #[ORM\Column(name: 'value', type: 'decimal', precision: 20, scale: 2)]
    private string $value;

    #[ORM\Column(type: 'decimal', precision: 20, scale: 2, nullable: true)]
    private ?string $canceled = null;

    #[ORM\ManyToOne(targetEntity: Cards::class)]
    #[Groups(['loyalty:topup:list','loyalty:topup:user'])]
    private ?Cards $card = null;

    #[ORM\ManyToOne(targetEntity: Invoice::class)]
    #[Groups(['loyalty:topup:list','loyalty:topup:user'])]
    private ?Invoice $invoice2 = null;

    #[ORM\Column(enumType: TopUpType::class)]
    #[Groups(['loyalty:topup:list'])]
    private TopUpType $type;

    #[ORM\Column(enumType: Source::class)]
    #[Groups(['loyalty:topup:list'])]
    private Source $source;

    #[ORM\ManyToOne(targetEntity: Currency::class)]
    #[Groups(['loyalty:topup:list','loyalty:topup:user'])]
    private ?Currency $currency;

    #[ORM\ManyToOne(targetEntity: Carwash::class)]
    #[Groups(['loyalty:topup:list'])]
    private ?Carwash $carwash = null;

    #[ORM\ManyToOne(targetEntity: CyclicTopUp::class, inversedBy: 'topUp')]
    #[Groups(['loyalty:topup:list'])]
    private ?CyclicTopUp $cyclicTopUp;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(['loyalty:topup:list'])]
    private $addedBy;

    #[ORM\Column(type: 'string', length: 128, nullable: true)]
    private ?string $token = null;

    #[ORM\Column(type: 'integer', nullable: true)]
    private ?int $cvv = null;

    #[ORM\ManyToOne(targetEntity: Owners::class)]
    #[Groups(['loyalty:topup:user'])]
    private ?Owners $owner = null;

    #[ORM\ManyToOne(targetEntity: ExternalPayment::class, inversedBy: 'topUp')]
    #[Groups(['loyalty:topup:list'])]
    private ?ExternalPayment $payment = null;

    #[ORM\Column(nullable: true, type: "text")]
    #[Groups(['loyalty:topup:list'])]
    private ?string $description = null;

    public function getType(): TopUpType
    {
        return $this->type;
    }

    public function setType(TopUpType $type): TopUp
    {
        $this->type = $type;
        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }


    public function getStatus(): ?TopUpStatus
    {
        return $this->status;
    }


    public function setStatus(?TopUpStatus $status): self
    {
        $this->status = $status;

        return $this;
    }


    public function getCtime(): \DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(\DateTimeInterface $ctime): self
    {
        $this->ctime = $ctime;

        return $this;
    }


    public function getMtime(): ?\DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(?\DateTimeInterface $mtime): self
    {
        $this->mtime = $mtime;

        return $this;
    }

    public function getValueInCredit(): float
    {
        return (float)$this->value;
    }

    public function setValueInCredit(float $value): self
    {
        $this->value = number_format($value, 2, '.', '');

        return $this;
    }

    #[Groups(['loyalty:topup:list', 'loyalty:topup:user'])]
    public function getValue(): float
    {
        return $this->getValueInCredit() * $this->getCurrency()->getRate();
    }

    public function setValue($value): self
    {
        $this->setValueInCredit($value / $this->getCurrency()->getRate());
        return $this;
    }

    public function getTopUpValueInCredit(): float
    {
        return (float) $this->topUpValue;
    }


    public function setTopUpValueInCredit(float $value): self
    {
        $this->topUpValue = number_format($value, 2, '.', '');
        ;

        return $this;
    }

    #[Groups(['loyalty:topup:list', 'loyalty:topup:user'])]
    public function getTopUpValue(): float
    {
        return $this->getTopUpValueInCredit() * $this->getCurrency()->getRate();
    }

    public function setTopUpValue($value): self
    {
        $this->setTopUpValueInCredit($value / $this->getCurrency()->getRate());
        return $this;
    }

    public function getCard(): ?Cards
    {
        return $this->card;
    }


    public function setCard(?Cards $card): self
    {
        $this->card = $card;

        return $this;
    }

    public function getSource(): ?Source
    {
        return $this->source;
    }

    public function setSource(Source $source): self
    {
        $this->source = $source;

        return $this;
    }

    #[Groups(['loyalty:topup:list'])]
    public function getInvoice(): ?int
    {
        return $this->getInvoice2()?->getId();
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps(): void
    {
        if (!isset($this->ctime)) {
            $this->setCtime(new \DateTime('now'));
        }
        $this->setMtime(new \DateTime('now'));

        if ($this->getToken() == null) {
            $this->generateCrypto();
        }
    }

    public function getCurrency(): ?Currency
    {
        return
            $this->currency ??
            $this->getCard()?->getCurrency()
            ;
    }


    public function setCurrency(?Currency $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getCyclicTopUp(): ?CyclicTopUp
    {
        return $this->cyclicTopUp;
    }

    public function setCyclicTopUp(?CyclicTopUp $cyclicTopUp): self
    {
        $this->cyclicTopUp = $cyclicTopUp;

        return $this;
    }

    public function getAddedBy(): ?string
    {
        return $this->addedBy;
    }

    public function setAddedBy(?string $addedBy): self
    {
        $this->addedBy = $addedBy;

        return $this;
    }

    public function getCanceled(): ?float
    {
        return $this->getCanceledInCredit() * $this->getCurrency()->getRate();
    }

    public function getCanceledInCredit(): ?float
    {
        return (float) $this->canceled;
    }

    public function setCanceledInCredit(?float $canceled): TopUp
    {
        $this->canceled = number_format($canceled, 2, '.', '');
        ;
        return $this;
    }

    #[Groups(['loyalty:topup:list'])]
    public function getProgress(): string
    {
        if ($this->getCanceledInCredit() > 0) {
            return 'CANCELED';
        }

        if ($this->getValueInCredit() < $this->getTopUpValueInCredit() && $this->getValueInCredit() != 0) {
            return 'NOT_FULLY_REFILLED';
        }

        if ($this->getValueInCredit() > 0) {
            return 'WAITING';
        }

        if ($this->getValueInCredit() == 0) {
            return 'REFILLED';
        }

        return 'UNKNOWN';
    }

    #[Groups(['loyalty:topup:list'])]
    public function getTopUpSent(): float
    {
        return $this->getTopUpValue() - $this->getValue() - $this->getCanceled();
    }

    #[Groups(['loyalty:topup:list'])]
    public function getTopUpToSend(): float
    {
        return $this->getTopUpValue() - $this->getTopUpSent() - $this->getCanceled();
    }

    public function getCarwash(): ?Carwash
    {
        return $this->carwash;
    }

    public function setCarwash(?Carwash $carwash): TopUp
    {
        $this->carwash = $carwash;
        return $this;
    }

    #[Groups(['loyalty:topup:list'])]
    public function getInvoiceGenerate(): bool
    {
        return
            is_null($this->invoice2) &&
            (is_null($this->canceled) || $this->canceled <= 0) &&
            !is_null($this->getCard()?->getClient()) &&
            ($this->getCard()->getClient()->getInvoiceStrategy() != ClientsInvoiceType::BLOCK) &&
            ($this->getType() == TopUpType::ADDITION)
            ;
    }

    public function getToken(): ?string
    {
        return $this->token;
    }

    public function setToken(?string $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function getCvv(): ?int
    {
        return $this->cvv;
    }

    public function setCvv(?int $cvv): self
    {
        $this->cvv = $cvv;

        return $this;
    }

    public function generateCrypto()
    {
        if (!$this->token) {
            $this->token = bin2hex(random_bytes(8));
        }

        if (!$this->cvv) {
            $this->cvv = random_int(0, 999);
        }
    }

    #[Groups(['topup:read', 'topup:write', 'loyalty:topup:list'])]
    public function getTopUpToken(): ?string
    {
        return  implode('-', [
            $this->getOwner()?->getId() ?? 0,
            dechex($this->getId()),
            $this->token]);
    }

    public function getOwner(): ?Owners
    {
        return $this->owner ?? $this->getCard()?->getOwner();
    }

    public function setOwner(?Owners $owner): TopUp
    {
        $this->owner = $owner;
        return $this;
    }

    public function getPayment(): ?ExternalPayment
    {
        return $this->payment;
    }

    public function setPayment(?ExternalPayment $payment): TopUp
    {
        $this->payment = $payment;
        return $this;
    }

    public function getInvoice2(): ?Invoice
    {
        return $this->invoice2;
    }

    public function setInvoice2(?Invoice $invoice2): TopUp
    {
        $this->invoice2 = $invoice2;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): TopUp
    {
        $this->description = $description;
        return $this;
    }
}
