{% extends 'base_email.html.twig' %}

{% block body %}
<tr>
    <td style="padding: 15px; background-color: #fff; color: #000">
    </td>
</tr>
<tr>
    <td style="padding: 0px 20px 0px 20px; background-color: #fff; color: #000">
        <div style="padding-top: 10px">
            {{ 'owner_email_user_create_new_card' | trans(
                { '{email}': userEmail,
                  '{cardNumber}': cardNumber,
                  '{standCode}': standCode,
                  '{carwashName}': carwashName,
                }
            ) | raw }}
        </div>
    </td>
</tr>
<tr>
    <td style="padding: 30px 20px 0px 20px; background-color: #fff; color: #000">
        <p>
            {{ 'common.regards'|trans|raw }}
        </p>
    </td>
</tr>

{% endblock body %}

{% block tryApp %}
{% endblock tryApp %}
