<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Tests\User;

use App\Entity\Carwash;
use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\Enum\TopUpStatus;
use App\Entity\Loyalty\TopUp;
use App\Entity\User;
use App\Repository\CarwashRepository;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

/**
 * Description of CarwashApiControllerTest
 *
 * <AUTHOR>
 */
class UserTourTest extends WebTestCase
{
    private const USER_MAIL = '<EMAIL>';
    private const STAND = "00150019";
    private const STAND_PAYMENT_VALUE = 1;
    private const PASSWORD = "qaz12wsx";
    private UserRepository $userRepo;
    private TopUpRepository $topUpRepo;
    private KernelBrowser $client;
    private EntityManagerInterface $em;

    protected function setUp(): void
    {
        $this->client = static::createClient();

        $kernel = self::bootKernel();

        $em = $kernel->getContainer()
                ->get('doctrine')
                ->getManager();

        $this->em = $em;
        $this->userRepo = $em->getRepository(User::class);
        $this->topUpRepo = $em->getRepository(TopUp::class);
    }

    /**
     * @test
     */
    public function tour1Test()
    {

        //$this->markTestIncomplete();
        $user = $this->userRepo->findOneByEmail(self::USER_MAIL);
        $this->assertNull($user); // sprawdzam czy na pewno nie istnieje, przed rejestracja

        $this->apiRegisterUser(self::USER_MAIL);
        $user = $this->userRepo->findOneByEmail(self::USER_MAIL);

        $this->apiConfirmRegistration($user->getRegistrationToken());
        $this->em->refresh($user);

        /// zaloguj sie i otrzymaj token uzytkownika
        $this->apiLogin(self::USER_MAIL);


        //// pobierz listę kart dla stanowiska 00150019 - powinna być pusta
        $content = $this->getCardsForStand(self::STAND);
        $this->assertEmpty($content['cards']);

        //// dodaj kartę do stanowiska 00150019
        $content = $this->newCardForStand(self::STAND);

        $content = $this->getCardsForStand(self::STAND);
        $this->assertNotEmpty($content['cards']);

        /// doladuj ta kartę przy pomocy kartki z doladowaniem
        $topUp = $this->topUpRepo->findOneBy([
            'token' => "token_doladowania",
            'cvv' => 123
        ]);
        $card = $content['cards'][0];
        $this->registerTopUp($card['cardToken'], $topUp);

        // sprawdz czy balance karty sie zaaktualizowal
        $content = $this->getCardsForStand(self::STAND);
        $this->assertNotEmpty($content['cards']);

        $card = $content['cards'][0];
        $this->assertEquals($card['balance'], $topUp->getValue());

        // zaplac na stanowisku 00150019
        $this->payForStand($card['cardToken'], self::STAND_PAYMENT_VALUE, self::STAND);

        // pobierz dane karty i zobacz czy zmniejszyl sie balance karty
        $content = $this->getCardsForStand(self::STAND);
        $card = $content['cards'][0];
        $this->assertEquals($card['balance'], $topUp->getValue() - self::STAND_PAYMENT_VALUE);

        // pobierz listę transakcji,
        $transactions = $this->getTransactionsForCard($card['cardToken']);
        $this->assertArrayHasKey('data', $transactions);

        // powinny byc 2 transakcje: SUBTRACTION (płatność) i ADDITION (doładowanie)
        $transactions = $transactions['data'];
        $this->assertCount(2, $transactions); 
        $this->assertEquals('SUBTRACTION', $transactions[0]['type']);
        $this->assertEquals(-self::STAND_PAYMENT_VALUE, $transactions[0]['value']);
        $this->assertEquals('ADDITION', $transactions[1]['type']);
        $this->assertEquals($topUp->getValue(), $transactions[1]['value']);
        
        // pobierz listę myjnii uzytkownika
        $carwashes = $this->getCarwashes();
        $this->assertNotEmpty($carwashes);

        // pobierz liste myjni dla karty
        $carwashes = $this->getCarwashesForCard($card['cardToken']);
        $this->assertNotEmpty($carwashes);

    }

    private function apiRegisterUser(string $mail)
    {
        $this->client->request('POST', '/register', [

            'email' => $mail,
            'plainPassword' => self::PASSWORD,
            'lang' => 'en'
        ]);

        $this->assertEquals(Response::HTTP_CREATED, $this->client->getResponse()->getStatusCode());
    }

    private function apiConfirmRegistration(string $registrationToken)
    {
        $this->client->request('GET', "/register/confirm?token=$registrationToken");
        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
    }


    private function apiLogin(string $mail)
    {
        $this->client->request(
            'POST',
            "/token",
            [
            "username" => $mail,
            "password" => self::PASSWORD,
            "grant_type" => "password",
            "client_id" => "client_app_test",
            "client_secret" => "app_secret"
            ]
        );
        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        $token = json_decode($this->client->getResponse()->getContent(), true)['access_token'];
        $this->client->setServerParameter('HTTP_Authorization', 'Bearer ' . $token);
    }
    private function getCardsForStand(string $stand)
    {
        $this->client->request('GET', "/api/user/stand/$stand/cards");
        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode(), $this->client->getResponse()->getContent());
        $content = json_decode($this->client->getResponse()->getContent(), true);
        return $content;
    }

    private function newCardForStand(string $stand)
    {
        $this->client->request('POST', "/api/user/stand/$stand/cards");
        $this->assertEquals(Response::HTTP_CREATED, $this->client->getResponse()->getStatusCode());
        $content = json_decode($this->client->getResponse()->getContent(), true);
        return $content;
    }

    private function registerTopUp(string $cardToken, TopUp $topUp)
    {
        $topUpToken = $topUp->getTopUpToken();
        $this->client->request(
            'POST',
            "/api/user/top_up/$topUpToken/register?cvv=" . $topUp->getCvv(),
            [],  // query parameters are now in the URL
            [],  // files
            ['CONTENT_TYPE' => 'application/json'],
            json_encode(['cardToken' => $cardToken])
        );
        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
    }
    
    private function payForStand(string $cardToken, int $value, string $standCode)
    {
        $this->client->request(
            'POST',
            "/api/user/card/$cardToken/pay",
            [],  // query parameters are now in the URL
            [],  // files
            ['CONTENT_TYPE' => 'application/json'],
            json_encode(['value' => $value, 'standCode' => $standCode])
        );
        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
    }

    private function getTransactionsForCard(string $cardToken)
    {
        $this->client->request(
            'GET',
            "/api/user/card/$cardToken/transactions"
        );
        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode(), $this->client->getResponse()->getContent());
        $content = json_decode($this->client->getResponse()->getContent(), true);
        return $content;
    }

    private function getCarwashes()
    {
        $this->client->request(
            'GET',
            "/api/user/carwashes"
        );
        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode(), $this->client->getResponse()->getContent());
        $content = json_decode($this->client->getResponse()->getContent(), true);
        return $content;
    }

    private function getCarwashesForCard(string $cardToken)
    {
        $this->client->request(
            'GET',
            "/api/user/card/$cardToken/carwashes"
        );
        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode(), $this->client->getResponse()->getContent());
        $content = json_decode($this->client->getResponse()->getContent(), true);
        return $content;
    }
}
