<?php

namespace App\Model\Loyalty;

use App\Entity\Loyalty\Enum\CardStatus;

class Card
{
    private ?CardStatus $status = null;
    private ?string $email = null;
    private ?string $alias = null;
    private ?string $description = null;
    private ?int $clientId = null;
    private ?string $number = null;
    private ?float $value = null;

    public function getStatus(): ?CardStatus
    {
        return $this->status;
    }

    public function setStatus(?CardStatus $status): Card
    {
        $this->status = $status;
        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): Card
    {
        $this->email = $email;
        return $this;
    }

    public function getAlias(): ?string
    {
        return $this->alias;
    }

    public function setAlias(?string $alias): Card
    {
        $this->alias = $alias;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): Card
    {
        $this->description = $description;
        return $this;
    }

    public function getClientId(): ?int
    {
        return $this->clientId;
    }

    public function setClientId(?int $clientId): Card
    {
        $this->clientId = $clientId;
        return $this;
    }

    public function getNumber(): ?string
    {
        return $this->number;
    }

    public function setNumber(?string $number): Card
    {
        $this->number = $number;
        return $this;
    }

    public function getValue(): ?float
    {
        return $this->value;
    }

    public function setValue(?float $value): Card
    {
        $this->value = $value;
        return $this;
    }
}
