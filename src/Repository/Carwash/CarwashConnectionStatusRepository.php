<?php

namespace App\Repository\Carwash;

use App\Entity\Carwash;
use App\Entity\Carwash\CarwashConnectionStatus;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CarwashConnectionStatus>
 *
 * @method CarwashConnectionStatus|null find($id, $lockMode = null, $lockVersion = null)
 * @method CarwashConnectionStatus|null findOneBy(array $criteria, array $orderBy = null)
 * @method CarwashConnectionStatus[]    findAll()
 * @method CarwashConnectionStatus[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CarwashConnectionStatusRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CarwashConnectionStatus::class);
    }

    public function add(CarwashConnectionStatus $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(CarwashConnectionStatus $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function update(Carwash $carwash, string $channel, bool $status, ?\DateTimeInterface $time = null)
    {
        if ($status) {
            $values = implode(",", [
                $carwash->getId(),
                '"' . $channel . '"',
                1,
                '"' . date_format($time, 'Y.m.d H:i:s') . '"'
            ]);
            $sql = "
     INSERT INTO carwash_connection_status (
             `carwash_id`,
             `channel`,
             `status`, 
             `last_contact`
             ) 
             VALUES ($values) 
     ON DUPLICATE KEY UPDATE
             `channel` = values(channel),
             `status` = values(status),
             `last_contact` = GREATEST(VALUES(last_contact), COALESCE(last_contact, '1900-01-01 00:00:00'))
             ;
 ";
        } else {
            $values = implode(",", [
                $carwash->getId(),
                '"' . $channel . '"',
                0
            ]);
            $sql = "
     INSERT INTO carwash_connection_status (
             `carwash_id`,
             `channel`,
            `status`             
             ) 
             VALUES ($values) 
     ON DUPLICATE KEY UPDATE
             `channel` = values(channel),
             `status` = values(status)             
             ;
 ";
        }


        $em = $this->getEntityManager();
        //echo $sql;
        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();
        return;
    }

//    /**
//     * @return CarwashConnectionStatus[] Returns an array of CarwashConnectionStatus objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('c.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?CarwashConnectionStatus
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
