<?php

namespace App\Tests\Owner;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class LoyaltyListTest extends WebTestCase
{
    const ownerId = 36814;

    private KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
        //$kernel = self::bootKernel();
        $this->apiLogin();
    }

    private function apiLogin()
    {
        $this->client->request(
            'POST',
            "/token",
            [
                "grant_type" => "client_credentials",
                "client_id" => "client_api_test",
                "client_secret" => "api_secret"
            ]
        );
        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        $token = json_decode($this->client->getResponse()->getContent(), true)['access_token'];

        $this->client->setServerParameter('HTTP_Authorization', 'Bearer ' . $token);
    }

    public function testGetCards()
    {

        $this->apiLogin();

        // Simulate a request to the getCards route
        $this->client->request('GET', '/api/owner/'. self::ownerId . '/cards');

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $responseContent = $this->client->getResponse()->getContent();
        $responseData = json_decode($responseContent, true);

        // Basic assertion to check if we received cards in the response
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('id', $responseData['data'][0]); // Assuming each card has an 'id'
    }

    public function testGetTransactions()
    {

        // Simulate a request to the getCards route
        $this->client->request('GET', '/api/owner/'. self::ownerId . '/transactions');

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $responseContent = $this->client->getResponse()->getContent();
        $responseData = json_decode($responseContent, true);
        // Basic assertion to check if we received cards in the response
        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('id', $responseData['data'][0]); // Assuming each card has an 'id'
    }
}
