<?php

namespace App\Service\Email;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Address;
use Twig\Environment;
use Symfony\Component\HttpFoundation\File\File;

class EmailSender
{
    public function __construct(
        private readonly MailerInterface $mailer,
        private readonly Environment $twig,
        private readonly ParameterBagInterface $parameterBag
    ) {
    }

    /**
     * @param File[] $attachments
     */
    public function sendHtml(
        string $to,
        string $subject,
        string $templateName,
        string $replyTo = null,
        array $context = [],
        array $attachments = [],
        array $cc = []
    ): void {
        $htmlContent = $this->twig->render($templateName, $context);

        $from = $this->parameterBag->get('mailer_from');
        $appName = $this->parameterBag->get('app_name');

        $email = (new Email())
            ->from(new Address($from, $appName))
            ->to($to)
            ->replyTo($replyTo ?? $from)
            ->addBcc('<EMAIL>')
            ->subject($subject)
            ->html($htmlContent);

        foreach ($cc as $ccEmail) {
            $email->addCc($ccEmail);
        }

        // Przetwarzanie załączników
        foreach ($attachments as $attachment) {
                $email->attachFromPath(
                    $attachment->getPathname(),  // Pobranie pełnej ścieżki pliku
                    $attachment->getBasename(),  // Pobranie nazwy pliku
                    $attachment->getMimeType()   // Pobranie typu MIME
                );
        }

        $this->mailer->send($email);
    }

    public function sendMail(
        string $to,
        string $from,
        string $subject,
        string $content,
    ): void {

        $email = (new Email())
            ->from($this->parameterBag->get('mailer_from'))
            ->replyTo($from)
            ->to($to)
            ->subject($subject)
            ->text($content);


        $this->mailer->send($email);
    }
}
