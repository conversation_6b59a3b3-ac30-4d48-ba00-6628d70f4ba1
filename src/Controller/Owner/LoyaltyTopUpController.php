<?php

namespace App\Controller\Owner;

use App\Entity\Loyalty\Enum\TopUpStatus;
use App\Entity\Loyalty\Enum\TopUpType;
use App\Entity\Loyalty\TopUp;
use App\Model\Date\DateTimeNullableRangeRequest;
use App\Model\Loyalty\NewInvoice;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\ClientsRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Service\Loyalty\InvoiceTopUpService;
use App\Service\SyncData\SyncDataService;
use Doctrine\ORM\EntityManagerInterface;
use I2m\StandardTypes\Enum\Source;
use I2m\Invoices\Enum\PaymentType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

class LoyaltyTopUpController extends AbstractController
{
    #[Route('/api/owner/{ownerId}/top_ups', methods: ['GET'])]
    public function getList(
        int $ownerId,
        Request $request,
        TopUpRepository $topUpRepository,
        SyncDataService $syncDataService,
        CardsRepository $cardsRepository,
        ClientsRepository $clientsRepository
    ): JsonResponse {

        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $email = $request->query->get('email');
        $cardNumber = $request->query->get('card');
        $cardToken = $request->query->get('cardToken');
        $clientId = $request->query->getInt('clientId');
        $client = $clientId ? $clientsRepository->find($clientId) : null;
        $page = $request->query->get('page', 1);
        $itemsPerPage = $request->query->get('perPage', 50);
        $orderBy = $request->query->get('orderBy', 'ctime');
        $orderDir = $request->query->get('orderDir', 'DESC');
        $search = $request->query->get('search');
        $sources = $request->get('source') ? explode(',', $request->get('source')) : null;
        $types = $request->get('type') ? explode(',', $request->get('type')) : null;
        $statuses = $request->get('status') ? explode(',', $request->get('status')) : null;
        $hasInvoice = $request->get('invoiceExists');
        $showTotal = $request->query->getBoolean('showTotal', true);

        // Check for invalid pagination and ordering
        if (!is_numeric($page) || $page < 1 || !is_numeric($itemsPerPage) || $itemsPerPage < 1) {
            return $this->json(['error' => 'Invalid pagination parameters'], JsonResponse::HTTP_BAD_REQUEST);
        }
        if (!in_array($orderDir, ['ASC', 'DESC'])) {
            return $this->json(['error' => 'Invalid order direction'], JsonResponse::HTTP_BAD_REQUEST);
        }

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get('timezone')
        );

        $card = null;
        if ($cardNumber) {
            $card = $cardsRepository->getCardByNumber($cardNumber, $owner);
            if (!$card) {
                return $this->json(
                    ['error' => "Card by number not found"],
                    JsonResponse::HTTP_NOT_FOUND
                );
            }
        } elseif ($cardToken) {
            $card = $cardsRepository->getCardByToken($cardToken, $email);
            if (!$card) {
                return $this->json(
                    ['error' => "Card by token not found"],
                    JsonResponse::HTTP_NOT_FOUND
                );
            }
        }

        $topUps = $topUpRepository->getHistory(
            $owner,
            $card,
            $dateRange->getDateFrom(),
            $dateRange->getDateTo(),
            $page,
            $itemsPerPage,
            $types,
            $sources,
            $statuses,
            $orderBy,
            $orderDir,
            $search,
            $hasInvoice,
            $client,
            $showTotal
        );

        return $this->json($topUps, JsonResponse::HTTP_OK, [], [
            'groups' => [
                "loyalty:topup:list",
                "loyalty:cards:client",
                "default:basic",
            ],
            'datetime_format' => 'Y-m-d H:i:s',
            'timezone' => new \DateTimeZone($request->get('timezone', 'GMT-0'))
        ]);
    }

    #[Route('/api/owner/{ownerId}/top_ups/bulk', methods: ['POST'])]
    public function newBulkTopUp(
        int $ownerId,
        Request $request,
        CardsRepository $cardsRepository,
        SyncDataService $syncDataService,
        EntityManagerInterface $em,
    ) {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $topupsData = json_decode($request->getContent(), true);

        $errors = [];
        foreach ($topupsData as $topUpData) {
            $cardNumber = $topUpData['cardNumber'];
            $lineInCsv = $topUpData['lineInCsv'];

            $card = $cardsRepository->getCardByNumber($cardNumber, $owner);

            if (is_null($card)) {
                $errors[] = $this->getError(
                    $lineInCsv,
                    "Card not found",
                    $topUpData
                );
                continue;
            }

            if (!empty($errors)) {
                continue;
            }

            $topUp = (new TopUp())
                ->setCard($card)
                ->setOwner($card->getOwner())
                ->setCurrency($card->getCurrency())
                ->setType(TopUpType::from($topUpData['type']))
                ->setSource(Source::from($topUpData['source']))
                ->setTopUpValue($topUpData['value'])
                ->setValue($topUpData['value'])
                ->setAddedBy($topUpData['email'])
            ;

            $em->persist($topUp);
        }

        if (empty($errors)) {
            $em->flush();
            return $this->json([]);
        }

        return $this->json(['errors' => $errors], JsonResponse::HTTP_BAD_REQUEST);
    }

    private function getError(
        int $line,
        string $description,
        array $topUpdate,
    ): array {
        return [
            'place' => "line: $line",
            'description' => $description,
            'content' => $topUpdate['cardNumber'] ? "cardNumber: {$topUpdate['cardNumber']}" : '',
        ];
    }

    #[Route('/api/owner/{ownerId}/top_up/{id}', methods: ['GET'])]
    public function getItem(
        int $ownerId,
        TopUpRepository $topUpRepository,
        SyncDataService $syncDataService,
        int $id,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $topUp = $topUpRepository->findOneBy(
            [
                'owner' => $owner,
                'id' => $id
            ]
        );

        return $this->json($topUp, Response::HTTP_OK, [], [
            'groups' => [
                "loyalty:topup:list",
                "loyalty:cards:client",
                "default:basic",
            ],
        ]);
    }

    #[Route('/api/owner/{ownerId}/top_up/{id}/invoice', methods: ['POST'])]
    public function newInvoice(
        int $ownerId,
        TopUpRepository $topUpRepository,
        SyncDataService $syncDataService,
        InvoiceTopUpService $invoiceTopUpService,
        Request $request,
        int $id,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $topUp = $topUpRepository->findOneBy(
            [
                'owner' => $owner,
                'id' => $id
            ]
        );
        $invoiceTopUpService->generateSingle(
            $topUp,
            PaymentType::from($request->request->get('paymentMethod')),
            $request->request->get('paymentTerm'),
            $request->request->get('description')
        );

        return $this->json($topUp, Response::HTTP_OK, [], [
            'groups' => [
                "loyalty:topup:list",
                "loyalty:cards:client",
                "default:basic",
            ],
        ]);
    }

    #[Route('/api/owner/{ownerId}/top_up/{id}', methods: ['PUT'])]
    public function editSingle(
        int $ownerId,
        Request $request,
        TopUpRepository $topUpRepository,
        SyncDataService $syncDataService,
        SerializerInterface $serializer,
        int $id
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $topUp = $topUpRepository->findOneBy(
            [
                'owner' => $owner,
                'id' => $id
            ]
        );

        $serializer->deserialize(
            $request->getContent(),
            TopUp::class,
            'json',
            [
                'object_to_populate' => $topUp,
                'groups' =>
                    [
                        'loyalty:topup:edit'
                    ]
            ]
        );

        $topUpRepository->save($topUp);

        return $this->json($topUp, Response::HTTP_OK, [], [
            'groups' => [
                "loyalty:topup:list",
                "loyalty:cards:client",
                "default:basic",
            ],
        ]);
    }

    #[Route('/api/owner/{ownerId}/top_up/{token}/register', methods: ['POST'])]
    public function register(
        int $ownerId,
        TopUpRepository $topUpRepository,
        SyncDataService $syncDataService,
        string $token
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $topUp = $topUpRepository->getByToken($token, TopUpStatus::INITIATED);

        if (is_null($topUp)) {
            return $this->json(['error' => "top up not found"], Response::HTTP_NOT_FOUND);
        }

        if (!is_null($topUp->getOwner())) {
            return $this->json(['error' => "top up already registered"], Response::HTTP_CONFLICT);
        }

        if ($owner->getCurrency() != $topUp->getCurrency()) {
            return $this->json(['error' => "owner and topUp currency conflict"], Response::HTTP_CONFLICT);
        }

        $topUp
            ->setOwner($owner)
            ->setStatus(TopUpStatus::ACTIVE)
        ;
        $topUpRepository->save($topUp);

        return $this->json($topUp, Response::HTTP_OK, [], [
            'groups' => [
                "loyalty:topup:list",
                "loyalty:cards:client",
                "default:basic",
            ],
        ]);
    }
}
