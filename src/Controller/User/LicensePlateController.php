<?php

namespace App\Controller\User;

use App\Entity\LicensePlate;
use App\Entity\User;
use App\Repository\LicensePlateRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

class LicensePlateController extends AbstractController
{
    #[Route("/api/users/license_plates", methods: ['GET'])]
    public function listAction(LicensePlateRepository $repository): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        return $this->json($user->getLicensePlates(), Response::HTTP_OK, [], ['groups' =>  "default:basic"]);
    }
    #[Route("/api/users/license_plates", methods: ['POST'])]
    public function addAction(
        Request $request,
        SerializerInterface $serializer,
        EntityManagerInterface $em
    ): Response {
        /** @var User $user */
        $user = $this->getUser();

        /** @var LicensePlate $licensePlate */
        $licensePlate = $serializer->deserialize(
            $request->getContent(),
            LicensePlate::class,
            'json',
        );

        $em->persist($licensePlate);
        $user->addLicensePLate($licensePlate);
        $em->persist($user);
        $em->flush();

        return $this->json($licensePlate, Response::HTTP_OK, [], ['groups' =>  "default:basic"]);
    }

    #[Route("/api/users/license_plate/{id}", methods: ['POST'])]
    public function editAction(
        LicensePlate $licensePlate,
        Request $request,
        SerializerInterface $serializer,
        EntityManagerInterface $em,
        LicensePlateRepository $lpRepository
    ): Response {

        /** @var User $user */
        $user = $this->getUser();
        // tylko użytkownik który jest właścicielem tablicy, lub jego manager mogą edytować rejesracje
        if (!in_array($licensePlate->getUser(), [$user])) {
            return $this->json([], Response::HTTP_FORBIDDEN);
        }

        $serializer->deserialize(
            $request->getContent(),
            LicensePlate::class,
            'json',
            [
                AbstractNormalizer::OBJECT_TO_POPULATE => $licensePlate,
            ],
        );

        $em->persist($licensePlate);
        $em->flush();

        return $this->json([], Response::HTTP_OK, [], ['groups' =>  "default:basic"]);
    }

    #[Route("/api/users/license_plate/{id}", methods: ['GET'])]
    public function returnAction(
        LicensePlate $licensePlate
    ): Response {

        /** @var User $user */
        $user = $this->getUser();
        // tylko użytkownik który jest właścicielem tablicy, lub jego manager mogą widziec
        if (!in_array($licensePlate->getUser(), [$user])) {
            return $this->json([], Response::HTTP_FORBIDDEN);
        }

        return $this->json($licensePlate, Response::HTTP_OK, [], ['groups' =>  "default:basic"]);
    }
}
