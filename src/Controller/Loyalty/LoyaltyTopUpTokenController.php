<?php

namespace App\Controller\Loyalty;

use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Service\SyncData\SyncDataService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class LoyaltyTopUpTokenController extends AbstractController
{
    #[Route('/api/loyalty/topup/{token}', methods: ['GET'])]
    public function getTopUp(
        string $token,
        Request $request,
        TopUpRepository $topUpRepository
    ) {
        $ownerId = $request->query->getInt('owner');
        $topUp = $topUpRepository->getByToken($token);
        $cvv = $request->query->getInt('cvv');

        if (is_null($topUp)) {
            return $this->json(["error" => "top up not found" ], JsonResponse::HTTP_NOT_FOUND);
        }
        if ($cvv && $topUp->getCvv() != $cvv) {
            return $this->json(["error" => "wrong cvv" ], JsonResponse::HTTP_FORBIDDEN);
        }

        if (
            $ownerId &&
            ($topUp->getCard()->getOwner()->getId() != $ownerId)
        ) {
            return $this->json(["error" => "wrong owner" ], JsonResponse::HTTP_BAD_REQUEST);
        }

        return $this->json($topUp, JsonResponse::HTTP_OK, [], [
            'groups' => [
                "loyalty:topup:list",
                "default:basic",
            ],
            'datetime_format' => 'Y-m-d H:i:s',
            'currency' => $topUp->getCurrency(),
            'timezone' => new \DateTimeZone($request->get('timezone', 'GMT-0'))
        ]);
    }

    #[Route('/api/loyalty/topup/{token}', methods: ['PUT'])]
    public function updateTopUp(
        string $token,
        Request $request,
        TopUpRepository $topUpRepository,
        CardsRepository $cardsRepository,
        SyncDataService $syncDataService
    ) {
        $topUp = $topUpRepository->getByToken($token);
        $cvv = $request->query->getInt('cvv');

        if (is_null($topUp)) {
            return $this->json(["error" => "top up not found" ], JsonResponse::HTTP_NOT_FOUND);
        }
        if ($cvv && $topUp->getCvv() != $cvv) {
            return $this->json(["error" => "wrong cvv" ], JsonResponse::HTTP_FORBIDDEN);
        }
        $data = json_decode($request->getContent(), true);

        if (isset($data['owner']['id'])) {
            $owner = $syncDataService->getOwner($data['owner']['id']);
            $topUp->setOwner($owner);
        }

        if (is_null($topUp->getCard()) && isset($data['card']['cardToken'])) {
            $card = $cardsRepository->getCardByToken($data['card']['cardToken']);
            $topUp->setCard($card);
        }

        $topUpRepository->save($topUp);

        return $this->json($topUp, JsonResponse::HTTP_OK, [], [
            'groups' => [
                "loyalty:topup:list",
                "default:basic",
            ],
            'datetime_format' => 'Y-m-d H:i:s',
            'timezone' => new \DateTimeZone($request->get('timezone', 'GMT-0'))
        ]);
    }
}
