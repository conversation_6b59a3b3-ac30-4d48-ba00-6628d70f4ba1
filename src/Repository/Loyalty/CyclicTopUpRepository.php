<?php

namespace App\Repository\Loyalty;

use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\CyclicTopUp;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Owners;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CyclicTopUp>
 *
 * @method CyclicTopUp|null find($id, $lockMode = null, $lockVersion = null)
 * @method CyclicTopUp|null findOneBy(array $criteria, array $orderBy = null)
 * @method CyclicTopUp[]    findAll()
 * @method CyclicTopUp[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CyclicTopUpRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CyclicTopUp::class);
    }

    public function add(CyclicTopUp $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(CyclicTopUp $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return CyclicTopUp[] Returns an array of CyclicTopUp objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('c.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?CyclicTopUp
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
    public function getList(
        ?Owners $owner = null,
        ?Cards $card = null,
        ?int $page = 1,
        ?int $perPage = 10,
        ?string $search = null,
        ?bool $isActive = null
    ) {

        $queryBuilder = $this->createQueryBuilder('t')
            ->leftJoin('t.card', 'card')
        ;
        $owner = $owner ?? $card?->getOwner();
        if ($owner) {
            $queryBuilder
                ->andWhere('card.owner = :owner')
                ->setParameter('owner', $owner);
        }

        if ($card) {
            $queryBuilder
                ->andWhere('t.card = :card')
                ->setParameter('card', $card);
        }

        if (!is_null($isActive)) {
            $queryBuilder
                ->andWhere('t.isActive = :isActive')
                ->setParameter('isActive', $isActive);
        }

        if ($search) {
            $orX = $queryBuilder->expr()->orX();

            $orX->add($queryBuilder->expr()->like('LOWER(card.number)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(card.email)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(card.alias)', ':search'));

            $queryBuilder->andWhere($orX)
                ->setParameter('search', '%' . strtolower($search) . '%');
        }

        $queryBuilder
            ->andWhere('card.status != :cardStatus')
            ->setParameter('cardStatus', CardStatus::DELETED);

        $countQueryBuilder = clone $queryBuilder;
        $total = $countQueryBuilder->select('COUNT(t)')->getQuery()->getSingleScalarResult();


        $qb = $queryBuilder->setFirstResult(($page - 1) * $perPage)
            ->setMaxResults($perPage);
        ;


        return [
            'data' => $qb->getQuery()->getResult(),
            'total' => $total
        ];
    }

    public function getConfig2(
        Owners $owner = null,
        int $configId = null,
    ): CyclicTopUp {
        $queryBuilder = $this->createQueryBuilder('t')
            ->leftJoin('t.card', 'card')
        ;

        $queryBuilder
            ->andWhere('card.owner = :owner')
            ->setParameter('owner', $owner)
            ->andWhere('t.id = :id')
            ->setParameter('id', $configId);

        return $queryBuilder->getQuery()->getResult()[0];
    }

    public function save(CyclicTopUp $cyclicTopUp)
    {
        $this->getEntityManager()->persist($cyclicTopUp);
        $this->getEntityManager()->flush();
    }
}
