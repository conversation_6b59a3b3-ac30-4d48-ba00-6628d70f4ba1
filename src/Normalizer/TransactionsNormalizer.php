<?php

namespace App\Normalizer;

use App\Entity\Loyalty\Transactions;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class TransactionsNormalizer implements NormalizerInterface
{
    public function __construct(
        private ObjectNormalizer $normalizer
    ) {
    }

    public function normalize($object, string $format = null, array $context = [])
    {
        /** @var Transactions $object */
        $object->setTime($this->getLocalTime($object->getTime(), $context['timezone'] ?? null));

        $data = $this->normalizer->normalize($object, $format, $context);
        return $data;
    }

    public function supportsNormalization($data, string $format = null, array $context = [])
    {
        return  ($data instanceof Transactions);
    }

    public function getLocalTime(?\DateTimeInterface $utcTime, ?\DateTimeZone $timeZone = null): ?\DateTimeInterface
    {
        if ($timeZone && $utcTime) {
            $ct = \DateTime::createFromInterface($utcTime);
            return $ct->setTimezone($timeZone);
        }
        return $utcTime;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            Transactions::class => true,
        ];
    }
}
