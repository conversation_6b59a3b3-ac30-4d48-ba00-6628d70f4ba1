<?php

namespace App\Entity\Carwash;

use App\Entity\Carwash;
use App\Repository\Carwash\EdgeDeviceRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\StandardTypes\Enum\EdgeDeviceType;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Table]
#[ORM\UniqueConstraint(columns: ['type', 'uid'])]
#[ORM\UniqueConstraint(columns: ['mac'])]
#[ORM\Entity(repositoryClass: EdgeDeviceRepository::class)]
#[ORM\HasLifecycleCallbacks]
class EdgeDevice
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private int $id;

    #[ORM\ManyToOne(targetEntity: Carwash::class)]
    private ?Carwash $carwash;

    #[ORM\Column(type: 'string', length: 255)]
    private string $uid;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $mac;

    #[ORM\Column(type: 'string', length: 16, nullable: false, enumType: EdgeDeviceType::class)]
    private EdgeDeviceType $type = EdgeDeviceType::UNKNOWN;

    #[ORM\Column(name: 'ctime', type: 'datetime')]
    private ?\DateTime $ctime = null;

    #[ORM\Column(name: 'mtime', type: 'datetime', nullable: true)]
    private ?\DateTime $mtime = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    private ?string $software;

    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUid(): ?string
    {
        return $this->uid;
    }

    public function setUid(string $uid): self
    {
        $this->uid = $uid;

        return $this;
    }

    public function getMac(): ?string
    {
        return $this->mac;
    }

    public function setMac(?string $mac): self
    {
        $this->mac = $mac;

        return $this;
    }

    public function getCarwash(): ?Carwash
    {
        return $this->carwash;
    }

    public function setCarwash(?Carwash $carwash): self
    {
        $this->carwash = $carwash;

        return $this;
    }

    public function getType(): EdgeDeviceType
    {
        return $this->type;
    }

    public function setType(EdgeDeviceType $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getCtime(): ?\DateTime
    {
        return $this->ctime;
    }

    public function setCtime(\DateTime $ctime): self
    {
        $this->ctime = $ctime;
        return $this;
    }

    public function getMtime(): \DateTime
    {
        return $this->mtime;
    }

    public function setMtime(\DateTime $mtime): self
    {
        $this->mtime = $mtime;
        return $this;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps()
    {
        $this->setMtime(new \DateTime('now'));
        if ($this->getCtime() == null) {
            $this->setCtime(new \DateTime('now'));
        }
    }

    public function getSoftware(): ?string
    {
        return $this->software;
    }

    public function setSoftware(?string $software): void
    {
        $this->software = $software;
    }
}
