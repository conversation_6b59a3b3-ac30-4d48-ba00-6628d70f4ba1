{% extends 'base_email.html.twig' %}

{% block body %}
<tr>
    <td style="padding: 50px 0px 0px 0px; background-color: #fff; color: #000">
        <form id="password-reset-form" action="{{ path('password_reset_confirm') }}" method="post" style="max-width: 500px; margin: 0 auto;">
            <input type="hidden" name="token" value="{{ token }}">

            <div style="margin-bottom: 20px;">
                <label for="password" style="display: block; font-weight: bold; margin-bottom: 5px;">
                    {{ 'password_reset.new_password'|trans }}
                </label>
                <input type="password" id="password" name="password" required minlength="8" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
                <div id="password-error" style="color: red; font-size: 12px; display: none;"></div>
            </div>

            <div style="margin-bottom: 40px;">
                <label for="confirm_password" style="display: block; font-weight: bold; margin-bottom: 5px;">
                    {{ 'password_reset.confirm_password'|trans }}
                </label>
                <input type="password" id="confirm_password" name="confirm_password" required style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
                <div id="confirm-password-error" style="color: red; font-size: 12px; display: none;"></div>
            </div>

            <div style="text-align: center;">
                <button type="submit" onclick="return validateForm();" style="background-color: {{ primaryColor }}; color: white; padding: 10px 20px; border: none; border-radius: 5px; text-decoration: none; cursor: pointer;">
                    {{ 'password_reset.reset_password'|trans }}
                </button>
            </div>
        </form>
    </td>
</tr>

<script>
    function validateForm() {
    var password = document.getElementById('password').value;
    var confirmPassword = document.getElementById('confirm_password').value;
    var passwordError = document.getElementById('password-error');
    var confirmPasswordError = document.getElementById('confirm-password-error');

    // Clear previous error messages
    passwordError.style.display = 'none';
    confirmPasswordError.style.display = 'none';
    passwordError.textContent = '';
    confirmPasswordError.textContent = '';

    var isValid = true;

    // Check if password contains invalid characters (e.g., spaces)
    if (/\s/.test(password)) {
        passwordError.style.display = 'block';
        passwordError.textContent = '{{ "password_reset.invalid_characters"|trans }}';
        isValid = false;
    }

    // Check if password is at least 8 characters
    if (password.length < 8) {
        passwordError.style.display = 'block';
        passwordError.textContent = '{{ "password_reset.error_min_length"|trans }}';
        isValid = false;
    }

    // Check if passwords match
    if (password !== confirmPassword) {
        confirmPasswordError.style.display = 'block';
        confirmPasswordError.textContent = '{{ "password_reset.passwords_must_match"|trans }}';
        isValid = false;
    }

    return isValid;
}
</script>
{% endblock body %}