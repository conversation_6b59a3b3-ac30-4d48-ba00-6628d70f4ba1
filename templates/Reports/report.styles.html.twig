<style>
    thead { display: table-header-group;}
    tr { page-break-inside: avoid; }
    .bold { font-weight: 600; }
    .bold-2 { font-weight: 800; }
    .table-subscriber td { padding: 4px; }
    .pdf-table td, th { border-radius: 0; }
    body {
        margin: 0;
        position: relative;
    }
    .row .col, html {  box-sizing: border-box; }
    table, td, th { border: none; }
    table {
        width: 100%;
        display: table;
        border-collapse: collapse;
    }
    td, th {
        padding: 15px 5px;
        display: table-cell;
        text-align: left;
        vertical-align: middle;
        border-radius: 0;
    }
    .left-align, .align-left { text-align: left;}
    .right-align, .align-right { text-align: right;}
    .row .col {
        float: left;
        padding: 0 0;
    }
    .row .col.s1 { width: 8.33333%; }
    .row .col.s2 { width: 16.66667%; }
    .row .col.s3 { width: 25%; }
    .row .col.s4 { width: 33.33333%; }
    .row .col.s5 { width: 41.66667%; }
    .row .col.s6 {
        width: 50%;
        margin-left: auto;
        left: auto;
        right: auto;
    }
    .row .col.s7 { width: 58.33333%; }
    .row .col.s8 { width: 66.66667%; }
    .row .col.s9 { width: 75%; }
    .row .col.s10 { width: 83.33333%; }
    .row .col.s11 { width: 91.66667%; }
    .row .col.s12 {  width: 100%; }
    html {
        font-family: Roboto, sans-serif;
        font-weight: 400;
        font-size: 12px;
        line-height: 1.5;
        color: rgba(0, 0, 0, 0.87);
        -ms-text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%;
        min-width: 300px;
    }
    .h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
        font-weight: 400;
        letter-spacing: 0.02rem;
        clear: both;
    }
    .h1, h1 {
        font-size: 3rem;
        line-height: 3.3rem;
        margin: 1.2rem 0;
    }
    .h2, h2 {
        font-size: 2.5rem;
        line-height: 2.75rem;
        margin: 1rem 0;
    }
    .h3, h3 {
        font-size: 2rem;
        line-height: 2.2rem;
        margin: 0.8rem 0;
    }
    .h4, h4 {
        font-size: 1.5rem;
        line-height: 1.65rem;
        margin: 0.6rem 0;
    }
    .h5, h5 {
        font-size: 1.166rem;
        line-height: 1.2826rem;
        margin: 0.4664rem 0;
    }
    .h6, h6 {
        font-size: 1rem;
        line-height: 1.1rem;
        margin: 0.4rem 0;
    }
    .table {
        margin-bottom: 20px;
        width: 100%;
        max-width: 100%;
    }
    .table > tbody > tr > td,
    .table > tbody > tr > th,
    .table > thead > tr > td,
    .table > thead > tr > th {
        padding: 8px;
        line-height: 1.42857143;
        vertical-align: middle;
        border-top: 1px solid #ddd;
        border-left: 0;
    }
    .table > thead > tr > th {
        vertical-align: bottom;
        border-bottom: 2px solid #ddd;
        border-left: 0;
    }
    .thumbnail-200 {
        max-height: 200px;
        max-width: 200px;
    }
    .page-break {
        page-break-after: always;
    }
    .left {
        float: left !important;
    }
    .right {
        float: right !important;
    }
    .row .col {
        float: left;
    }
    .table-header {
        background-color: #e40613;
        color: white;
    }
</style>