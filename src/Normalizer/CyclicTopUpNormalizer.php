<?php

namespace App\Normalizer;

use App\Entity\Currency;
use App\Entity\Loyalty\CyclicTopUp;
use App\Entity\TopUp;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class CyclicTopUpNormalizer implements NormalizerInterface
{
    public function __construct(
        private ObjectNormalizer $normalizer
    ) {
    }

    public function normalize($object, string $format = null, array $context = [])
    {
        /** @var CyclicTopUp $object */
        $object->setCtime($this->getLocalTime($object->getCtime(), $context['timezone'] ?? null));
        $data = $this->normalizer->normalize($object, $format, $context);
        return $data;
    }

    public function supportsNormalization($data, string $format = null, array $context = []): bool
    {
        return  ($data instanceof CyclicTopUp);
    }

    public function getLocalTime(?\DateTimeInterface $utcTime, ?\DateTimeZone $timeZone = null): ?\DateTimeInterface
    {
        if ($timeZone && $utcTime) {
            $ct = \DateTime::createFromInterface($utcTime);
            return $ct->setTimezone($timeZone);
        }
        return $utcTime;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            CyclicTopUp::class => true,
        ];
    }
}
