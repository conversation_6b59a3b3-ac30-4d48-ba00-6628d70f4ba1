<?php

declare(strict_types=1);

namespace App\Repository\Loyalty;

use App\Entity\Carwash;
use App\Entity\Currency;
use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Loyalty\Enum\CardType;
use App\Entity\Loyalty\Enum\TransactionType;
use App\Entity\Loyalty\Transactions;
use App\Entity\Owners;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use I2m\StandardTypes\Enum\Source;

/**
 * @method Transactions|null find($id, $lockMode = null, $lockVersion = null)
 * @method Transactions|null findOneBy(array $criteria, array $orderBy = null)
 * @method Transactions[]    findAll()
 * @method Transactions[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TransactionsRepository extends ServiceEntityRepository
{
    public function __construct(
        ManagerRegistry $registry,
    ) {
        parent::__construct($registry, Transactions::class);
    }

    public function save(Transactions $transaction): Transactions
    {
        $this->getEntityManager()->persist($transaction);
        $this->getEntityManager()->flush();
        return $transaction;
    }

    public function calculateBalance(Cards $card): float
    {
         $result = $this->createQueryBuilder('t')
            ->select("sum(t.value)")
            ->where("t.card = :card")
            ->setParameter('card', $card)->getQuery()->getSingleScalarResult();
          return  (float)$result;
    }

    public function getHistory(
        ?Owners $owner = null,
        ?Cards $card = null,
        ?\DateTimeInterface $date_from = null,
        ?\DateTimeInterface $date_to = null,
        ?int $page = 1,
        ?int $perPage = 10,
        ?array $types = null,
        ?array $sources = null,
        string $orderBy = 'time',
        string $orderDir = 'DESC',
        ?string $search = null,
        ?bool $showTotal = null,
        ?bool $hasAlias = null,
        ?bool $isVirtual = null,
        ?array $sn = null,
    ) {

        if (is_null($owner) && is_null($card)) {
            return null;
        }

        $queryBuilder = $this->createQueryBuilder('t')
            ->leftJoin('t.card', 'card')
            ->leftJoin('card.client', 'client')
            ->leftJoin('t.carwash', 'carwash')
            ->addSelect('card')
            ->addSelect('client')
            ->addSelect('carwash')
        ;
        //
        $queryBuilder
            ->andWhere('t.type NOT IN (:excludeTypes)')
            ->setParameter('excludeTypes', [
                TransactionType::INITIAL
            ]);

        // $owner = $owner ?? $card?->getOwner();
        if ($owner) {
            $queryBuilder
                ->andWhere('t.owner = :owner')
                ->setParameter('owner', $owner);
        }

        if ($hasAlias === true) {
            $queryBuilder
                ->andWhere('LENGTH(card.alias) > 0');
        } elseif ($hasAlias === false) {
            $queryBuilder
                ->andWhere('(LENGTH(card.alias) = 0) or card.alias is null ');
        }

        if ($card) {
            $queryBuilder
                ->andWhere('t.card = :card')
                ->setParameter('card', $card);
        }

        if ($date_from !== null) {
            $queryBuilder->andWhere('t.time >= :date_from')
                ->setParameter('date_from', $date_from);
        }
        if ($date_to !== null) {
            $queryBuilder->andWhere('t.time <= :date_to')
                ->setParameter('date_to', $date_to);
        }

        if ($types) {
            $queryBuilder
                ->andWhere('t.type IN (:types)')
                ->setParameter('types', $types);
        }

        if ($sn) {
            $queryBuilder
                ->andWhere('carwash.sn IN (:sn)')
                ->setParameter('sn', $sn);
        }

        if ($sources) {
            $queryBuilder
                ->andWhere('t.source IN (:sources)')
                ->setParameter('sources', $sources);
        }

        if ($isVirtual === true) {
            $queryBuilder
                ->andWhere('card.type = :type')
                ->setParameter('type', CardType::VIRTUAL);
        }

        if ($search) {
            $orX = $queryBuilder->expr()->orX();

            $orX->add($queryBuilder->expr()->like('LOWER(card.number)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(card.email)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(card.alias)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(client.companyName)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(client.taxNumber)', ':search'));

            $queryBuilder->andWhere($orX)
                ->setParameter('search', '%' . strtolower($search) . '%');
        }

        $queryBuilder
            ->andWhere('card.status != :cardStatus')
            ->setParameter('cardStatus', CardStatus::DELETED);

        // zapytanie o całkowitą ilość rekordów jest bardzo długie
        // więc dajemy możliwość wyłączenia
        if ($showTotal) {
            $countQueryBuilder = clone $queryBuilder;
            $total = $countQueryBuilder->select('COUNT(t)')->getQuery()->getSingleScalarResult();
        } else {
            $total = null;
        }

        $queryBuilder->orderBy("t.$orderBy", $orderDir);
        if ($page && $perPage) {
            $queryBuilder->setFirstResult(($page - 1) * $perPage)
                ->setMaxResults($perPage);
            ;
        }


        return [
            'data' => $queryBuilder->getQuery()->getResult(),
            'total' => $total,
            'type' => [
                TransactionType::ADDITION,
                TransactionType::SUBTRACTION,
                TransactionType::PROMOTION,
                TransactionType::ALIGNMENT
            ],
            'source' => Source::cases(),
        ];
    }

    public function getStats(
        Currency $currency,
        ?Cards $card = null,
        ?Owners $owner = null,
        ?\DateTimeInterface $from = null,
        ?\DateTimeInterface $to = null
    ): array {

        $query = $this->createQueryBuilder('t')
           ->groupBy('t.source, t.type')

           ->setParameter('rate', $currency->getRate())
        ;

        if ($card) {
            $query
                ->where('t.card = :card')
                ->setParameter('card', $card);
        }

        if ($owner) {
            $query
                ->leftJoin('t.card', "crd")
                ->where('t.owner = :owner')
                ->setParameter('owner', $owner);
        }

        if (isset($from, $to)) {
            $query->andWhere('t.time >= :dateFrom')
                ->andWhere('t.time <= :dateTo')
                ->setParameter('dateFrom', $from)
                ->setParameter('dateTo', $to);
        }

        $query->select(
            't.source as source,t.type as type, 
            SUM(t.value) * :rate as value',
        );
        $result = $query->getQuery()->getResult();

        $data = [
           'ADDITION' => [
               "CAR_WASH" => 0,
               "INTERNET" => 0,
               "MONEY_CHANGER" => 0,
               "VACUUM_CLEANER" => 0,
               "DISTRIBUTOR"  => 0
           ],
           'PROMOTION' => [
               "INTERNET" => 0,
               "MONEY_CHANGER" => 0,
           ],
           'SUBTRACTION' => [
               "INTERNET" => 0,
               "CAR_WASH" => 0,
               "VACUUM_CLEANER" => 0,
               "DISTRIBUTOR"  => 0
           ],
        ];

        foreach ($result as $item) {
            $type = $item['type']->value;
            $source = $item['source']->value;

            if (
                array_key_exists($type, $data) &&
                array_key_exists($source, $data[$type])
            ) {
                $data[$type][$source] = $item['value'];
            }
        }

        return $data;
    }
}
