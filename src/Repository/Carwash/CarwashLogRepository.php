<?php

namespace App\Repository\Carwash;

use App\Entity\Carwash;
use App\Entity\Carwash\CarwashLog;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

use function Sentry\captureMessage;

/**
 * @extends ServiceEntityRepository<CarwashLog>
 *
 * @method CarwashLog|null find($id, $lockMode = null, $lockVersion = null)
 * @method CarwashLog|null findOneBy(array $criteria, array $orderBy = null)
 * @method CarwashLog[]    findAll()
 * @method CarwashLog[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CarwashLogRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CarwashLog::class);
    }


    public function notice(Carwash $carwash, string $type, ?string $identifier, string $comment, bool $sentry = false)
    {
        $this->log($carwash, $type, $identifier, $comment, $sentry, "NOTICE");
    }

    public function error(Carwash $carwash, string $type, ?string $identifier, string $comment, bool $sentry = false)
    {
        $this->log($carwash, $type, $identifier, $comment, $sentry, "ERROR");
    }

    public function log(?Carwash $carwash, string $type, ?string $identifier, string $comment, bool $sentry = false, string $level = 'DEBUG'): void
    {
        $this->getEntityManager()->getConnection()->insert('carwash_log', [
            'carwash_id' => $carwash?->getId(),
            'type' => $type,
            'identifier' => $identifier,
            'comment' => $comment,
            'ctime' => (new \DateTime())->format('Y-m-d H:i:s'),
            'level' => $level,
        ]);
        if ($sentry) {
            captureMessage("Myjnia {$carwash->getSn()}, {$type}-{$identifier}: $comment");
        }
    }

    public function add(CarwashLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(CarwashLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return CarwashLog[] Returns an array of CarwashLog objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('c.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?CarwashLog
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }

    public function clearByTime(\DateTime $time)
    {
        $qbDelete = $this->createQueryBuilder('l')
            ->delete()
            ->andWhere('l.ctime < :time')
            ->setParameter('time', $time)
        ;


        return $qbDelete->getQuery()->execute();
    }
}
