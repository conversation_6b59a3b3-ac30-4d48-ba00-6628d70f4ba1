<?php

namespace App\Controller\User;

use App\Entity\User;
use App\Model\ClientApp\SupportRequest;
use App\Service\Email\OwnerEmailService;
use App\Service\Loyalty\VirtualCardManager;
use App\Service\Support\SupportService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoyaltyStandController extends AbstractController
{
    #[Route('/api/user/stand/{standCode}/cards', methods: ['POST'])]
    public function create(
        string $standCode,
        VirtualCardManager $virtualCardManager,
        OwnerEmailService $ownerEmailService,
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $email = $user->getEmail();

        $card = $virtualCardManager->createCardForBay($standCode, $email);
        if (is_null($card)) {
            return $this->json($card, JsonResponse::HTTP_NO_CONTENT);
        }

        $ownerEmailService->sendCardAddEmail($user, $card, $standCode);

        $context = [
            'groups' => [
                "loyalty:cards:user",
                "default:basic",
                "owner:logo",
                "owner:contact"
            ],
            'datetime_format' => 'Y-m-d H:i:s',
        ];

        return $this->json($card, JsonResponse::HTTP_CREATED, [], $context);
    }

    #[Route('/api/user/stand/{standCode}/cards', methods: ['GET'])]
    public function getCardForStand(
        string $standCode,
        VirtualCardManager $virtualCardManager,
        TranslatorInterface $translator
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $email = $user->getEmail();

        try {
            $cards = $virtualCardManager->getCardForBay(
                $standCode,
                $email
            );
        } catch (\Exception $exception) {
            return $this->json(['error' => $translator->trans('error_cards_fetch_failed'), 'code' => 1], JsonResponse::HTTP_BAD_GATEWAY);
        }


        $groups = [
            "loyalty:cards:user",
            "default:basic",
            "owner:logo",
            "owner:contact"
        ];


        return $this->json($cards, JsonResponse::HTTP_OK, [], ['groups' => $groups]);
    }

    #[Route('/api/user/stand/{standCode}/support', methods: ['POST'])]
    public function sendSupport(
        string $standCode,
        SupportService $supportService,
        #[MapRequestPayload] SupportRequest $supportRequest,
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $supportService->createTicketFromStand($user, $standCode, $supportRequest->getDescription());
        return $this->json([], JsonResponse::HTTP_OK);
    }
}
