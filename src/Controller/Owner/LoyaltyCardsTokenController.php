<?php

namespace App\Controller\Owner;

use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\CyclicTopUp;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Loyalty\Enum\TopUpType;
use App\Entity\Loyalty\TopUp;
use App\Model\Date\DateTimeNullableRangeRequest;
use App\Model\Loyalty\Card;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\ClientsRepository;
use App\Repository\Loyalty\CyclicTopUpRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Repository\OwnerRepository;
use App\Service\Email\EmailSender;
use App\Service\Loyalty\VirtualCardManager;
use App\Service\SyncData\SyncDataService;
use App\Service\User\UserService;
use Exception;
use I2m\StandardTypes\Enum\Source;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Translation\LocaleSwitcher;
use Symfony\Contracts\Translation\TranslatorInterface;
use App\Service\Loyalty\CardsManager;
use App\Entity\Loyalty\Enum\TopUpStatus;

use function Sentry\captureException;

#[Route('/api/owner/{ownerId}/card/{cardToken}')]
class LoyaltyCardsTokenController extends AbstractController
{
    #[Route('', methods: ['GET'])]
    public function getCard(
        int $ownerId,
        SyncDataService $syncDataService,
        string $cardToken,
        Request $request,
        CardsRepository $cardsRepository,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $card = $cardsRepository->getCardByToken($cardToken, null, $owner);
        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get('timezone')
        );

        $context =  [
            'groups' => [
                "loyalty:cards:list",
                "default:basic"
            ],
        ];

        if ($request->query->getBoolean('stats')) {
            $context['stats'] = [
                        'from' => $dateRange->getDateFrom(),
                        'to' => $dateRange->getDateTo(),
                        'timezone' => $dateRange->getTimezone(),
                        ];
        }

        return $this->json(
            $card,
            JsonResponse::HTTP_OK,
            [],
            $context
        );
    }

    #[Route('', methods: ['PUT', 'PATCH' /** @deprecated */])]
    public function updateCardByToken(
        int $ownerId,
        SyncDataService $syncDataService,
        string $cardToken,
        ClientsRepository $clientsRepository,
        CardsRepository $cardsRepository,
        UserService $userService,
        #[MapRequestPayload] Card $cardModel,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $card = $cardsRepository->getCardByToken($cardToken, null, $owner);
        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }

        $card
            ->setStatus($cardModel->getStatus() ?? $card->getStatus());
        $card->setEmail($cardModel->getEmail());
        $card->setAlias($cardModel->getAlias());
        $card->setDescription($cardModel->getDescription());
        $card->setClient($clientsRepository->getClient($cardModel->getClientId(), $owner));

        $cardsRepository->save($card);

        $userService->inviteUserIfNotExist($card->getEmail(), $owner);

        return $this->json(
            $card,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    "loyalty:cards:list",
                    "loyalty:client:owner",
                    "default:basic"
                ]]
        );
    }

    #[Route('/top_ups', methods: ['POST'])]
    public function newTopUp(
        int $ownerId,
        SyncDataService $syncDataService,
        string $cardToken,
        Request $request,
        CardsRepository $cardsRepository,
        TopUpRepository $topUpRepository,
        CyclicTopUpRepository $cyclicTopUpRepository,
        EmailSender $emailSender,
        TranslatorInterface $translator,
        LocaleSwitcher $localeSwitcher,
        CardsManager $cardsManager,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $card = $cardsRepository->getCardByToken($cardToken, null, $owner);
        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }
        $data = json_decode($request->getContent(), true);

        $cylicTopUpId = $data['cyclicTopUp']['id'] ?? null;
        $cylicTopUp = $cylicTopUpId ?
            $cyclicTopUpRepository->findOneBy(['id' => $cylicTopUpId, 'card' => $card]) : null;

        $topUps = $cardsManager->addTopUpWithBonus(
                $card,
                $data['value'],
                TopUpStatus::ACTIVE,
                $request->headers->get('X-User-Email'),
                $data['bonus'],
                $cylicTopUp
            );

        $topUpsCount = count($topUps);
        if ($topUpsCount == 0) {
            return $this->json(
                ['error' => "no top up created"],
                JsonResponse::HTTP_BAD_REQUEST
            );
        }

        $topUp = $topUps[0];
        $bonusTopUp = $topUpsCount >= 2 ? $topUps[1] : null;
        $topUpTotalValue = $topUp->getTopUpValue() + ($bonusTopUp?->getTopUpValue() ?? 0);

        $email = $card->getEmail();
        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $locale = $owner->getConfig()->getLanguage()->value;

            $localeSwitcher->runWithLocale(
                $locale,
                function ($locale) use ($emailSender, $translator, $email, $owner, $topUp, $topUpTotalValue) {
                    $emailSender->sendHtml(
                        $email,
                        $translator->trans('top_up.notification_title', locale: $locale),
                        'top_up/top_up_email.html.twig',
                        replyTo: $owner->getConfig()->getSupportEmail(),
                        context: [
                        'ownerName' => $owner->getName(),
                        'card' => $topUp->getCard()->getNumber(),
                        'value' => $topUpTotalValue,
                        'currency' => $topUp->getCurrency()->getSymbol(),
                        ]
                    );
                }
            );
        }

        return $this->json(
            $topUp,
            JsonResponse::HTTP_CREATED,
            [],
            [
               'groups' => [
                   "loyalty:topup:list",
                   "default:basic",
                ]
            ]
        );
    }

    #[Route('/top_ups', methods: ['DELETE'])]
    public function deleteTopUp(
        int $ownerId,
        SyncDataService $syncDataService,
        string $cardToken,
        TopUpRepository $topUpRepository,
        CardsRepository $cardsRepository,
        Request $request
    ): JsonResponse {

        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $ownerId = $request->query->getInt('owner');
        $card = $cardsRepository->getCardByToken($cardToken, null, $owner);

        $cancel = $topUpRepository->deleteTopUps($card);

        return $this->json(
            $cancel,
            JsonResponse::HTTP_OK,
            [],
            [
               'groups' => [
                   "default:basic",
               ]
            ]
        );
    }

    #[Route('', methods: ['DELETE'])]
    public function deleteCard(
        int $ownerId,
        SyncDataService $syncDataService,
        string $cardToken,
        CardsRepository $cardsRepository,
        Request $request
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $card = $cardsRepository->getCardByToken($cardToken, null, $owner);
        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }
        $card->setStatus(CardStatus::DELETED);
        $cardsRepository->save($card);

        return $this->json(
            $card,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    "loyalty:cards:list",
                    "default:basic"
                ]
            ]
        );
    }

    #[Route('/cyclic_top_ups', methods: ['POST'])]
    public function newCyclicTopUp(
        int $ownerId,
        SyncDataService $syncDataService,
        string $cardToken,
        Request $request,
        CardsRepository $cardsRepository,
        CyclicTopUpRepository $cyclicTopUpRepository
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $card = $cardsRepository->getCardByToken($cardToken, null, $owner);
        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }
        $data = json_decode($request->getContent(), true);
        $currency = $card->getCurrency() ?? $card->getOwner()->getCurrency();
        if (is_null($currency)) {
            throw new BadRequestHttpException("can't calculate currency");
        }

        $cyclicTopUp = (new CyclicTopUp())
            ->setCard($card)
            ->setType($data['type'])
            ->setDiscount($data['discount'])
            ->setComment($data['comment'])
            ->setStartTime(new \DateTime($data["startTime"]))
            ->setEndTime(new \DateTime($data["endTime"]))
            ->setIsActive($data["isActive"])
            ->setValue($data["value"])
            ->setAddedBy($data['email'])
        ;
        $cyclicTopUpRepository->save($cyclicTopUp);

        return $this->json(
            $cyclicTopUp,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    "loyalty:cyclic_topup:list",
                    "default:basic",
                ]
            ]
        );
    }
}
