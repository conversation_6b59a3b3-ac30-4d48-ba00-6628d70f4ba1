<?php

namespace App\Controller\Public;

use App\Entity\Owners;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;

class TermsController extends AbstractController
{
    #[Route(name: 'terms_of_use', path: '/terms/of_use', methods: ['GET'])]
    public function getTermsOfUse(Request $request, TranslatorInterface $translator): Response
    {
        $params = [
            'title' => 'terms_of_use',
            'content' => $this->getFile('terms_of_use.html', $this->getLanguage($request))
        ];

        return $this->renderPage('terms.html.twig', $params, $request, $translator);
    }

    #[Route(name: 'privacy_policy', path: '/terms/privacy', methods: ['GET'])]
    public function getPrivacyPolicy(Request $request, TranslatorInterface $translator): Response
    {
        $params = [
            'title' => 'privacy_policy',
            'content' => $this->getFile('rodo.html', $this->getLanguage($request))
        ];

        return $this->renderPage('terms.html.twig', $params, $request, $translator);
    }

    #[Route(path: '/terms/owner/{id}', methods: ['GET'])]
    public function getOwnerTerms(Request $request, Owners $owner, TranslatorInterface $translator): Response
    {
        $params = [
            'owner' => $owner->getConfig()
        ];

        return $this->renderPage('terms_owner.html.twig', $params, $request, $translator);
    }

    private function getLanguage(Request $request): string
    {
        return $request->query->get('language', 'en');
    }

    private function renderPage(string $template, array $params, Request $request, TranslatorInterface $translator): Response
    {
        $language = $this->getLanguage($request);
        /**
         * @var Translator $translator
         */
        $translator->setLocale($language);

        return $this->render($template, $params);
    }

    private function getFile(string $file, string $language = 'en')
    {
        $filePath =
            $this->getParameter('kernel.project_dir') .
            "/printables/{$language}/{$file}";

        return file_get_contents($filePath);
    }
}
