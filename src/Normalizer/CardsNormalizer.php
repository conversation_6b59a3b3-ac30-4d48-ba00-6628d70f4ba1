<?php

namespace App\Normalizer;

use App\Entity\Loyalty\Cards;
use App\Repository\Loyalty\TopUpRepository;
use App\Repository\Loyalty\TransactionsRepository;
use DateTime;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class CardsNormalizer implements NormalizerInterface
{
    public function __construct(
        private TransactionsRepository $transactionsRepository,
        private TopUpRepository $topUpRepository,
        private ObjectNormalizer $normalizer
    ) {
    }

    public function normalize($object, string $format = null, array $context = [])
    {
        /** @var Cards $object */
        $stats = [];

        if (array_key_exists('timezone', $context)) {
            /** @var Cards $object */
            $object->setCtime($this->getLocalTime($object->getCtime(), $context['timezone'] ?? null));
            $object->setLastContact($this->getLocalTime($object->getLastContact(), $context['timezone'] ?? null));
        }



        if (array_key_exists('groups', $context) && in_array('loyalty:cards:list', $context['groups'])) {
            $stats["toSend"] = $this->topUpRepository->getTotalTopUpCredits($object) * $object->getCurrency()?->getRate();

            $comment = $object->getAdditInfo() ? $object->getAdditInfo() . " " : '';
            $comment .= $object->getDescription() ? $object->getDescription() . " " : '';
            $comment .= $object->getClient()?->getDescription()
                ? $object->getClient()->getDescription()
                : '';

            $object->setFullComment($comment);
        }

        if (array_key_exists('stats', $context)) {
            $from = $context['stats']['from'];
            $to = $context['stats']['to'];

            $stats1Transactions = $this->transactionsRepository->getStats(
                $object->getCurrency(),
                $object,
                null,
                $from,
                $to,
            );
            $subtraction = $stats1Transactions['SUBTRACTION'];
            $paySum = $subtraction['CAR_WASH'] + $subtraction['VACUUM_CLEANER'] + $subtraction['DISTRIBUTOR'];

            $statsTopUp = $this->topUpRepository->getStats(
                $object,
                $object->getOwner(),
                $from,
                $to,
            );


            $stats["promotions"] = $statsTopUp['PROMOTION']["INTERNET"] + $statsTopUp['PROMOTION']["MONEY_CHANGER"];
            $stats["topUps"] = $statsTopUp['ADDITION']["INTERNET"] + $statsTopUp['ADDITION']["MONEY_CHANGER"];
            $stats["payments"] = round($paySum, 2);

            $stats["topUp"] = $statsTopUp;
            $stats["transaction"] = $stats1Transactions;
        }
        $object->setStats($stats);

        $data = $this->normalizer->normalize($object, $format, $context);
        return $data;
    }

    public function supportsNormalization($data, string $format = null, array $context = []): bool
    {
        return  ($data instanceof Cards);
    }

    public function getLocalTime(?\DateTimeInterface $utcTime, ?\DateTimeZone $timeZone = null): ?\DateTimeInterface
    {
        if ($timeZone && $utcTime) {
            $ct = DateTime::createFromInterface($utcTime);
            return $ct->setTimezone($timeZone);
        }
        return $utcTime;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            Cards::class => true,
        ];
    }
}
