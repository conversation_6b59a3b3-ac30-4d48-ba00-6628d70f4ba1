<?php

namespace App\Service\User;

use App\Entity\User;
use App\Exception\User\InvalidTokenException;
use App\Exception\User\UserNotFoundException;
use App\Repository\UserRepository;
use App\Service\Email\EmailSender;
use App\Service\Security\TokenGenerator;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class PasswordResetService
{
    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly EmailSender $emailSender,
        private readonly TokenGenerator $tokenGenerator,
        private readonly TranslatorInterface $translator,
        private readonly UrlGeneratorInterface $urlGenerator,
        private readonly RequestStack $requestStack,
        private readonly UserPasswordHasherInterface $passwordHasher,
    ) {
    }

    public function handlePasswordReset(string $email): void
    {
        $user = $this->userRepository->findOneByEmail($email);

        if ($user === null) {
            throw new UserNotFoundException($email);
        }


        $token = $this->tokenGenerator->generate();
        $user->setPasswordResetToken($token);

        $this->userRepository->save($user);

        $this->emailSender->sendHtml(
            $email,
            $this->translator->trans('password_reset.title'),
            'password_reset/password_reset_email.html.twig',
            context: [
                'password_reset_link' => $this->buildPasswordResetLink($token),
            ],
        );
    }

    public function confirmPasswordReset(string $token, string $newPassword): void
    {
        $user = $this->validateToken($token);

        $hashedPassword = $this->passwordHasher->hashPassword($user, $newPassword);
        $user->setPassword($hashedPassword);
        $user->setPasswordResetToken(null);

        $this->userRepository->save($user);
    }

    public function validateToken(string $token): User
    {
        $user = $this->userRepository->findOneBy(['passwordResetToken' => $token]);
        if ($user === null) {
            throw new InvalidTokenException();
        }

        return $user;
    }

    private function buildPasswordResetLink(string $token): string
    {
        $url = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();

        return $url . $this->urlGenerator->generate(
            'password_reset_form',
            [
                'token' => $token,
            ],
            UrlGeneratorInterface::ABSOLUTE_PATH,
        );
    }
}
