<?php

namespace App\Entity\Loyalty;

use App\Entity\Currency;
use App\Repository\Loyalty\CyclicTopUpRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: CyclicTopUpRepository::class)]
#[ORM\HasLifecycleCallbacks]
class CyclicTopUp
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['loyalty:cyclic_topup:list'])]
    private int $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Cards::class, inversedBy: 'cyclicTopUps')]
    #[Groups(['loyalty:cyclic_topup:list'])]
    private Cards $card;

    #[ORM\Column(type: 'float')]
    #[Groups(['loyalty:cyclic_topup:list','loyalty:cyclic_topup:edit'])]
    private float $value;

    #[ORM\Column(type: 'datetime', nullable: true)]
    #[Groups(['loyalty:cyclic_topup:list','loyalty:cyclic_topup:edit'])]
    private ?\DateTimeInterface $startTime = null;

    #[ORM\Column(type: 'datetime', nullable: true)]
    #[Groups(['loyalty:cyclic_topup:list','loyalty:cyclic_topup:edit'])]
    private ?\DateTimeInterface $endTime = null;

    #[ORM\Column(type: 'datetime')]
    private ?\DateTimeInterface $ctime;

    #[ORM\Column(type: 'datetime')]
    private ?\DateTimeInterface $mtime;

    #[ORM\Column(type: 'string', length: 32)]
    #[Groups(['loyalty:cyclic_topup:list','loyalty:cyclic_topup:edit'])]
    private string $type;

    #[ORM\OneToMany(targetEntity: TopUp::class, mappedBy: 'cyclicTopUp')]
    private $topUp;

    #[ORM\Column(type: 'boolean')]
    #[Groups(['loyalty:cyclic_topup:list','loyalty:cyclic_topup:edit'])]
    private bool $isActive = false;

    #[ORM\Column(type: 'float', nullable: true)]
    #[Groups(['loyalty:cyclic_topup:list','loyalty:cyclic_topup:edit'])]
    private ?float $discount = null;

    #[ORM\Column(type: 'datetime', nullable: true)]
    #[Groups(['loyalty:cyclic_topup:list'])]
    private ?\DateTimeInterface $lastPeriod = null;

    #[ORM\Column(type: 'datetime', nullable: true)]
    #[Groups(['loyalty:cyclic_topup:list'])]
    private ?\DateTimeInterface $lastCall = null;

    #[ORM\Column(type: 'text', nullable: true)]
    #[Groups(['loyalty:cyclic_topup:list','loyalty:cyclic_topup:edit'])]
    private ?string $comment = null;

    #[Groups(['loyalty:cyclic_topup:list'])]
    private ?Currency $currency = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[Groups(['loyalty:cyclic_topup:list'])]
    private ?string $addedBy = null;

    public function getCurrency(): ?Currency
    {
        return $this->currency ??
            $this->getCard()->getCurrency() ??
            $this->getCard()->getOwner()->getCurrency()
            ;
    }

    public function setCurrency(?Currency $currency): CyclicTopUp
    {
        $this->currency = $currency;
        return $this;
    }

    public function __construct()
    {
        $this->topUp = new ArrayCollection();
    }

    public function setId(int $id): CyclicTopUp
    {
        $this->id = $id;

        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCard(): ?Cards
    {
        return $this->card;
    }

    public function setCard(?Cards $card): self
    {
        $this->card = $card;

        return $this;
    }

    public function getValue(): ?float
    {
        return
                $this->value *
                $this->getCurrency()?->getRate();
    }

    public function setValue(float $value): self
    {
        $this->value =
                $value /
                $this->getCurrency()?->getRate();

        return $this;
    }

    public function getStartTime(): ?\DateTimeInterface
    {
        return $this->startTime;
    }

    public function setStartTime(?\DateTimeInterface $startTime): self
    {
        $this->startTime = $startTime;

        return $this;
    }

    public function getEndTime(): ?\DateTimeInterface
    {
        return $this->endTime;
    }

    public function setEndTime(?\DateTimeInterface $endTime): self
    {
        $this->endTime = $endTime;

        return $this;
    }

    public function getCtime(): ?\DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(\DateTimeInterface $ctime): self
    {
        $this->ctime = $ctime;

        return $this;
    }

    public function getMtime(): ?\DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(\DateTimeInterface $mtime): self
    {
        $this->mtime = $mtime;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return Collection<int, TopUp>
     */
    public function getTopUp(): Collection
    {
        return $this->topUp;
    }

    public function addTopUp(TopUp $topUp): self
    {
        if (!$this->topUp->contains($topUp)) {
            $this->topUp[] = $topUp;
            $topUp->setCyclicTopUp($this);
        }

        return $this;
    }

    public function removeTopUp(TopUp $topUp): self
    {
        if ($this->topUp->removeElement($topUp)) {
            // set the owning side to null (unless already changed)
            if ($topUp->getCyclicTopUp() === $this) {
                $topUp->setCyclicTopUp(null);
            }
        }

        return $this;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps(): void
    {
        $this->setMtime(new \DateTime('now'));
        if (!isset($this->ctime)) {
            $this->setCtime(new \DateTime('now'));
        }
    }

    public function isIsActive(): ?bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): self
    {
        $this->isActive = $isActive;

        return $this;
    }

    public function getDiscount(): ?float
    {
        return $this->discount;
    }

    public function setDiscount(?float $discount): self
    {
        $this->discount = $discount;

        return $this;
    }

    public function getLastPeriod(): ?\DateTimeInterface
    {
        return $this->lastPeriod;
    }

    public function setLastPeriod(?\DateTimeInterface $lastPeriod): self
    {
        $this->lastPeriod = $lastPeriod;

        return $this;
    }

    public function getLastCall(): ?\DateTimeInterface
    {
        return $this->lastCall;
    }

    public function setLastCall(?\DateTimeInterface $lastCall): self
    {
        $this->lastCall = $lastCall;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): CyclicTopUp
    {
        $this->comment = $comment;
        return $this;
    }

    public function setAddedBy(?string $addedBy): self
    {
        $this->addedBy = $addedBy;
        return $this;
    }

    public function getAddedBy(): ?string
    {
        return $this->addedBy;
    }

    public function isActive(): ?bool
    {
        return $this->isActive;
    }

    public function setActive(bool $isActive): static
    {
        $this->isActive = $isActive;

        return $this;
    }
}
