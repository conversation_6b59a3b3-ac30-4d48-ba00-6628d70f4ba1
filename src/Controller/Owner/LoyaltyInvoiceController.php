<?php

namespace App\Controller\Owner;

use App\Entity\OwnerConfig;
use App\Model\Date\DateTimeNullableRangeRequest;
use App\Model\Loyalty\SendInvoice;
use App\Repository\Invoice\InvoiceRepository;
use App\Repository\Loyalty\ClientsRepository;
use App\Repository\OwnerConfigRepository;
use App\Service\Loyalty\InvoiceTopUpService;
use App\Service\SyncData\SyncDataService;
use I2m\Connectors\Model\CwLoyaltyApi\Logo;
use I2m\Invoices\Enum\InvoiceGeneratorType;
use I2m\Invoices\Service\InvoiceService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

class LoyaltyInvoiceController extends AbstractController
{
    #[Route('/api/owner/{ownerId}/config', methods: ["PUT"])]
    public function saveOwnerConfig(
        SerializerInterface $serializer,
        Request $request,
        OwnerConfigRepository $repository,
        int $ownerId,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $ownerConfig = $serializer->deserialize(
            $request->getContent(),
            OwnerConfig::class,
            'json',
            [
                'circular_reference_limit' => 1,
                'object_to_populate' => $owner->getConfig(),
                'groups' =>
                    [
                        'default:basic',
                    ]
            ]
        );

        $repository->save($ownerConfig);

        return $this->json(
            $owner->getConfig(),
            JsonResponse::HTTP_OK,
            context: [
                'groups' =>
                [
                    'default:basic',
                ]
            ]
        );
    }

    #[Route('/api/owner/{ownerId}/config', methods: ["GET"])]
    public function getOwnerConfig(
        int $ownerId,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        return $this->json(
            $owner->getConfig(),
            JsonResponse::HTTP_OK,
            context: [
                'circular_reference_limit' => 1,
                'groups' => [
                    "default:basic",
                ],
            ],
        );
    }

    #[Route('/api/owner/{ownerId}/logo', methods: ["GET"])]
    public function getOwnerLogo(
        int $ownerId,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        return $this->json(
            $owner->getConfig(),
            JsonResponse::HTTP_OK,
            context: [
                'groups' => [
                    "owner:logo",
                ],
            ],
        );
    }

    #[Route('/api/owner/{ownerId}/logo', methods: ["PUT"])]
    public function saveOwnerLogo(
        SerializerInterface $serializer,
        Request $request,
        OwnerConfigRepository $repository,
        int $ownerId,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $ownerConfig = $serializer->deserialize(
            $request->getContent(),
            OwnerConfig::class,
            'json',
            [
                'circular_reference_limit' => 1,
                'object_to_populate' => $owner->getConfig(),
                'groups' =>
                    [
                        'owner:logo',
                    ]
            ]
        );

        $repository->save($ownerConfig);

        $logo = new Logo();
        $logo->setLogo($ownerConfig->getLogo());

        return $this->json(
            $logo,
            JsonResponse::HTTP_OK,
        );
    }

    #[Route('/api/owner/{ownerId}/invoices/config/{type}', methods: ["PUT"])]
    public function saveConfig(
        InvoiceGeneratorType $type,
        Request $request,
        OwnerConfigRepository $repository,
        int $ownerId,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $ownerConfig = $owner->getConfig();
        $invoiceConfig = $ownerConfig->getInvoiceConfig();
        $invoiceConfig[$type->value] = json_decode($request->getContent());
        $ownerConfig->setInvoiceConfig($invoiceConfig);
        $ownerConfig->setInvoiceType($type);

        $repository->save($ownerConfig);

        return $this->json(
            $owner->getConfig()->getInvoiceConfig()[$type->value],
            JsonResponse::HTTP_OK,
        );
    }

    #[Route('/api/owner/{ownerId}/invoices/config/{type}', methods: ["GET"])]
    public function getConfig(
        InvoiceGeneratorType $type,
        int $ownerId,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $config = $owner->getConfig()?->getInvoiceConfig();

        return $this->json(
            $config[$type->value] ?? [],
            JsonResponse::HTTP_OK,
        );
    }

    #[Route('/api/owner/{ownerId}/invoices', methods: ["GET"])]
    public function getList(
        InvoiceRepository $invoiceRepository,
        int $ownerId,
        Request $request,
        SyncDataService $syncDataService,
        ClientsRepository $clientsRepository,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $page = $request->query->get('page', 1);
        $itemsPerPage = $request->query->get('itemsPerPage', 50);
        $search = $request->query->get('search');
        $clientId = $request->query->getInt('clientId');
        $client = $clientId ? $clientsRepository->find($clientId) : null;

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get('timezone')
        );

        $invoices = $invoiceRepository->getList(
            $owner,
            $page,
            $itemsPerPage,
            $search,
            $dateRange->getDateFrom(),
            $dateRange->getDateTo(),
            $client
        );

        return $this->json(
            $invoices,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    "invoices:owner",
                    "default:basic",
                ],
            ]
        );
    }

    #[Route('/api/owner/{ownerId}/invoice/{id}', methods: ["GET"])]
    public function getInvoice(
        InvoiceRepository $invoiceRepository,
        SyncDataService $syncDataService,
        int $id,
        int $ownerId,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $invoice = $invoiceRepository->findOneBy(['id' => $id, 'owner' => $owner]);

        if (is_null($invoice)) {
            $this->json(["invoice not found"], JsonResponse::HTTP_NOT_FOUND);
        }

        return $this->json(
            $invoice,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    "invoices:owner",
                    "default:basic",
                ],
            ]
        );
    }

    #[Route('/api/owner/{ownerId}/invoice/{id}/download', methods: ["GET"])]
    public function downloadInvoice(
        InvoiceRepository $invoiceRepository,
        InvoiceService $invoiceService,
        SyncDataService $syncDataService,
        int $id,
        int $ownerId,
    ): Response {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $invoice = $invoiceRepository->findOneBy(['id' => $id, 'issuer' => $owner]);

        if (is_null($invoice)) {
            $this->json(["invoice not found"], JsonResponse::HTTP_NOT_FOUND);
        }
        $blobData = $invoiceService->getStream($invoice);


        $response = new Response(stream_get_contents($blobData));
        $response->headers->set('Content-Type', 'application/octet-stream');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $invoice->getNumber() . '.pdf"');

        return $response;
    }

    #[Route('/api/owner/{ownerId}/invoice/{id}/send', methods: ["POST"])]
    public function sendInvoice(
        InvoiceRepository $invoiceRepository,
        SyncDataService $syncDataService,
        int $id,
        int $ownerId,
        InvoiceTopUpService $invoiceTopUpService,
        #[MapRequestPayload] SendInvoice $sendInvoice,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $invoice = $invoiceRepository->findOneBy(['id' => $id, 'issuer' => $owner]);

        if (is_null($invoice)) {
            $this->json(["invoice not found"], JsonResponse::HTTP_NOT_FOUND);
        }
        $invoiceTopUpService->sendEmail($invoice, $sendInvoice->getEmail(), $owner);
        return $this->json(
            $invoice,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    "invoices:owner",
                    "default:basic",
                ],
            ]
        );
    }
}
