<?php

namespace App\Repository;

use App\Entity\Carwash;
use App\Entity\Loyalty\Cards;
use App\Entity\Owners;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use I2m\StandardTypes\Enum\CMSubscription;

/**
 * @extends ServiceEntityRepository<Carwash>
 * @method Carwash|null findOneBySn(int $sn)
 */
class CarwashRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Carwash::class);
    }

    //    /**
    //     * @return Carwash[] Returns an array of Carwash objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('c')
    //            ->andWhere('c.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('c.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Carwash
    //    {
    //        return $this->createQueryBuilder('c')
    //            ->andWhere('c.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }

    public function save(Carwash $entity): Carwash
    {
        $this->getEntityManager()->persist($entity);
        $this->getEntityManager()->flush();
        return $entity;
    }

    /**
     * @return Carwash[]
     */
    public function findNearby(?float $userLat, ?float $userLon, ?float $radius, ?string $email = null): array
    {
        $qb = $this
            ->createQueryBuilder('d')
            ->innerJoin('d.owner', 'o') // Łączenie właściciela urządzenia
            ->innerJoin(Cards::class, 'c', 'WITH', 'c.owner = o') // Łączenie kart
            ->where('o.subscription IN (:subscription)')
            ->setParameter('subscription', [CMSubscription::BASIC, CMSubscription::PREMIUM])

        ;
        if ($userLat && $userLon) {
            $qb->addSelect(
                '(6371 * acos(
                cos(radians(:userLat)) * cos(radians(d.lat)) *
                cos(radians(d.lon) - radians(:userLon)) +
                sin(radians(:userLat)) * sin(radians(d.lat))
            )) AS HIDDEN distance'
            )
                ->having('distance < :radius')

                ->orderBy('distance', 'ASC')
                ->setParameter('userLat', $userLat)
                ->setParameter('userLon', $userLon)
                ->setParameter('radius', $radius);
        }


        if ($email) {
            $qb
                ->andWhere('c.email = :email')
                ->setParameter('email', $email);
        }

        return $qb->getQuery()->getResult();
    }

    public function getList(
        ?Owners $owner = null,
        ?int $page = 1,
        ?int $perPage = 10,
        ?string $search = null,
    ) {
        $queryBuilder = $this->createQueryBuilder('t')
            ->leftJoin('t.owner', 'owner')
        ;

        if ($owner) {
            $queryBuilder
                ->andWhere('t.owner = :owner')
                ->setParameter('owner', $owner);
        }

        if ($search) {
            $orX = $queryBuilder->expr()->orX();

            $orX->add($queryBuilder->expr()->like('LOWER(t.name)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(t.sn)', ':search'));

            $queryBuilder->andWhere($orX)
                ->setParameter('search', '%' . strtolower($search) . '%');
        }

        $countQueryBuilder = clone $queryBuilder;
        $total = $countQueryBuilder->select('COUNT(t)')->getQuery()->getSingleScalarResult();

        $qb = $queryBuilder->setFirstResult(($page - 1) * $perPage)
            ->setMaxResults($perPage);
        ;

        return [
            'data' => $qb->getQuery()->getResult(),
            'total' => $total
        ];
    }
}
