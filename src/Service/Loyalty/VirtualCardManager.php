<?php

namespace App\Service\Loyalty;

use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Loyalty\Enum\TransactionType;
use App\Entity\Loyalty\Transactions;
use App\Entity\Owners;
use App\Entity\User;
use App\Exception\Loyalty\RefusedException;
use App\Repository\CarwashRepository;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\TransactionsRepository;
use I2m\StandardTypes\Enum\CMSubscription;
use I2m\Connectors\Service\CwActionApi\ActionPaymentService;
use I2m\StandardTypes\Enum\Currency;

class VirtualCardManager
{
    public function __construct(
        private CardsRepository $cardsRepository,
        private ActionPaymentService $actionPaymentService,
        private CarwashRepository $carwashRepository,
        private TransactionsRepository $transactionsRepository
    ) {
    }

    public function getCardForBay(string $stand_code, string $email)
    {
        $carwash = $this->getCarwashFromStandCode($stand_code);

        $owner = $carwash->getOwner();
        $cards =  $this->cardsRepository->findBy(
            [
                            'email' => $email,
                            'owner' => $owner,
                            'status' => CardStatus::ACTIVE,
            ]
        );

        return
        [
            'cards' => $cards,
            'owner' => $owner,
            'carwash' => $carwash,
        ];
    }

    public function createCardForBay(string $stand_code, string $email): ?Cards
    {

        $carwash = $this->getCarwashFromStandCode($stand_code);
        return $this->createCardForOwner($carwash->getOwner(), $email);
    }

    public function createCardForOwner(Owners $owner, string $email): ?Cards
    {
        if (!$owner->getSubscription()->isGreaterEqualThan(CMSubscription::BASIC)) {
            return null;
        }

        if (!$owner->getConfig()?->isAutoRegisterCards()) {
            return null;
        }

        $card =
            (new Cards())
                ->setOwner($owner)
                ->setVirtual(true)
                ->setCurrency($owner->getCurrency())
                ->setEmail($email)
        ;


        $card = $this->cardsRepository->save($card);

        return $card;
    }

    public function getCarwashFromStandCode(string $stand_code): ?\App\Entity\Carwash
    {
        $response = $this->actionPaymentService->status($stand_code);
        $sn = $response->getStand()->getCarwash()->getSn();
        $carwash = $this->carwashRepository->findOneBySn($sn);
        return $carwash;
    }

    public function makePayment(
        Cards $card,
        string $stand_code,
        float $valueToPay,
        ?User $user,
        ?string $licensePlate = null
    ): Transactions {

        if (!$card->isVirtual()) {
            throw new RefusedException(
                "to nie jest karta wirtualna"
            );
        }

        // sprawdzam czy na pewno balance = suma transakcji.
        $balance = $this->transactionsRepository->calculateBalance($card);
        $card->verifyBalance($balance);

        if ($card->getBalance() < $valueToPay) {
            throw new RefusedException(
                "nie wystarczająca kwota kredytu na karcie {$card->getNumber()}: {$card->getBalance()} < $valueToPay"
            );
        }

        $carwash = $this->getCarwashFromStandCode($stand_code);
        if ($carwash->getOwner() !== $card->getOwner()) {
            throw new RefusedException(
                "właściciel stanowiska nie jest zgody z tokenem karty"
            );
        }

        $response = $this->actionPaymentService->topUp(
            $stand_code,
            $valueToPay,
            $card->getCurrency()->getCode(),
            'VIRTUAL_2',
            "{$card->getOwner()->getId()}:{$card->getNumber()}"
        );
        $stand = $response->getStand();
        $valueToPayCredit = $valueToPay / $card->getCurrency()->getRate();
        $newBalance = $balance - $valueToPayCredit;
        $transaction = (new Transactions())
            ->setOwner($card->getOwner())
            ->setCard($card)
            ->setCurrency($card->getCurrency())
            ->setType(TransactionType::SUBTRACTION)
            ->setBalanceInCredit($newBalance)
            ->setValueInCredit(-$valueToPayCredit)
            ->setCarwash($carwash)
            ->setSource($stand->getSource())
            ->setTime(new \DateTime())
            ->setBayId($stand->getBayId())
            ->setLicensePlate($licensePlate)
            ->setUser($user)
        ;
        $card->setBalance($newBalance);
        return $this->transactionsRepository->save($transaction);
    }
}
