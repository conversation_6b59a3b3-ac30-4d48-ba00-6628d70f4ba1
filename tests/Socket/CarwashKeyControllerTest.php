<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Tests\Socket;

use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use App\Entity\Carwash;
use App\Service\CarwashCrypto;

/**
 * Description of CarwashApiControllerTest
 *
 * <AUTHOR>
 */
class CarwashKeyControllerTest extends WebTestCase
{


    public function provideReqestData()
    {
        return [
            [
                'sn' => 10001,
                'plc' => 'E5700171161',
                'mac' => '00:60:65:81:86:A9',
                'msg' => '{"g_i2m_key[0]":{"iTimeStamp":**********,"iSn":**********,"iBay":9,"rVend":10,"rCredit":60.1,"iType":0}}',
                'statusCode' => 200,
                'cardNumber' => **********,
                'currency' => 'EUR'
            ]
            
        ];
    }

    protected function setUp(): void
    {
        self::ensureKernelShutdown();
        $this->client = static::createClient();
    }

    /**
     * @test
     * @dataProvider provideReqestData
     */
    public function performtStatsReqest($sn, $plc, $mac, $msg, $statusCode, $cardNumber, $currency)
    {

        $crypto = new CarwashCrypto();
        $client = $this->client;

        $carwash = (new Carwash())
            ->setSn($sn);

        $edgeDevice = (new Carwash\EdgeDevice())
            ->setUid($plc)
            ->setMac($mac)
            ->setCarwash($carwash);

        // wlozenie karty
        $ask = [
            'rCredit'  => 5,
            "sCurrency" => $currency
        ];

        // zapytanie od wersji v2.21.0b39
        $body = $crypto->encrypt($edgeDevice, json_encode($ask), CarwashCrypto::PASS_SOCKET);
        $url = "/plc/$plc?key=$cardNumber";
        $client->request('GET', $url, [], [], [], $body);
        //print_r($client->getResponse()->getContent());
        $this->assertEquals($statusCode, $client->getResponse()->getStatusCode());
        $text = $crypto->decrypt($edgeDevice, $client->getResponse()->getContent(), CarwashCrypto::PASS_SOCKET);
        $data = json_decode($text);
        $this->assertEquals(0, $data->g_i2m_rev->value);

        // potwierdzenie doladowania od wersji v2.21.0b39
        $body = $crypto->encrypt($edgeDevice, '{"g_i2m_rev":{"value":200.00,"rCredit":205.00,"sCurrency":"HRK"}}', CarwashCrypto::PASS_SOCKET);
        $url = "/plc/$plc?key=$cardNumber";
        $client->request('POST', $url, [], [], [], $body);
        $this->assertEquals($statusCode, $client->getResponse()->getStatusCode());


        // zapytanie < wersji v2.21.0b39
        $url = "/plc/$plc?key=$cardNumber";
        $client->request('GET', $url);
        $this->assertEquals($statusCode, $client->getResponse()->getStatusCode());
        $this->assertEquals(0, $data->g_i2m_rev->value);

        // potwierdzenie doladowania < wersji v2.21.0b39
        $body = $crypto->encrypt($edgeDevice, '{"g_i2m_rev":{"value":200.00}}', CarwashCrypto::PASS_SOCKET);
        $url = "/plc/$plc?key=$cardNumber";
        $client->request('POST', $url, [], [], [], $body);
        $this->assertEquals($statusCode, $client->getResponse()->getStatusCode());

        // wyslanie transakcji
        $body = $crypto->encrypt($edgeDevice, $msg, CarwashCrypto::PASS_SOCKET);
        $url = "/plc/$plc";
        

        $client->request('POST', $url, [], [], [], $body);

        $this->assertEquals($statusCode, $client->getResponse()->getStatusCode());
    }

    /**
     * @test
     * nie ma tej karty w systemie
     */
    public function noConversionToEuro()
    {
        $sn = 10001;
        $plc = 'E5700171161';
        $mac = '00:60:65:81:86:A9';
        $cardNumber = **********;

        $crypto = new CarwashCrypto();
        $client = $this->client;

        $carwash = (new Carwash())
            ->setSn($sn);

        $edgeDevice = (new Carwash\EdgeDevice())
            ->setUid($plc)
            ->setMac($mac)
            ->setCarwash($carwash);

        // wlozenie karty
        $ask = [
            'rCredit'  => 100,
            "sCurrency" => 'EUR'
        ];

        $body = $crypto->encrypt($edgeDevice, json_encode($ask), CarwashCrypto::PASS_SOCKET);
        $url = "/plc/$plc?key=$cardNumber";
        $client->request('GET', $url, [], [], [], $body);
        $this->assertEquals(200, $client->getResponse()->getStatusCode());
        $text = $crypto->decrypt($edgeDevice, $client->getResponse()->getContent(), CarwashCrypto::PASS_SOCKET);
        $data = json_decode($text);
        $this->assertEquals(0, $data->g_i2m_rev->value);
    }
}
