#!/usr/bin/env pwsh  

param (

)


#php bin/console doctrine:schema:validate

./vendor/bin/phpcbf ./src/
./vendor/bin/phpstan analyse ./src/ --no-progress --level=5
./vendor/bin/phpcs --exclude=Generic.Files.LineLength ./src/

php bin/console doctrine:database:drop --if-exists --force --env=test
php bin/console doctrine:database:create --if-not-exists --env=test
php bin/console doctrine:schema:update --force --env=test
php bin/console doctrine:fixtures:load -n --env=test
php bin/console league:oauth2-server:create-client   test client_app_test app_secret --scope=USER --grant-type=password --env=test
php bin/console league:oauth2-server:create-client   test client_api_test api_secret --scope=API --grant-type=client_credentials --env=test
php bin/phpunit