<?php

namespace App\Command;

use App\Entity\Loyalty\Enum\TopUpType;
use App\Entity\Loyalty\TopUp;
use App\Repository\CurrencyRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Repository\OwnerRepository;
use I2m\StandardTypes\Enum\Source;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'loyalty:create:top-up',
    description: 'Add a short description for your command',
)]
class TopUpsGenerateCommand extends Command
{
    public function __construct(
        private TopUpRepository $topUpRepository,
        private CurrencyRepository $currencyRepository,
        private OwnerRepository $ownerRepository
    ) {
        parent::__construct();
    }
    protected function configure(): void
    {
        $this
            ->addOption('owner', null, InputOption::VALUE_OPTIONAL, 'ownerId')
            ->addOption('value', null, InputOption::VALUE_REQUIRED, 'value of topUp')
            ->addOption('currency', null, InputOption::VALUE_OPTIONAL, 'eg. PLN, EUR if owner is empty')
            ->addOption('length', null, InputOption::VALUE_REQUIRED, 'topUps Count')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $length = $input->getOption('length');
        $value = $input->getOption('value');

        $ownerId = $input->getOption('owner');

        $owner = $ownerId ? $this->ownerRepository->find($ownerId) : null;

        $currency =
            $owner?->getCurrency() ??
            $this->currencyRepository->getCurrency($input->getOption('currency'));

        if (is_null($currency)) {
            $io->error("currency not found");
        }
        $topUps = [];


        while ($length > 0) {
            $length--;
            $output->writeln("Tworze doładowanie na kwotę $value {$currency->getCode()->value}");

            $topUp = $this->topUpRepository->addTopUp(
                value: $value,
                currency: $currency,
                type: TopUpType::ADDITION,
                source: Source::INTERNET,
                owner: $owner,
                comment: "top up bonus card"
            );
            $topUps[] = $topUp;
        }
        $now = (new \DateTime())->format('Ymd_His');
        $this->saveDataToCsv($topUps, "{$now}_{$value}_{$currency->getCode()->value}.csv");
        $io->success('You have a new command! Now make it your own! Pass --help to see your options.');

        return Command::SUCCESS;
    }

    /**
     * @param TopUp[] $topUps
     */
    private function saveDataToCsv($topUps, $filename)
    {
        $headers = ['Lp.', 'QR', 'ccv', 'value', 'currency'];
        // Otwieramy lub tworzymy plik do zapisu
        $file = fopen($filename, 'a');

        // Jeśli plik nie istnieje, dodajemy nagłówki
        if (filesize($filename) === 0) {
            fputcsv($file, $headers);
        }
        $i = 0;
        // Zapisujemy dane
        foreach ($topUps as $topUp) {
            $i++;
            $datum = [$i, "https://vc.i2m.pl/topup/{$topUp->getTopUpToken()}", str_pad((string)$topUp->getCvv(), 3, '0', STR_PAD_LEFT), $topUp->getValue(), $topUp->getCurrency()->getSymbol()];
            fputcsv($file, $datum);
        }
        // Zamykamy plik
        fclose($file);

        return true; // Zwracamy true, jeśli zapis zakończył się powodzeniem
    }
}
