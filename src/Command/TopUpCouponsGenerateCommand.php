<?php

namespace App\Command;

use App\Entity\Enum\Languages;
use App\Entity\Loyalty\Enum\TopUpType;
use App\Entity\Loyalty\TopUp;
use App\Repository\CurrencyRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Repository\OwnerRepository;
use App\Service\QrPrintGenerator\TopUpCardService;
use I2m\StandardTypes\Enum\Source;
use setasign\Fpdi\Fpdi;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Filesystem\Filesystem;
use ZipArchive;

#[AsCommand(
    name: 'loyalty:create:top-up-coupons',
    description: 'Add a short description for your command',
)]
class TopUpCouponsGenerateCommand extends Command
{
    public function __construct(
        private TopUpRepository $topUpRepository,
        private CurrencyRepository $currencyRepository,
        private OwnerRepository $ownerRepository,
        private TopUpCardService $topUpCardService,
    ) {
        parent::__construct();
    }
    protected function configure(): void
    {
        $this
            ->addOption('owner', null, InputOption::VALUE_OPTIONAL, 'ownerId')
            ->addOption('value', null, InputOption::VALUE_REQUIRED, 'value of topUp')
            ->addOption('currency', null, InputOption::VALUE_OPTIONAL, 'eg. PLN, EUR if owner is empty')
            ->addOption('length', null, InputOption::VALUE_REQUIRED, 'topUps Count')
            ->addOption(
                name: 'lang',
                mode: InputOption::VALUE_OPTIONAL,
                default: 'pl'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $lang = $input->getOption('lang');
        $length = $input->getOption('length');
        $value = $input->getOption('value');

        $ownerId = $input->getOption('owner');

        $owner = $ownerId ? $this->ownerRepository->find($ownerId) : null;

        $currency =
            $owner?->getCurrency() ??
            $this->currencyRepository->getCurrency($input->getOption('currency'));

        if (is_null($currency)) {
            $io->error("currency not found");
            return -1;
        }
        $topUps = [];

        $now = (new \DateTime())->format('Ymd_His');
        $filesystem = new Filesystem();
        $exportPath = "coupons/{$ownerId}/{$now}_{$value}_{$currency->getCode()->value}";
        $filesystem->mkdir($exportPath);

        while ($length > 0) {
            $length--;
            $output->writeln("Tworze doładowanie na kwotę $value {$currency->getCode()->value}");

            $topUp = $this->topUpRepository->addTopUp(
                value: $value,
                currency: $currency,
                type: TopUpType::ADDITION,
                source: Source::INTERNET,
                owner: $owner,
                comment: "top up bonus card"
            );
            $topUps[] = $topUp;
        }

        $this->generateCards(
            $topUps,
            $lang,
            $exportPath
        );

        $this->saveDataToCsv($topUps, $exportPath);

        $this->mergeCards($exportPath);

        $this->topUpCardService->zipDirectory(
            $exportPath,
            "coupons/{$ownerId}/{$now}_{$value}_{$currency->getCode()->value}.zip"
        );

        $io->success('You have a new command! Now make it your own! Pass --help to see your options.');

        return Command::SUCCESS;
    }

    private function generateCards(
        array $topUps,
        string $lang,
        string $exportPath,
    ) {

        $filesystem = new Filesystem();

        // Generujemy karty
        foreach ($topUps as $topUp) {
            $topUpCode = $topUp->getTopUpToken();

            $filePath = $this->topUpCardService->generateSingle(
                params: [
                    'code' => $topUpCode,
                    'cvv' => str_pad((string)$topUp->getCvv(), 3, '0', STR_PAD_LEFT),
                    'value' =>  $topUp->getValue(),
                    'currency' => $topUp->getCurrency()->getSymbol(),
                ],
                language: Languages::from($lang),
            );

            $filesystem->copy($filePath, $exportPath . DIRECTORY_SEPARATOR . $topUpCode . ".pdf");
        }

        return true;
    }

    private function mergeCards(string $sourcePath)
    {
        $now = (new \DateTime())->format('Ymd_His');
        $filesystem = new Filesystem();

        // Sprawdzenie, czy katalog istnieje
        if (!$filesystem->exists($sourcePath)) {
            throw new \RuntimeException("Katalog źródłowy nie istnieje: $sourcePath");
        }

        // Pobierz wszystkie pliki PDF z katalogu
        $files = glob($sourcePath . DIRECTORY_SEPARATOR . '*.pdf');
        if (empty($files)) {
            throw new \RuntimeException("Brak plików PDF w katalogu: $sourcePath");
        }

        $pdf = new Fpdi('P', 'mm', 'A4'); // 'P' = portrait, 'mm' = milimetry
        // Wymiary kart (np. 9.2cm x 6.1cm = 92mm x 61mm)
        $cardWidth = 86;
        $cardHeight = 54;

        // Ustawienia siatki
        $cardsPerRow = 2;
        $cardsPerCol = 5;

        // Własne marginesy i odstępy
        $marginLeft = 10; // mm od lewej
        $marginTop = 10;  // mm od góry
        $gapX = 1;         // odstęp poziomy między kartami
        $gapY = 1;         // odstęp pionowy między kartami

        $col = 0;
        $row = 0;

        $pdf->AddPage();

        foreach ($files as $filePath) {
            $pdf->setSourceFile($filePath);
            $tplIdx = $pdf->importPage(1);

            $x = $marginLeft + $col * ($cardWidth + $gapX);
            $y = $marginTop + $row * ($cardHeight + $gapY);

            $pdf->useTemplate($tplIdx, $x, $y, $cardWidth, $cardHeight);

            $col++;
            if ($col >= $cardsPerRow) {
                $col = 0;
                $row++;
            }
            if ($row >= $cardsPerCol) {
                $pdf->AddPage();
                $col = 0;
                $row = 0;
            }
        }

        $finalPath = $sourcePath . DIRECTORY_SEPARATOR . 'merge_' . $now . '.pdf';
        $pdf->Output('F', $finalPath);

        return $finalPath;
    }

    private function saveDataToCsv($topUps, $sourcePath)
    {
        $now = (new \DateTime())->format('Ymd_His');
        $filename = $sourcePath . DIRECTORY_SEPARATOR .  "table_{$now}.csv";

        $headers = ['Lp.', 'QR', 'ccv', 'value', 'currency'];
        // Otwieramy lub tworzymy plik do zapisu
        $file = fopen($filename, 'a');

        // Jeśli plik nie istnieje, dodajemy nagłówki
        if (filesize($filename) === 0) {
            fputcsv($file, $headers);
        }
        $i = 0;
        // Zapisujemy dane
        foreach ($topUps as $topUp) {
            $i++;
            $datum = [$i, "https://vc.i2m.pl/topup/{$topUp->getTopUpToken()}", str_pad((string)$topUp->getCvv(), 3, '0', STR_PAD_LEFT), $topUp->getValue(), $topUp->getCurrency()->getSymbol()];
            fputcsv($file, $datum);
        }
        // Zamykamy plik
        fclose($file);

        return true; // Zwracamy true, jeśli zapis zakończył się powodzeniem
    }

    public function zipDirectory(string $sourceDir, string $zipFilePath): bool
    {
        if (!is_dir($sourceDir)) {
            throw new \InvalidArgumentException("Katalog źródłowy nie istnieje: $sourceDir");
        }

        $zip = new ZipArchive();
        if ($zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
            throw new \RuntimeException("Nie udało się utworzyć pliku ZIP: $zipFilePath");
        }

        $sourceDir = realpath($sourceDir);
        $sourceDirLength = strlen($sourceDir) + 1;

        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($sourceDir, \FilesystemIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($files as $file) {
            if (!$file->isFile()) {
                continue;
            }

            $filePath = $file->getRealPath();
            $relativePath = substr($filePath, $sourceDirLength);

            $zip->addFile($filePath, $relativePath);
        }

        return $zip->close();
    }
}
