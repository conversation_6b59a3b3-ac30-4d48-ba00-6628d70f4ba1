<?php

namespace App\Controller\User;

use App\Entity\User;
use App\Model\ClientApp\SupportRequest;
use App\Repository\Loyalty\TransactionsRepository;
use App\Service\Support\SupportService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;

class LoyaltyTransactionsController extends AbstractController
{
    #[Route('/api/user/transaction/{id}/support', methods: ['POST'])]
    public function sendSupport(
        int $id,
        TransactionsRepository $transactionsRepository,
        SupportService $supportService,
        #[MapRequestPayload] SupportRequest $supportRequest,
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();

        $transaction = $transactionsRepository->findOneBy(
            ['id' => $id, 'user' => $user]
        );

        $supportService->createTicketFromTransaction($user, $transaction, $supportRequest->getDescription());

        return $this->json([], JsonResponse::HTTP_OK);
    }
}
