<?php

namespace App\Entity;

use App\Entity\ExternalPayment\PaymentGate;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\StandardTypes\Enum\CMSubscription;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Table(name: 'owners')]
#[ORM\Entity(repositoryClass: \App\Repository\OwnerRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Owners
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'NONE')]
    #[ORM\Column(name: 'id', type: 'integer')]
    #[Groups(['default:basic'])]
    private int $id;

    #[ORM\Column(name: 'name', type: 'string', length: 255, nullable: true)]
    #[Groups(['default:basic'])]
    private string $name;

    #[ORM\Column(name: 'ctime', type: 'datetime')]
    private \DateTimeInterface $ctime;

     #[ORM\Column(name: 'mtime', type: 'datetime')]
    private ?\DateTimeInterface $mtime = null;

    #[ORM\ManyToOne(targetEntity: Currency::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?Currency $currency;

    #[ORM\OneToOne(mappedBy: 'owner', cascade: ['persist', 'remove'])]
    #[Groups(["owner:contact",'owner:config'])]
    private ?OwnerConfig $config = null;

    #[ORM\OneToOne(mappedBy: 'owner', cascade: ['persist', 'remove'])]
    private ?PaymentGate $gate = null;

    #[ORM\Column(nullable: true, length: 8, enumType: CMSubscription::class)]
    private ?CMSubscription $subscription;

    #[ORM\Column(nullable: true)]
    private int $cmId;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getCtime(): \DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(\DateTimeInterface $ctime): self
    {
        $this->ctime = $ctime;

        return $this;
    }

    public function getMtime(): ?\DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(?\DateTimeInterface $mtime): self
    {
        $this->mtime = $mtime;

        return $this;
    }

    public function getCurrency(): ?Currency
    {
        return $this->currency;
    }


    public function setCurrency(?Currency $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps(): void
    {
        if (!isset($this->ctime)) {
            $this->ctime  = new \DateTime('now');
        }
        $this->mtime = new \DateTime('now');
    }

    #[Groups(['owner:logo'])]
    public function getLogo(): ?string
    {
        return $this->getConfig()->getLogo();
    }

    public function getConfig(): ?OwnerConfig
    {
        if ($this->config === null) {
            $this->config = (new OwnerConfig($this));
        }

        return $this->config;
    }

    public function setConfig(?OwnerConfig $config): Owners
    {
        $this->config = $config;
        return $this;
    }

    public function getSubscription(): ?CMSubscription
    {
        return $this->subscription;
    }

    public function setSubscription(?CMSubscription $subscription): Owners
    {
        $this->subscription = $subscription;
        return $this;
    }

    public function getGate(): ?PaymentGate
    {
        return $this->gate;
    }

    public function setGate(?PaymentGate $gate): self
    {
        $this->gate = $gate;

        return $this;
    }

    public function isRechargeable()
    {
        return
            // dodac tutaj warunek czy jest aktywna subskrypcja bkfpay+
            ($this->getGate()?->isReady() ?? false);
    }

    public function isInvocingOk()
    {
        return
            !empty($this->config->getTaxNumber()) &&
            !empty($this->config->getName()) &&
            !empty($this->config->getCountry())
            ;
    }

    public function getCmId(): int
    {
        return $this->cmId;
    }

    public function setCmId(int $cmId): Owners
    {
        $this->cmId = $cmId;
        return $this;
    }
}
