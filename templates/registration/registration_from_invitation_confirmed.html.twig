{% extends 'base.html.twig' %}

{% block title %}{{ 'registration.completed' | trans }} - {{ app_name }}{% endblock %}

{% block fos_user_content %}
    <main class="registration-page">
        <div class="registration-container">
            <div class="brand-header">
                <img src="{{ asset('assets/images/logo.png') }}" class="brand-logo" alt="{{ app_name }} Logo"/>
            </div>
            
            <div class="registration-card">
                <div class="card-header">
                    <h1>{{ 'registration.completed' | trans }}</h1>
                </div>

                <div class="card-body">
                    <p>{{ 'registration.completed_message' | trans }} <strong>{{ email }}</strong></p>
                    <p>{{ 'registration.download_app_log_in' | trans }}</p>

                    <div class="download-links">
                        <a href="{{ appLink_google }}" target="_blank">
                            <img src="{{ asset('assets/images/badges/googleplay.png') }}" alt={{ 'google_play_badge_alt' | trans }} class="store-badge">
                        </a>
                        <a href="{{ appLink_apple }}" target="_blank">
                            <img src="{{ asset('assets/images/badges/appstore.png') }}" alt={{ 'app_store_badge_alt' | trans }} class="store-badge">
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <style>
        .registration-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #ef5350 0%, #e53935 100%);
            display: flex;
            justify-content: center;
            font-family: system-ui, -apple-system, sans-serif;
        }

        .registration-container {
            padding-top: 10vh;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }

        .brand-header {
            text-align: center;
            margin-bottom: 1rem;
        }

        .brand-logo {
            height: 60px;
            width: auto;
        }

        .registration-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            overflow: hidden;
        }

        .card-header {
            padding: 2rem 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }

        .card-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            padding-bottom: 0.5rem;
        }

        .card-body {
            padding: 1rem 2rem 2rem 2rem;
            text-align: center;
        }

        .download-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .store-badge {
            height: 50px;
            width: auto;
        }
    </style>
{% endblock fos_user_content %}

