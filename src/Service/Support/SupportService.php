<?php

namespace App\Service\Support;

use App\Entity\ExternalPayment\ExternalPayment;
use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\TopUp;
use App\Entity\Loyalty\Transactions;
use App\Entity\Owners;
use App\Entity\User;
use App\Repository\Carwash\CarwashLogRepository;
use App\Service\Email\EmailSender;
use App\Service\Loyalty\VirtualCardManager;

use function Sentry\captureMessage;

class SupportService
{
    public function __construct(
        private EmailSender $emailSender,
        private VirtualCardManager $virtualCardManager
    ) {
    }
    public function createTicket(User $user, Owners $owner, string $title, string $description)
    {
        captureMessage(
            "Użytkownik {$user->getEmail()} zrobił zgłoszenie do właściciela: {$owner->getName()}\n" .
            "$title : $description"
        );

        $this->emailSender->sendMail(
            $owner->getConfig()->getSupportEmail(),
            $user->getEmail(),
            $title,
            $description
        );
    }

    public function createTicketFromTransaction(User $user, Transactions $transaction, string $description)
    {
        $owner = $transaction->getOwner();
        $title = "BKFPay support from User {$user->getEmail()}, transaction {$transaction->getId()}";
        $this->createTicket($user, $owner, $title, $description);
    }

    public function createTicketFromStand(User $user, string $standCode, string $description)
    {
        $carwash = $this->virtualCardManager->getCarwashFromStandCode($standCode);

        $title = "BKFPay support from User {$user->getEmail()}, stand $standCode, carwash {$carwash->getLongName()}";
        $this->createTicket($user, $carwash->getOwner(), $title, $description);
    }

    public function createTicketFromCard(User $user, Cards $card, string $description)
    {
        $title = "BKFPay support from User {$user->getEmail()}, owner of card {$card->getNumber()}";
        $this->createTicket($user, $card->getOwner(), $title, $description);
    }

    public function createTicketFromTopUp(User $user, TopUp $topUp, string $description)
    {
        $card = $topUp->getCard();
        $title = "BKFPay support from User {$user->getEmail()}, owner of card {$card->getNumber()}";
        $this->createTicket($user, $card->getOwner(), $title, $description);
    }

    public function createTicketFromExternalPayment(User $user, ExternalPayment $transaction, string $description)
    {
        $owner = $transaction->getOwner();
        $title = "BKFPay support from User {$user->getEmail()}, external payment  {$transaction->getId()}";
        $this->createTicket($user, $owner, $title, $description);
    }

    public function createTicketFromDeleteAccount(User $user, string $description)
    {
        captureMessage("Użytkownik {$user->getEmail()} usunął konto. Opis: $description");
    }
}
