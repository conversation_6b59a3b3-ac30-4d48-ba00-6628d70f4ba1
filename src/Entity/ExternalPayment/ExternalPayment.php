<?php

namespace App\Entity\ExternalPayment;

use App\Entity\Loyalty\Clients;
use App\Entity\Loyalty\TopUp;
use App\Entity\Owners;
use App\Entity\User;
use App\Repository\ExternalPayment\ExternalPaymentRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\Payment\Enum\GateList;
use I2m\Payment\Enum\Status;
use I2m\Payment\Interface\IConfig;
use I2m\Payment\Interface\IPayment;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\Serializer\Attribute\Groups;

#[ORM\Entity(repositoryClass: ExternalPaymentRepository::class)]
#[ORM\HasLifecycleCallbacks]
class ExternalPayment implements IPayment
{
    public const ISSUER_GATE = 'GATE';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['default:basic',"external_payment:list"])]
    private int $id;

    #[ORM\ManyToOne()]
    #[Groups(["external_payment:list"])]
    private ?User $user = null;

    #[ORM\ManyToOne()]
    private ?Owners $owner = null;


    #[ORM\OneToMany(targetEntity: TopUp::class, mappedBy: 'payment')]
    private $topUp;

    #[ORM\Column(type: 'datetime', nullable: false)]
    #[Groups(['default:basic',"external_payment:list"])]
    private \DateTimeInterface $initiatedTimestamp;

    #[ORM\Column(type: 'datetime', nullable: true)]
    #[Groups(['default:basic',"external_payment:list"])]
    private ?\DateTimeInterface $confirmedTimestamp = null;

    #[ORM\Column(nullable: false)]
    #[Groups(['default:basic',"external_payment:list"])]
    private float $value;

    #[ORM\Column(type: 'string', enumType: Status::class)]
    #[Groups(['default:basic',"external_payment:list"])]
    private Status $status;

    #[ORM\Column(nullable: true)]
    #[Groups(['default:basic',"external_payment:list"])]
    private array $additionalData;

    #[ORM\Column(type: 'text', nullable: true)]
    #[Groups(['default:basic',"external_payment:list"])]
    private ?string $externalId = null;

    #[ORM\Column(nullable: true)]
    private ?string $digest = null;

    #[ORM\ManyToOne()]
    private ?PaymentGate $gate = null;

    #[Groups(["external_payment:payment"])]
    private ?string $redirectUrl = null;

    public function __construct()
    {
        $this->topUp = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(?User $user): ExternalPayment
    {
        $this->user = $user;
        return $this;
    }

    public function getOwner(): ?Owners
    {
        return $this->owner;
    }

    public function setOwner(?Owners $owner): ExternalPayment
    {
        $this->owner = $owner;
        return $this;
    }

    public function setTopUp(?TopUp $topUp): ExternalPayment
    {
        $this->topUp = $topUp;
        return $this;
    }

    public function getInitiatedTimestamp(): \DateTimeInterface
    {
        return $this->initiatedTimestamp;
    }

    public function setInitiatedTimestamp(?\DateTimeInterface $initiatedTimestamp): ExternalPayment
    {
        $this->initiatedTimestamp = $initiatedTimestamp;
        return $this;
    }

    public function getConfirmedTimestamp(): ?\DateTimeInterface
    {
        return $this->confirmedTimestamp;
    }

    public function setConfirmedTimestamp(?\DateTimeInterface $time): ExternalPayment
    {
        $this->confirmedTimestamp = $time;
        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(?float $value): ExternalPayment
    {
        $this->value = $value;
        return $this;
    }

    public function getStatus(): Status
    {
        return $this->status;
    }

    public function setStatus(Status $status): ExternalPayment
    {
        $this->status = $status;
        return $this;
    }

    public function getAdditionalData(): array
    {
        return $this->additionalData;
    }

    public function setAdditionalData(array $additionalData): ExternalPayment
    {
        $this->additionalData = $additionalData;
        return $this;
    }
    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(?string $orderId): ExternalPayment
    {
        $this->externalId = $orderId;
        return $this;
    }

    public function getDigest(): ?string
    {
        return $this->digest;
    }

    public function setDigest(?string $digest): ExternalPayment
    {
        $this->digest = $digest;
        return $this;
    }

    public function getGate(): ?IConfig
    {
        return $this->gate;
    }

    public function setGate(?PaymentGate $gate): ExternalPayment
    {
        $this->gate = $gate;
        return $this;
    }

    public function getRedirectUrl(): ?string
    {
        return $this->redirectUrl;
    }

    public function setRedirectUrl(?string $redirectUrl): ExternalPayment
    {
        $this->redirectUrl = $redirectUrl;
        return $this;
    }

    public function getDescription(): ?string
    {
        return "Doładowanie karty";
    }

    public function getTopUp(): Collection
    {
        return $this->topUp;
    }

    #[Groups(['default:basic'])]
    public function getCurrency(): ?Currency
    {
        return $this->getGate()?->getCurrency() ?? null;
    }

    #[Groups(['default:basic'])]
    public function getType(): ?GateList
    {
        return $this->getGate()?->getType();
    }

    public function getClient(): ?Clients
    {
        return $this->user->getClient();
    }
}
