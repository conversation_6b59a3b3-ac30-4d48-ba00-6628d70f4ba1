<?php

namespace App\Model\User;

use App\Entity\Enum\Languages;

class UserRegister
{
    private string $email;
    private string $plainPassword;
    private Languages $lang; // wartosc domyslna do usuniecia po aktualizacji aplikacji

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): UserRegister
    {
        $this->email = $email;
        return $this;
    }

    public function getPlainPassword(): string
    {
        return $this->plainPassword;
    }

    public function setPlainPassword(string $plainPassword): UserRegister
    {
        $this->plainPassword = $plainPassword;
        return $this;
    }

    public function getLang(): Languages
    {
        return $this->lang;
    }

    public function setLang(Languages $lang): UserRegister
    {
        $this->lang = $lang;
        return $this;
    }
}
