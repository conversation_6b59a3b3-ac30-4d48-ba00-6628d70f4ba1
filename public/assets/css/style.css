@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap');

html {
    font-family: Roboto, sans-serif;
    font-weight: 400
}

@media only screen and (min-width: 0) {
    html {
        font-size: 14px
    }
}

@media only screen and (min-width: 992px) {
    html {
        font-size: 13.5px
    }
}

@media only screen and (min-width: 1200px) {
    html {
        font-size: 13px
    }
}

.clearfix {
    clear: both;
}

ul, ul li {
    list-style-type: none;
}

.align-justify {
    text-align: justify;
}

.p-20 {
    padding: 20px !important;
}

.pt-0 {
    padding-top: 0 !important;
}

.mb-10 {
    margin-bottom: 10px !important;
}

img {
    border: 0;
}

ul {
    padding: 0;
}

.card-panel {
    box-shadow: 0 1px 2px rgba(0, 0, 0, .26) !important;
}

body {
    margin: 0;
}

section {
    display: block;
}

b {
    font-weight: 700;
}

*, :after, :before {
    box-sizing: inherit;
}

.card-panel {
    background-color: #fff;
    border-radius: 2px;
    transition: box-shadow .25s;
}

.card-panel {
    padding: 2rem;
}

::-webkit-input-placeholder {
    color: #d1d1d1;
}

:-moz-placeholder, ::-moz-placeholder {
    color: #d1d1d1;
}

:-ms-input-placeholder {
    color: #d1d1d1;
}

.hiddendiv {
    word-wrap: break-word;
    display: none;
    overflow-wrap: break-word;
    padding-top: 1.2rem;
    white-space: pre-wrap;
}

.card-panel:after, .card-panel:before {
    clear: both;
    content: "";
    display: block;
}

.card-panel {
    margin: 0;
}

body {
    position: relative;
}

#sign-up {
    background: #35384c;
    height: 100%;
    min-height: 100%;
    min-height: 100vh;
    padding-top: 80px;
}

.thumbnail-150 {
    max-height: 150px;
    max-width: 150px;
}

.thumbnail-150 {
    max-height: 150px;
    max-width: 150px;
}

#sign-up {
    /* background: url(../background.jpg); */
    background-color: #ff0000ee;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    display: flex;
    flex-direction: row;
    justify-content: center;
}

.card-panel-logo {
    background-color: transparent;
    color: #fff;
    padding: 0;

}

.card-panel-header {
    background-color: rgba(226, 22, 41, .8);
    color: #fff;
    font-weight: 700;
    padding: 0;
    text-transform: uppercase;
}

.card-panel-header {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    padding: 1.3rem 2rem;
}

.card-panel {
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.card-panel-logo {
    padding: 1.3rem;
}

.terms-of-use li {
    list-style-type: disc;
    margin-bottom: 5px;
    margin-left: 45px;
}

.terms-of-use p {
    margin-bottom: 16px;
    margin-left: 15px;
}

.terms-of-use div {
    font-size: 1.166rem;
    line-height: 1.2826rem;
}

.terms-of-use * {
    margin: 0;
}

@media only screen and (min-width: 767px) and (max-width: 992px) {
    #sign-up {
        padding-top: 80px;
    }
}

@media only screen and (max-width: 767px) {
    #sign-up {
        padding-top: 0;
    }
}

.fb_reset {
    background: none;
    border: 0;
    border-spacing: 0;
    color: #000;
    cursor: auto;
    direction: ltr;
    font-family: "lucida grande", tahoma, verdana, arial, sans-serif;
    font-size: 11px;
    font-style: normal;
    font-variant: normal;
    font-weight: normal;
    letter-spacing: normal;
    line-height: 1;
    margin: 0;
    overflow: visible;
    padding: 0;
    text-align: left;
    text-decoration: none;
    text-indent: 0;
    text-shadow: none;
    text-transform: none;
    visibility: visible;
    white-space: normal;
    word-spacing: normal;
}

.fb_reset > div {
    overflow: hidden;
}
