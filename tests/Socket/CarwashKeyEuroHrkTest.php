<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Tests\Socket;

use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use App\Entity\Carwash;
use App\Service\CarwashCrypto;

/**
 * Description of CarwashApiControllerTest
 *
 * <AUTHOR>
 */
class CarwashKeyEuroHrkTest extends WebTestCase {

     /**
     * @test
     * karta która juz istnieje w systemie
     */
    public function conversionToEuro() {
        $this->markTestIncomplete();
        $sn = 150;
        $plc = 'CE530169488';
        $mac = '00:60:65:3A:FF:88';
        $cardNumber = **********;



        $crypto = new CarwashCrypto();
        $client = static::createClient();

        $carwash = (new Carwash())
            ->setSn($sn);

        $edgeDevice = (new Carwash\EdgeDevice())
            ->setUid($plc)
            ->setMac($mac)
            ->setCarwash($carwash);

        // wlozenie karty
        $ask = [
            'rCredit'  => 100,
            "sCurrency" => 'EUR'
        ];

        $body = $crypto->encrypt($edgeDevice, json_encode($ask), CarwashCrypto::PASS_SOCKET);
        $url = "/plc/$plc?key=$cardNumber";
        $client->request('GET', $url, [], [], [], $body);
        $this->assertEquals(200, $client->getResponse()->getStatusCode());
        $text = $crypto->decrypt($edgeDevice, $client->getResponse()->getContent(), CarwashCrypto::PASS_SOCKET);
        $data = json_decode($text);
        $this->assertEquals(-73.46, $data->g_i2m_rev->value);

    }

    /**
     * @test
     * nie ma tej karty w systemie
     */
    public function noConversionToEuro() {
        $sn = 10001;
        $plc = 'E5700171161';
        $mac = '00:60:65:81:86:A9';
        $cardNumber = **********;

        $crypto = new CarwashCrypto();
        $client = static::createClient();

        $carwash = (new Carwash())
            ->setSn($sn);

        $edgeDevice = (new Carwash\EdgeDevice())
            ->setUid($plc)
            ->setMac($mac)
            ->setCarwash($carwash);

        // wlozenie karty
        $ask = [
            'rCredit'  => 100,
            "sCurrency" => 'EUR'
        ];

        $body = $crypto->encrypt($edgeDevice, json_encode($ask), CarwashCrypto::PASS_SOCKET);
        $url = "/plc/$plc?key=$cardNumber";
        $client->request('GET', $url, [], [], [], $body);
        $this->assertEquals(200, $client->getResponse()->getStatusCode());
        $text = $crypto->decrypt($edgeDevice, $client->getResponse()->getContent(), CarwashCrypto::PASS_SOCKET);
        $data = json_decode($text);
        $this->assertEquals(0, $data->g_i2m_rev->value);

    }


    // Konwersja karty:
    // * karta używana - waluta null - konwersja
    // * karta używana - waluta HRK - konwersja
    // * karta używana - waluta EUR - konwersja
    // * karta używana - waluta EUR, a na myjni <> EURO
    // * nowa karta - jeśli klient po konwersji kredytu nie zmieniamy, ustawiamy na walutę euro. Jeśli klient przed konwersją wtedy HRK


    // do doładowań z CM wykorzystuj tylko doładowania z walutą taką  samą jak karty.

    // jeśli właściciel nie jest przekonwertowany wtedy doładowania z CM dodajemy z HRK, jeśli przekonwertowany wtedy EUR

    // w momencie konwersji właściciela
    // # karty
    // +* zmieniamy wszystkie doładowania historyczne z rekalkulacją
    // +* zmianiamy wszystkie transakcje z rekalkulacją
    // * zmianiamy wszystkie balance na kartach z rekalkulacją

    // # obrót
    // * zmieniamy obrót z rekalkulacją
    //   * +stanowiska
    //   * rozmieniarki
    // * zmieniamy inkasacje z rekalkulacją
    //   * stanowiska
    //   * odkurzacze





}
