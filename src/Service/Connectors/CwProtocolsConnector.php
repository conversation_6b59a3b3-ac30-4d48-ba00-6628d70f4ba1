<?php

namespace App\Service\Connectors;

use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpClient\RetryableHttpClient;
use Psr\Log\LoggerInterface;
use Exception;

use function Sentry\captureException;

class CwProtocolsConnector
{
    private $http;
    private $logger;

    public function __construct(
        HttpClientInterface $http,
        LoggerInterface $logger
    ) {
        $this->http = new RetryableHttpClient($http);
        $this->logger = $logger;
    }

    public function socket(
        string $url,
        string $body,
        ?string $ip
    ) {

        try {
            $response = $this->http->request('POST', $url, [
                'body' => $body,
                'headers' => [
                    'X-BKF-Source-Ip' => $ip,
                ],
            ]);

            $raw = $response->getContent();
            $statusCode = $response->getStatusCode();
            if ($statusCode != 200) {
                $this->logger->error("Status code: $statusCode url: $url data: $raw");
            } else {
                $this->logger->notice("Status code: $statusCode url: $url data: $raw");
            }
            return ['code' => $statusCode,
                'body' => $raw ];
        } catch (Exception $e) {
            $this->logger->error("Exception with socket url: " . $url
                    . " Ex: " . $e->getMessage()); //->setExtra($data);
            captureException($e);

            return ['code' => 500,
                'body' => substr($e->getMessage(), 0, 64) ];
        }
    }
}
