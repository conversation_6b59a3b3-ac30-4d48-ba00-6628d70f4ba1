<?php

namespace App\Service\Loyalty;

use App\Entity\Carwash;
use App\Entity\Currency;
use App\Entity\ExternalPayment\ExternalPayment;
use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\CyclicTopUp;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Loyalty\Enum\TopUpStatus;
use App\Entity\Loyalty\Enum\TopUpType;
use App\Entity\Loyalty\Enum\TransactionType;
use App\Entity\Loyalty\TopUp;
use App\Entity\Loyalty\Transactions;
use App\Entity\MobilePayment;
use App\Entity\Owners;
use App\Model\Loyalty\AskRevalue;
use App\Repository\Carwash\CarwashLogRepository;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Repository\Loyalty\TransactionsRepository;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use I2m\StandardTypes\Enum\CMSubscription;
use I2m\StandardTypes\Enum\Source;
use I2m\IIot\Model\Param\ParamBkfCardTransaction;
use I2m\IIot\Services\Protocols\Protocols;
use I2m\IIot\Tools\Version;

use function Sentry\captureMessage;

/**
 * Description of CardsManager
 *
 * <AUTHOR>
 */
class CardsManager
{
    private const CARWASH_OPERATIONS = 0;
    private const INTERNET_OPERATIONS = 1;
    private const PROMOTION_OPERATIONS = 2;

    private const CMD_REVALUE = 21;
    private const CMD_BLOCK = 22;
    private const CMD_CONVERT = 23;
    private const CMD_CARD_NO_EXIST = 24;
    private const CMD_SUBSCRIPTION_REQUIED = 25;
    private const CMD_CARWASH_WITHOUT_OWNER = 26;

    public function __construct(
        private CardsRepository $cardsRepository,
        private TopUpRepository $topUpRepo,
        private Protocols $protocols,
        private EntityManagerInterface $em,
        private TransactionsRepository $transactionsRepository,
        private CarwashLogRepository $carwashLogRepository,
        private Hrk2EuroService $hrk2EuroService,
        private InvoiceTopUpService $invoiceTopUpService
    ) {
    }

    private function getTransactionType($mode, $value): TransactionType
    {
        // This mode is restricted to promotions so it requires it's own type
        if ($mode === self::PROMOTION_OPERATIONS) {
            return TransactionType::PROMOTION;
        }

        switch (true) {
            case $value > 0:
                return TransactionType::ADDITION;
            case $value < 0 && ($mode == 1):
                return TransactionType::BLOCKADE;
            case $value < 0:
                return TransactionType::SUBTRACTION;
            default:
                return TransactionType::INITIAL;
        }
    }

    public function addTransaction(Carwash $carwash, ParamBkfCardTransaction $cardDto)
    {
        if (!$cardDto->cardNumber) {
            return null;
        }

        $this->carwashLogRepository->log($carwash, 'LOYALTY', $cardDto->cardNumber, json_encode($cardDto));

        $source = $this->getTransactionSource($cardDto->mode, $cardDto->source);
        $type = $this->getTransactionType($cardDto->mode, $cardDto->value);
        $ct = DateTime::createFromFormat('U', $cardDto->time);

        $card = $this->getCard($carwash, $cardDto->cardNumber);
        if (is_null($card)) {
            return null;
        }

        /*
         * sprawdzam czy przypadkiem ten wpis nie zostal juz dodany
         * np. dzieje sie tak w sytuacji gdy myjnia wysle dwa razy ten sam pakiet
         */
        $trans = $this->transactionsRepository->findOneBy(
            [
                'carwash' => $carwash,
                'card' => $card,
                'type' => $type,
                'source' => $source,
                'value' => $cardDto->value,
                'balance' => $cardDto->balance,
                'time' => $ct,
                'bayId' => $cardDto->bayId
            ]
        );

        if (!is_null($trans)) {
            $this->carwashLogRepository->notice(
                $carwash,
                "LOYALTY",
                $card->getNumber(),
                " zdublowany wpis z transakcją: " . json_encode($cardDto),
                false
            );
            return;
        }

        /*
         * mamy aktualny, poprzedni balans na karcie
         * oraz ostatnią transakcję, jeśli się nie zgadza dodajmy korektę
         */
        $this->correction($carwash, $card, $cardDto);



        // Should throw exception here as we might encounter nullable value from self::CARWASH_OPERATIONS
        if (null === $source) {
             throw new \LogicException('Wrong mode');
        }

        $transaction = (new Transactions())
                        ->setCurrency($card->getCurrency())
                        ->setOwner($card->getOwner())
                        ->setCarwash($carwash)
                        ->setCard($card)
                        ->setType($type)
                        ->setSource($source)
                        ->setValueInCredit($cardDto->value)
                        ->setBalanceInCredit($cardDto->balance)
                        ->setTime($ct)
                        ->setBayId($cardDto->bayId)
                            ;

        $this->em->persist($transaction);
        $this->em->flush();
        if (
            ($cardDto->value > 0)
            && (
                ($cardDto->mode === self::CARWASH_OPERATIONS)
                || ($cardDto->mode === self::PROMOTION_OPERATIONS)
            )
        ) {
            $topUp = new TopUp();
            $topUp
                ->setCard($transaction->getCard())
                ->setOwner($card->getOwner())
                ->setCurrency($card->getCurrency())
                ->setCtime($transaction->getCtime())
                ->setTopUpValueInCredit($cardDto->value)
                ->setValueInCredit(0)
                ->setSource($transaction->getSource())
                ->setType(TopUpType::from($transaction->getType()->value))
                ->setCarwash($transaction->getCarwash())
            ;


            $this->em->persist($topUp);
            $this->em->flush();

            if ($cardDto->mode === self::CARWASH_OPERATIONS) {
                $addInfo = [
                    'topUpId' => $topUp->getId(),
                    'transactionId' => $transaction->getId(),
                    'clientId' => $transaction->getCard()->getClient()?->getId(),
                ];
                $this->protocols->addParam(
                    40101,
                    $transaction->getCtime()->getTimestamp(),
                    null,
                    $addInfo
                );

                $this->protocols->flush("loyalty");
                $this->invoiceTopUpService->afterChangerTopUp($topUp);
            }
        }

        $card->setBalance($cardDto->balance);
        $card->setMtime($transaction->getCtime());
        $card->setLastContact($transaction->getCtime());
        $this->em->flush();
    }


    private function correction(Carwash $carwash, Cards $card, ParamBkfCardTransaction $cardDto)
    {
        $correction = ($cardDto->balance - $card->getBalanceInCredit() - $cardDto->value);
        $correction = round($correction, 2);

        if ($correction) {
            $transaction = new Transactions();
            $transaction->setCard($card);
            $transaction->setCarwash($carwash);
            $transaction->setType(TransactionType::ALIGNMENT);
            $transaction->setSource(Source::SCRIPT);
            $transaction->setBalanceInCredit($cardDto->balance - $cardDto->value);
            $transaction->setOwner($card->getOwner());
            $transaction->setCurrency($card->getCurrency());
            $transaction->setValueInCredit($correction);

            $transaction->setTime(DateTime::createFromFormat('U', $cardDto->time));
            $this->em->persist($transaction);
            $this->em->flush();
        }
    }

    public function getRevalue(Carwash\EdgeDevice $edgeDevice, int $card_id, ?AskRevalue $askRevalue)
    {
        $carwash = $edgeDevice->getCarwash();

        if (is_null($carwash->getOwner())) {
            return $this->revalueResponse($edgeDevice, 0, self::CMD_CARWASH_WITHOUT_OWNER);
        }
        // w wersji darmowej nie działają zdalne doładowania
        if (!$carwash->getOwner()->getSubscription()->isGreaterEqualThan(CMSubscription::BASIC)) {
            return $this->revalueResponse($edgeDevice, 0, self::CMD_SUBSCRIPTION_REQUIED);
        }

        $card = $this->getCard($carwash, $this->convert($card_id));
        if (is_null($card)) {
            return $this->revalueResponse($edgeDevice, 0, self::CMD_CARD_NO_EXIST);
        }

        $value = $this->hrk2EuroService->getRevalue($askRevalue, $card, $carwash);
        if (!is_null($value)) {
            return $this->revalueResponse($edgeDevice, $value, self::CMD_CONVERT);
        }
        //
        // https://gitlab.bkf.pl/bkf/ebkf/carwashmanager/carwash-api/-/issues/129
        //
        if ($card->getStatus() == CardStatus::ACTIVE) {
            return $this->revalueResponse($edgeDevice, $this->topUpRepo->getTotalTopUpCredits($card), self::CMD_REVALUE);
        }

        return $this->revalueResponse($edgeDevice, -999, self::CMD_BLOCK);
    }

    public function ackRevalue(Carwash $carwash, int $card_id, float $value, ?string $currencyCode, ?int $cmd = null): float
    {
        $card = $this->getCard($carwash, $this->convert($card_id));
        if (is_null($card)) {
            return 0;
        }
        // konwersja karty dokonałą się
        if ($cmd == self::CMD_CONVERT) {
            $this->hrk2EuroService->ackConvert($card, $carwash);
        }

        // If card value is negative -> card's blocked
        if ($value < 0) {
            // Add info about it to transactions table
            //$this->transactionsRepository->addCardBlockadeEntry($carwash, $card, $value);
            return 0;
        }
        return $this->topUpRepo->accountTopUp($card, $value);
    }

    /**
     * Convert int to hex and fullfill number to 8 characters
     */
    public function convert(int $number): string
    {
        return str_pad(strtoupper(dechex($number)), 8, '0', STR_PAD_LEFT);
    }

    private function getTransactionSource($mode, $source): ?Source
    {
        switch ($mode) {
            case self::CARWASH_OPERATIONS:
                return Source::from($source);

            case self::INTERNET_OPERATIONS:
                return Source::INTERNET;

            case self::PROMOTION_OPERATIONS:
                return Source::MONEY_CHANGER;
        }
        return null;
    }

    private function getCard(Carwash $carwash, string $cardNumber): ?Cards
    {

        if (is_null($carwash->getOwner())) {
            $this->carwashLogRepository->notice(
                $carwash,
                "LOYALTY",
                $cardNumber,
                "Brak przypisanego właściciela do myjni: " . json_encode($cardNumber)
            );
            return null;
        }

        /*
        * znajdz klucz w bazie, jeśli nie ma dodaj
        */
        /** @var Cards|null $card */
        $card = $this
            ->cardsRepository
            ->findOneBy([
                          'owner' => $carwash->getOwner(),
                          'number' => $cardNumber,
                      ]);

        if (is_null($card)) {
            $card = new Cards();
            $card->setNumber($cardNumber);
            $card->setStatus(CardStatus::ACTIVE);
            $card->setOwner($carwash->getOwner());
            $card->setCurrency($carwash->getOwner()->getCurrency());
            $this->em->persist($card);
            $this->carwashLogRepository->notice(
                $carwash,
                "LOYALTY",
                $cardNumber,
                "Utworzono nową kartę: $cardNumber"
            );
            $this->em->flush();
        }

        return $card;
    }

    private function revalueResponse(Carwash\EdgeDevice $edgeDevice, float $value, int $cmd): array
    {
        if (
            ($version =  Version::parseVersionId($edgeDevice->getSoftware())) &&
            ($version['branchId'] >= 220) // oprogrmaowanie < v2.20 zwraca błąd gdy w json jest nieznane pole
        ) {
            return ['value' => $value, 'cmd' => $cmd];
        } else {
            return ['value' => $value];
        }
    }

    public function getClientInfo(Carwash $carwash, int $card_id): array
    {
        $card = $this->getCard($carwash, $this->convert($card_id));
        $client =  $card->getClient();
        if (is_null($client)) {
            return ["fiscalize" => true];
        }
        return ["fiscalize" => false,
            "nip" => $client->getTaxNumber()
            ];
    }

    /**
     * @return TopUp[]
     */
    public function addTopUpWithBonus(
        Cards $card,
        float $toTopUpValue,
        TopUpStatus $status,
        string $addedBy = null,
        ?float $discount = null,
        ?CyclicTopUp $cyclicTopUp = null,
        ?ExternalPayment $payment = null,
    ): array {
        $bonusValue = (($discount ?? 0) / 100) * $toTopUpValue;
        $topUpValue =  $toTopUpValue - $bonusValue;
        $topUps = [];
        if ($topUpValue > 0) {
            $topUp1 = (new TopUp())
                ->setCard($card)
                ->setOwner($card->getOwner())
                ->setType(TopUpType::ADDITION)
                ->setSource(Source::INTERNET)
                ->setTopUpValue($topUpValue)
                ->setStatus($status)
                ->setValue(($status == TopUpStatus::ACTIVE) ? $topUpValue : 0)
                ->setCurrency($card->getCurrency())
                ->setAddedBy($addedBy)
                ->setCyclicTopUp($cyclicTopUp)
                ->setPayment($payment)
            ;

            $this->topUpRepo->save($topUp1);
            $topUps[] = $topUp1;
        }

        if ($bonusValue > 0) {
            $topUp2 = (new TopUp())
                ->setCard($card)
                ->setOwner($card->getOwner())
                ->setType(TopUpType::PROMOTION)
                ->setSource(Source::INTERNET)
                ->setTopUpValue($bonusValue)
                ->setStatus($status)
                ->setValue(($status == TopUpStatus::ACTIVE) ? $bonusValue : 0)
                ->setCurrency($card->getCurrency())
                ->setAddedBy($addedBy)
                ->setCyclicTopUp($cyclicTopUp)
                ->setPayment($payment)
            ;

            $this->topUpRepo->save($topUp2);
            $topUps[] = $topUp2;
        }

        return $topUps;
    }
    public function setCardForTopUp(TopUp $topUp, $ownerId): TopUp
    {
        $card = $this->cardsRepository->find($topUp->getCard()->getId());

        if ($ownerId != $card->getOwner()?->getId()) {
            throw new LoyaltyException("card and topup owner not match");
        }

        $topUp->setCard($card);
        return $topUp;
    }

    public function confirmTopUps(ExternalPayment $payment): void
    {
        $topUps = $this->topUpRepo->findBy(['payment' => $payment, 'status' => TopUpStatus::INITIATED]);

        if (empty($topUps)) {
            captureMessage("nie mam doładowan dla płatnosci {$payment->getId()}");
            return;
        }

        foreach ($topUps as $topUp) {
            $topUp->setValueInCredit($topUp->getTopUpValueInCredit());
            $topUp->setStatus(TopUpStatus::ACTIVE);
            $this->topUpRepo->save($topUp);
        }
    }
}
