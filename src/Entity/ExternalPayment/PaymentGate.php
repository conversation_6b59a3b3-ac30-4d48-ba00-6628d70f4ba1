<?php

namespace App\Entity\ExternalPayment;

use App\Entity\Owners;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\Payment\Enum\GateList;
use I2m\Payment\Interface\IConfig;
use I2m\Payment\Service\PaymentGateException;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\Serializer\Annotation\Groups;
use App\Repository\ExternalPayment\PaymentGateRepository;

#[ORM\Entity(repositoryClass: PaymentGateRepository::class)]
#[ORM\HasLifecycleCallbacks]
class PaymentGate implements IConfig
{
    #[ORM\Id]
    #[ORM\GeneratedValue()]
    #[ORM\Column(name: 'id', type: 'integer')]
    #[Groups(['default:basic'])]
    private ?int $id = null;

    #[ORM\OneToOne(inversedBy: 'gate', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private Owners $owner;

    #[ORM\Column(length: 16, enumType: GateList::class)]
    #[Groups(['gate:config', 'gate:payment'])]
    private GateList $type;

    #[ORM\Column()]
    #[Groups('gate:config')]
    private array $config = [];

    #[ORM\Column(nullable: true)]
    private ?string $comment = null;

    #[ORM\Column(length: 3, nullable: true, enumType: Currency::class)]
    private ?Currency $currency = null;

    #[ORM\Column(type: 'text', nullable: true)]
    #[Groups(['gate:payment'])]
    private ?string $logo;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getType(): GateList
    {
        return $this->type;
    }

    public function setType(GateList $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getConfig(): array
    {
        return $this->config;
    }

    public function setConfig(array $config): self
    {
        $this->config = $config;

        return $this;
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    public function setCurrency(Currency $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getProperty(string $property): string
    {
        $val = $this->config[$property];

        if (empty($val)) {
            throw new PaymentGateException(
                "Brak ustawienia $property dla płatnoci {$this->getType()->value} ($this->id)"
            );
        }

        return $val;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): PaymentGate
    {
        $this->comment = $comment;
        return $this;
    }

    public function getLogo(): ?string
    {
        return $this->logo;
    }

    public function setLogo(?string $logo): PaymentGate
    {
        $this->logo = $logo;
        return $this;
    }

    public function getOwner(): ?Owners
    {
        return $this->owner;
    }

    public function setOwner(Owners $owner): static
    {
        $this->owner = $owner;

        return $this;
    }

    public function isEnabled()
    {
        // docelowo mozna to zrobić jako zmienna
        return true;
    }

    public function isReady()
    {
        return
            $this->isEnabled();
    }
}
