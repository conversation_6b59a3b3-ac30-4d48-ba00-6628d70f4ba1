<?php

namespace App\Entity\Carwash;

use App\Entity\Carwash;
use App\Repository\Carwash\CarwashConnectionStatusRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table]
#[ORM\UniqueConstraint(columns: ['carwash_id', 'channel'])]
#[ORM\Entity(repositoryClass: CarwashConnectionStatusRepository::class)]
class CarwashConnectionStatus
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 255)]
    private $channel;

    #[ORM\Column(type: 'integer')]
    private $status;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $lastContact;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Carwash::class, inversedBy: 'connectionStatus')]
    private $carwash;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getChannel(): ?string
    {
        return $this->channel;
    }

    public function setChannel(string $channel): self
    {
        $this->channel = $channel;

        return $this;
    }

    public function getStatus(): ?int
    {
        return $this->status;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getLastContact(): ?\DateTimeInterface
    {
        return $this->lastContact;
    }

    public function setLastContact(\DateTimeInterface $lastContact): self
    {
        $this->lastContact = $lastContact;

        return $this;
    }

    public function getCarwash(): ?Carwash
    {
        return $this->carwash;
    }

    public function setCarwash(?Carwash $carwash): self
    {
        $this->carwash = $carwash;

        return $this;
    }
}
