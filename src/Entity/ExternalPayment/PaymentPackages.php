<?php

namespace App\Entity\ExternalPayment;

use App\Entity\Currency;
use App\Entity\ExternalPayment\Enum\PackageStatus;
use App\Entity\Loyalty\Clients;
use App\Entity\Owners;
use App\Repository\ExternalPayment\PaymentPackagesRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: PaymentPackagesRepository::class)]
class PaymentPackages
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['default:basic',"package:user"])]
    private ?int $id = null;

    #[ORM\ManyToOne(Owners::class)]
    #[ORM\JoinColumn(nullable: false)]
    private Owners $owner;

    #[ORM\Column]
    #[Groups(['default:basic',"package:user"])]
    private float $value;
    #[ORM\Column(nullable: true)]
    #[Groups(['default:basic',"package:user"])]
    private ?int $discount = null;

    #[ORM\Column]
    #[Groups(['default:basic',"package:user"])]
    private string $title;
    #[ORM\Column]
    #[Groups(['default:basic',"package:user"])]
    private string $description;

    #[ORM\Column(enumType: PackageStatus::class)]
    private PackageStatus $status = PackageStatus::ACTIVE;

    /// na potrzeby obliczenia rabatu klienta
    private ?Clients $client = null;

    public function getStatus(): PackageStatus
    {
        return $this->status;
    }

    public function setStatus(PackageStatus $status): PaymentPackages
    {
        $this->status = $status;
        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): PaymentPackages
    {
        $this->description = $description;
        return $this;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): PaymentPackages
    {
        $this->title = $title;
        return $this;
    }

    public function getDiscount(): ?int
    {
        return max(
            $this->client?->getDiscount() ?? 0,
            $this->discount ?? 0
        );
    }

    public function setDiscount(?int $discount): PaymentPackages
    {
        $this->discount = $discount;
        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): PaymentPackages
    {
        $this->value = $value;
        return $this;
    }

    public function getOwner(): Owners
    {
        return $this->owner;
    }

    public function setOwner(Owners $owner): PaymentPackages
    {
        $this->owner = $owner;
        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): PaymentPackages
    {
        $this->id = $id;
        return $this;
    }

    public function getBonus(): ?float
    {
        return (($this->discount ?? 0) / 100) * $this->value;
    }

    #[Groups(['default:basic',"package:user"])]
    public function getCurrency(): ?Currency
    {
        return $this->getOwner()->getCurrency();
    }

    public function setClient(?Clients $client): PaymentPackages
    {
        $this->client = $client;
        return $this;
    }
}
