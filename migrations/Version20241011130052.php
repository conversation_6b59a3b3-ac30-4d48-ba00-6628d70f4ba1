<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241011130052 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE owner_config DROP id, CHANGE invoice_config invoice_config JSON DEFAULT NULL COMMENT \'(DC2Type:json)\', ADD PRIMARY KEY (owner_id)');
        $this->addSql('ALTER TABLE owner_config ADD CONSTRAINT FK_717003227E3C61F9 FOREIGN KEY (owner_id) REFERENCES owners (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE top_up_copy_20240920 (id INT NOT NULL, card_id INT DEFAULT NULL, currency_id INT DEFAULT NULL, carwash_id INT DEFAULT NULL, cyclic_top_up_id INT DEFAULT NULL, owner_id INT DEFAULT NULL, top_up_value NUMERIC(20, 2) NOT NULL, status VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, ctime DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, mtime DATETIME DEFAULT NULL, value NUMERIC(20, 2) NOT NULL, canceled NUMERIC(20, 2) DEFAULT NULL, invoice INT DEFAULT NULL, type VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, source VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, added_by VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, token VARCHAR(128) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, cvv INT DEFAULT NULL) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE cards ADD is_virtual TINYINT(1) DEFAULT NULL');
        $this->addSql('ALTER TABLE transactions DROP FOREIGN KEY FK_EAA81A4CA76ED395');
        $this->addSql('ALTER TABLE owner_config DROP FOREIGN KEY FK_717003227E3C61F9');
        $this->addSql('DROP INDEX `primary` ON owner_config');
        $this->addSql('ALTER TABLE owner_config ADD id INT NOT NULL, CHANGE invoice_config invoice_config JSON DEFAULT NULL COMMENT \'(DC2Type:json)\'');
    }
}
