<?php

namespace App\Repository\Carwash;

use App\Entity\Carwash\EdgeDevice;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use I2m\StandardTypes\Enum\EdgeDeviceType;

/**
 * @extends ServiceEntityRepository<EdgeDevice>
 */
class EdgeDeviceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EdgeDevice::class);
    }

    //    /**
    //     * @return EdgeDevice[] Returns an array of EdgeDevice objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('e')
    //            ->andWhere('e.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('e.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?EdgeDevice
    //    {
    //        return $this->createQueryBuilder('e')
    //            ->andWhere('e.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }

    public function create(EdgeDeviceType $type, string $uid): ?EdgeDevice
    {
        $ed = $this->findOneBy(['type' => $type, 'uid' => $uid]);
        if (is_null($ed)) {
            $ed =
                (new EdgeDevice());
        }

        $ed
            ->setType($type)
            ->setUid($uid);

        return $this->save($ed);
    }

    public function save(EdgeDevice $ed): EdgeDevice
    {
        $this->getEntityManager()->persist($ed);
        $this->getEntityManager()->flush();
        return $ed;
    }
}
