<?php

namespace App\Controller\User;

use App\Entity\ExternalPayment\Enum\PackageStatus;
use App\Entity\Loyalty\Enum\TopUpStatus;
use App\Entity\Loyalty\Enum\TopUpType;
use App\Entity\Loyalty\TopUp;
use App\Entity\User;
use App\Model\ClientApp\PayByCardToken;
use App\Model\ClientApp\RechargeByCardToken;
use App\Model\ClientApp\SupportRequest;
use App\Repository\CarwashRepository;
use App\Repository\ExternalPayment\PaymentPackagesRepository;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Repository\Loyalty\TransactionsRepository;
use App\Service\ExternalPayment\ExtPaymentGateService;
use App\Service\Loyalty\CardsManager;
use App\Service\Loyalty\VirtualCardManager;
use App\Service\Support\SupportService;
use I2m\StandardTypes\Enum\Source;
use I2m\Invoices\Enum\PaymentStatus;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;
use App\Repository\ExternalPayment\PaymentGateRepository;

class LoyaltyCardsController extends AbstractController
{
    #[Route('/api/user/cards', methods: ['GET'])]
    public function getCards(
        CardsRepository $cardsRepository,
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $email = $user->getEmail();

        $cards = $cardsRepository->getCards(
            email: $email,
            showTotal: true
        );

        $context = [
            'groups' => [
                "loyalty:cards:user",
                "default:basic",
                "owner:logo",
                "owner:contact"
            ],
            'datetime_format' => 'Y-m-d H:i:s',
        ];

        return $this->json($cards, JsonResponse::HTTP_OK, [], $context);
    }

    #[Route('/api/user/card/{cardToken}/pay', methods: ['POST'])]
    public function pay(
        string $cardToken,
        #[MapRequestPayload] PayByCardToken $pay,
        VirtualCardManager $virtualCardManager,
        CardsRepository $cardsRepository,
        TranslatorInterface $translator
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $email = $user->getEmail();

        $card = $cardsRepository->getCardByToken(cardToken: $cardToken, email: $email);

        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }

        try {
            $transaction = $virtualCardManager->makePayment(
                card: $card,
                stand_code: $pay->getStandCode(),
                valueToPay: $pay->getValue(),
                user: $user,
                licensePlate: $pay->getLicensePlate()
            );
        } catch (\Exception $exception) {
            return $this->json(['error' => $translator->trans('error_payment_at_stand_failed'), 'code' => 1], JsonResponse::HTTP_BAD_GATEWAY);
        }


        $groups = [
            "loyalty:transaction:user",
            "default:basic",
            "owner:logo",
            "owner:contact"
        ];


        return $this->json($transaction, JsonResponse::HTTP_OK, [], ['groups' => $groups]);
    }

    #[Route('/api/user/card/{cardToken}/support', methods: ['POST'])]
    public function support(
        string $cardToken,
        #[MapRequestPayload] SupportRequest $supportRequest,
        SupportService $supportService,
        CardsRepository $cardsRepository
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $email = $user->getEmail();

        $card = $cardsRepository->getCardByToken(cardToken: $cardToken, email: $email);

        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }

        $supportService->createTicketFromCard($user, $card, $supportRequest->getDescription());
        return $this->json([], JsonResponse::HTTP_OK);
    }

    #[Route('/api/user/card/{cardToken}/transactions', methods: ['GET'])]
    public function transactions(
        string $cardToken,
        CardsRepository $cardsRepository,
        Request $request,
        TransactionsRepository $transactionsRepository
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $email = $user->getEmail();

        $card = $cardsRepository->getCardByToken(
            cardToken: $cardToken,
            email: $email
        );

        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }

        $transactions = $transactionsRepository->getHistory(
            card:     $card,
            page:     $request->query->getInt('page', 1),
            perPage:  $request->query->getInt('itemsPerPage', 50)
        );

        $groups = [
            "loyalty:transaction:user",
            "default:basic",
        ];

        return $this->json(
            $transactions,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => $groups,
            ]
        );
    }

    #[Route('/api/user/card/{cardToken}', methods: ['GET'])]
    public function getCard(
        string $cardToken,
        CardsRepository $cardsRepository,
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $email = $user->getEmail();

        $card = $cardsRepository->getCardByToken(
            cardToken: $cardToken,
            email: $email
        );

        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }

        $groups = [
            "loyalty:cards:user",
            "default:basic",
            "owner:logo",
            "owner:contact"
        ];

        return $this->json(
            $card,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => $groups,
            ]
        );
    }

    #[Route('/api/user/card/{cardToken}/recharge', methods: ['POST'])]
    public function recharge(
        string $cardToken,
        #[MapRequestPayload] RechargeByCardToken $recharge,
        ExtPaymentGateService $paymentGateService,
        CardsManager $cardsManager,
        CardsRepository $cardsRepository,
        TopUpRepository $topUpRepository,
        PaymentPackagesRepository $packagesRepository
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $email = $user->getEmail();

        $card = $cardsRepository->getCardByToken(cardToken: $cardToken, email: $email);

        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }

        if (!$card->isRechargeable()) {
            throw new BadRequestHttpException("card is not rechargable");
        }


        $package = $packagesRepository
            ->findOneBy(
                ['id' => $recharge->getId(), 'owner' => $card->getOwner()],
                ['value' => 'ASC']
            );
        if (is_null($package)) {
            throw new NotFoundHttpException("package not found");
        }

        $package->setClient($card->getClient());
        $topUps = $cardsManager
            ->addTopUpWithBonus(
                $card,
                $package->getValue(),
                TopUpStatus::INITIATED,
                "external payment: $email",
                $package->getDiscount()
            );
        $value = 0;

        /** @var TopUp[] $topUps */
        foreach ($topUps as $topUp) {
            if ($topUp->getType() === TopUpType::ADDITION) {
                $value += $topUp->getTopUpValue();
            }
        }
        $payment = $paymentGateService->init($card->getOwner(), $user, $value);

        /** @var TopUp[] $topUps */
        foreach ($topUps as $topUp) {
            $topUp->setPayment($payment);
            $topUpRepository->save($topUp);
        }

        $groups = [
            "external_payment:payment",
            "default:basic",
            "owner:logo",
            "owner:contact"
        ];


        return $this->json($payment, JsonResponse::HTTP_OK, [], ['groups' => $groups]);
    }

    #[Route('/api/user/card/{cardToken}/recharge', methods: ['GET'])]
    public function getRecharge(
        string $cardToken,
        CardsRepository $cardsRepository,
        PaymentPackagesRepository $packagesRepository,
        PaymentGateRepository $paymentGateRepository
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $email = $user->getEmail();

        $card = $cardsRepository->getCardByToken(cardToken: $cardToken, email: $email);

        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }

        if (!$card->isRechargeable()) {
            throw new BadRequestHttpException("card is not rechargable");
        }

        $packages = $packagesRepository->findBy(['owner' => $card->getOwner(), 'status' => PackageStatus::ACTIVE], orderBy: ['value' => 'DESC']);
        if (empty($packages)) {
            throw new BadRequestHttpException("packages not found for this owner");
        }

        $paymentGate = $paymentGateRepository->findOneBy(['owner' => $card->getOwner()]);

        return $this->json(
            data: [
                'data' => $packages,
                'client' => $card->getClient(),
                'paymentGate' => $paymentGate,
            ],
            status: JsonResponse::HTTP_OK,
            context: [
                'groups' =>
                [
                    'default:basic',
                    'package:user',
                    'loyalty:client:user',
                    'gate:payment'
                ]
            ]
        );
    }

    #[Route('/api/user/card/{cardToken}/carwashes', methods: ['GET'])]
    public function getCarwashes(
        string $cardToken,
        CardsRepository $cardsRepository,
        CarwashRepository $carwashRepository
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $email = $user->getEmail();

        $card = $cardsRepository->getCardByToken(cardToken: $cardToken, email: $email);

        if (is_null($card)) {
            throw new NotFoundHttpException("card not found");
        }
        $carwashes = $carwashRepository->findBy(['owner' => $card->getOwner()]);

        return $this->json(
            data: $carwashes,
            status: JsonResponse::HTTP_OK,
            context: [
                'groups' =>
                    [
                        'default:basic',
                        'carwash:list'
                    ]
            ]
        );
    }
}
