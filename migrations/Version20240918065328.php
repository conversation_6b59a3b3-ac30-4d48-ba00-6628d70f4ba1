<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240918065328 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE owner_config (id INT AUTO_INCREMENT NOT NULL, owner_id INT NOT NULL, mtime DATETIME NOT NULL, ctime DATETIME NOT NULL, support_email VARCHAR(64) DEFAULT NULL, phone VARCHAR(32) DEFAULT NULL, tax_number VARCHAR(32) DEFAULT NULL, regon VARCHAR(32) DEFAULT NULL, company_name VARCHAR(128) DEFAULT NULL, address VARCHAR(255) DEFAULT NULL, post_code VARCHAR(16) DEFAULT NULL, city VARCHAR(128) DEFAULT NULL, country VARCHAR(32) DEFAULT NULL, default_payment_terms VARCHAR(16) DEFAULT NULL, default_payment_method VARCHAR(16) DEFAULT NULL, vat_tax INT DEFAULT NULL, logo LONGTEXT DEFAULT NULL, UNIQUE INDEX UNIQ_717003227E3C61F9 (owner_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE owner_config ADD CONSTRAINT FK_717003227E3C61F9 FOREIGN KEY (owner_id) REFERENCES owners (id)');
        $this->addSql('ALTER TABLE owners CHANGE ctime ctime DATETIME NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE owner_config DROP FOREIGN KEY FK_717003227E3C61F9');
        $this->addSql('DROP TABLE owner_config');
        $this->addSql('ALTER TABLE owners CHANGE ctime ctime DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL');
    }
}
