<?php

namespace App\Service\Consumer;

use App\Entity\Carwash;
use App\Repository\Carwash\CarwashConnectionStatusRepository;
use App\Repository\Carwash\CarwashLogRepository;
use App\Repository\Carwash\EdgeDeviceRepository;
use App\Repository\CarwashRepository;
use App\Service\Loyalty\CardsManager;
use App\Service\SyncData\SyncDataService;
use Doctrine\ORM\EntityManagerInterface;
use I2m\StandardTypes\Enum\EdgeDeviceType;
use I2m\IIot\Model\Param\ParamBkfCardTransaction;
use I2m\IIot\Model\Param\ParamList;
use I2m\IIot\Model\Protocols\ProtocolsDTO;
use Psr\Log\LoggerInterface;
use Symfony\Component\Serializer\SerializerInterface;

use function Sentry\captureMessage;

class DataAnalyzer
{
    public function __construct(
        private LoggerInterface $logger,
        private SyncDataService $syncDataService,
        private EdgeDeviceRepository $edgeDeviceRepository,
        private CarwashConnectionStatusRepository $connectionStatusRepository,
        private EntityManagerInterface $em,
        private CardsManager $cardsManager,
        private SerializerInterface $serializer,
    ) {
    }

    public function analyze(ProtocolsDTO $dto, \DateTimeInterface $time)
    {

        $sn = $dto->getSn();
        $carwash = $this->syncDataService->getCarwash((int) $sn);

        if (!$carwash) {
            $mesage = "myjnia nie znaleziona: sn {$dto->getSn()} ip {$dto->getIp()}, sw {$dto->getSw()}, plc {$dto->getUid()}";
            $this->logger->error($mesage);
            captureMessage($mesage);
            return null;
        }

        $sw = $dto->getSw();
        $uid = $dto->getUid();

        if (!is_null($sw)) {
            $sw = strtolower($sw);
            $edgeDevice = $this->edgeDeviceRepository->findOneBy(['uid' => $uid, 'type' => EdgeDeviceType::PLC]);
            $edgeDevice?->setSoftware($sw);
        }

        // szukam myjni w bazie


        if (!is_null($dto->readParam())) {
            $this->analyze2($carwash, $dto->readParam());
        }

        $this->em->flush();
        $this->em->clear();
        return null;
    }

    public function analyze2(Carwash $carwash, $params)
    {

        foreach ($params as $p) {
            $dts = $p->getDt();
            $paramId = $p->getPId();
            foreach ($dts as $dt) {
                $ai = $dt->getAi();
                $time = $dt->time();
                $this->analize3($carwash, $paramId, $time, $ai);
            }
        }
        $this->em->flush();
    }

    public function analize3(Carwash $carwash, int $paramId, \DateTimeInterface $time, string $ai)
    {
        switch ($paramId) {
            case ParamList::CW_PARAM_BKFCARD_TRANSACTION:
                $transaction = $this->serializer->deserialize($ai, ParamBkfCardTransaction::class, 'json');
                $this->connectionStatusRepository->update($carwash, "LOYALTY", true, $time);
                $this->cardsManager->addTransaction($carwash, $transaction);
                break;
        }
    }
}
