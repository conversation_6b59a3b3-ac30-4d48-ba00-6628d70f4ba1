<?php

namespace App\Service\Email;

use App\Entity\Loyalty\Cards;
use App\Entity\User;
use App\Service\Loyalty\VirtualCardManager;
use Symfony\Contracts\Translation\TranslatorInterface;

class OwnerEmailService
{
    public function __construct(
        private TranslatorInterface $translator,
        private EmailSender $emailSender,
        private VirtualCardManager $virtualCardManager,
    ) {
    }

    public function sendCardAddEmail(User $user, Cards $card, string $standCode)
    {
        $owner = $card->getOwner();

        if (!$owner->getConfig()) {
            throw new \Exception("No owner config for owner {$owner->getId()}");
        }

        $supportEmail = $owner->getConfig()->getSupportEmail();
        if (!$supportEmail) {
            throw new \Exception('No support email for owner {$owner->getId()}');
        }

        $carwash = $this->virtualCardManager->getCarwashFromStandCode($standCode);
        $carwashName = $carwash->getLongName();

        if ($owner->getConfig()->isCardRegisterNotification()) {
            $title = "BKFPay " . $this->translator->trans(
                id: 'owner_email_user_create_new_card',
                parameters: [
                    '{email}' => $user->getEmail(),
                    '{cardNumber}' => $card->getNumber(),
                    '{standCode}' => $standCode,
                    '{carwashName}' => $carwashName,
                ],
                locale: $owner->getConfig()->getLanguage()->value,
            );

            $this->emailSender->sendHtml(
                to: $supportEmail,
                subject: $title,
                templateName: 'owner_email/card_add_email.html.twig',
                context: [
                    'userEmail' => $user->getEmail(),
                    'cardNumber' => $card->getNumber(),
                    'standCode' => $standCode,
                    'carwashName' => $carwashName,
                ],
            );
        }
    }
}
