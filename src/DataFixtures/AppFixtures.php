<?php

namespace App\DataFixtures;

use App\Entity\Carwash;
use App\Entity\Carwash\EdgeDevice;
use App\Entity\Currency;
use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Loyalty\Enum\TopUpStatus;
use App\Entity\Loyalty\Enum\TopUpType;
use App\Entity\Loyalty\Enum\TransactionType;
use App\Entity\Loyalty\TopUp;
use App\Entity\Loyalty\Transactions;
use App\Entity\OwnerConfig;
use App\Entity\Owners;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use I2m\StandardTypes\Enum\CMSubscription;
use I2m\StandardTypes\Enum\EdgeDeviceType;
use I2m\StandardTypes\Enum\Source;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class AppFixtures extends Fixture
{
    public function __construct()
    {
    }

    public function load(ObjectManager $manager): void
    {
        // waluta

        $currency[0] = new Currency();
        $currency[0]
            ->setCode(\I2m\StandardTypes\Enum\Currency::HRK)
            ->setRate(1);
        $manager->persist($currency[0]);

        $currency[1] = new Currency();
        $currency[1]
            ->setCode(\I2m\StandardTypes\Enum\Currency::EUR)
            ->setRate(0.5);
        $manager->persist($currency[1]);

        $currency[2] = new Currency();
        $currency[2]
            ->setCode(\I2m\StandardTypes\Enum\Currency::PLN)
            ->setRate(1);
        $manager->persist($currency[2]);

        $currency[3] = new Currency();
        $currency[3]
            ->setCode(\I2m\StandardTypes\Enum\Currency::CR)
            ->setRate(1);
        $manager->persist($currency[3]);

        // myjnia testowa
        $owner[0] = new Owners();
        $owner[0]->setId(36814);
        $owner[0]->setName("Owner testowy");
        $owner[0]->setCurrency($currency[2]);
        $owner[0]->setSubscription(CMSubscription::PREMIUM);

        $manager->persist($owner[0]);

        $owner[1] = new Owners();
        $owner[1]->setId(36815);
        $owner[1]->setName("Owner testowy 2");
        $owner[1]->setCurrency($currency[2]);
        $owner[1]->setSubscription(CMSubscription::PREMIUM);

        $manager->persist($owner[1]);

        $ownerConfig[0] = new OwnerConfig($owner[0]);
        $ownerConfig[0]->setSupportEmail('<EMAIL>');
        $ownerConfig[0]->setCardRegisterNotification(true);

        $manager->persist($ownerConfig[0]);

        $ownerConfig[1] = new OwnerConfig($owner[1]);
        $ownerConfig[1]->setSupportEmail('<EMAIL>');
        $ownerConfig[1]->setCardRegisterNotification(true);

        $manager->persist($ownerConfig[1]);

        // 150
        $carwash[150] = new Carwash();
        $carwash[150]->setSn(150);
        $carwash[150]->setId(150);
        $carwash[150]->setOwner($owner[0]);
        $carwash[150]->setName("Myjnia testowa DEiA");
        $manager->persist($carwash[150]);

        $edgeDevice[150] = (new EdgeDevice())
            ->setType(EdgeDeviceType::PLC)
            ->setUid('CE530169488')
            ->setMac('00:60:65:3A:FF:88')
            ->setCarwash($carwash[150]);
        $manager->persist($edgeDevice[150]);

        $this->setReference('carwash-150', $carwash[150]);


        //10001
        $carwash[10001] = new Carwash();
        $carwash[10001]->setSn(10001);
        $carwash[10001]->setId(10001);
        $carwash[10001]->setOwner($owner[1]);
        $carwash[10001]->setName("Myjnia mala hala");
        $manager->persist($carwash[10001]);

        $edgeDevice[10001] = (new EdgeDevice())
            ->setType(EdgeDeviceType::PLC)
            ->setUid('E5700171161')
            ->setMac('00:60:65:81:86:A9')
            ->setCarwash($carwash[10001]);
        $manager->persist($edgeDevice[10001]);


        //22080
        $carwash[22080] = new Carwash();
        $carwash[22080]->setSn(22080);
        $carwash[22080]->setId(22080);
        $carwash[22080]->setOwner($owner[1]);
        $carwash[22080]->setName("Myjnia jasna");
        $manager->persist($carwash[22080]);

        $edgeDevice[22080] = (new EdgeDevice())
            ->setType(EdgeDeviceType::PLC)
            ->setUid('CE530169580')
            ->setMac('00:60:65:40:E7:A0')
            ->setCarwash($carwash[22080]);
        $manager->persist($edgeDevice[22080]);

        //141
        $carwash[141] = new Carwash();
        $carwash[141]->setSn(141);
        $carwash[141]->setId(141);
        $carwash[141]->setOwner($owner[0]);
        $carwash[141]->setName("Steronik developerski");
        $manager->persist($carwash[141]);

        $edgeDevice[141] = (new EdgeDevice())
            ->setType(EdgeDeviceType::PLC)
            ->setUid('CE530168729')
            ->setMac('00:60:65:1F:01:1B')
            ->setCarwash($carwash[141]);
        $manager->persist($edgeDevice[141]);

        //jakis sterownik ktory nie istnieje
        $carwash[1] = new Carwash();
        $carwash[1]->setSn(1);
        $carwash[1]->setId(1);
        $carwash[1]->setName("nieistniejacy sterownik");
        $manager->persist($carwash[1]);

        $edgeDevice[1] = (new EdgeDevice())
            ->setType(EdgeDeviceType::PLC)
            ->setUid('CE111111111')
            ->setMac('11:11:11:11:11:11')
            ->setCarwash($carwash[1]);
        $manager->persist($edgeDevice[1]);

        $card[0] = new Cards();
        $card[0]
            ->setOwner($owner[0])
            ->setBalance(100)
            ->setCurrency($currency[0])
            ->setNumber('DEADBEAF')
            ->setStatus(CardStatus::ACTIVE)
        ;
        $manager->persist($card[0]);

        $transaction[0] =
            (new Transactions())
                ->setOwner($card[0]->getOwner())
                ->setValueInCredit(10)
                ->setCard($card[0])
                ->setCurrency($card[0]->getCurrency())
                ->setSource(Source::INTERNET)
                ->setType(TransactionType::ADDITION)
                ->setTime(new \DateTime())
            ;
        $manager->persist($transaction[0]);

        $card[1] = new Cards();
        $card[1]
            ->setOwner($owner[1])
            ->setBalance(100)
            ->setCurrency($currency[3])
            ->setNumber('DEADBEAF')
            ->setStatus(CardStatus::ACTIVE)
        ;
        $manager->persist($card[1]);


        $topUp[0] = (new TopUp())

            ->setOwner($owner[0])
            ->setCurrency($owner[0]->getCurrency()) // musi być przed value!!!
            ->setType(TopUpType::ADDITION)
            ->setSource(Source::INTERNET)
            ->setTopUpValue(100)
            ->setValue(100)
            ->setStatus(TopUpStatus::ACTIVE)
            ->setToken("token_doladowania")
            ->setCvv(123)
            ;
        $manager->persist($topUp[0]);
        $manager->flush();
    }
}
