<?php

namespace App\Normalizer;

use App\Entity\ExternalPayment\PaymentPackages;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class PackageNormalizer implements NormalizerInterface
{
    public function __construct(
        private ObjectNormalizer $normalizer
    ) {
    }

    public function normalize($object, string $format = null, array $context = [])
    {
        /** @var PaymentPackages $object */
        if (array_key_exists('client', $context)) {
            $object->setClient($context['client']);
        }

        $data = $this->normalizer->normalize($object, $format, $context);
        return $data;
    }

    public function supportsNormalization($data, string $format = null, array $context = []): bool
    {
        return  ($data instanceof PaymentPackages);
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            PaymentPackages::class => true,
        ];
    }
}
