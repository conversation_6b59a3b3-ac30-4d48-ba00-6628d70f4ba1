<?php

namespace App\Controller\Public;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Attribute\Route;

class AppRedirectController extends AbstractController
{
    #[Route('/stand/{id}', methods: ['GET'])]
    #[Route('/card/{id}', methods: ['GET'])]
    #[Route('/topup/{id}', methods: ['GET'])]
    #[Route('/welcome', methods: ['GET'])]
    public function redirectToAppStore(Request $request): RedirectResponse
    {
        $userAgent = $request->headers->get('User-Agent');

        if (str_contains(strtolower($userAgent), 'android')) {
            return new RedirectResponse(
                'https://play.google.com/store/apps/details?id=pl.bkf.virtual_cards_mobile&hl=pl'
            );
        } elseif (preg_match('/iphone|ipod|ipad/i', $userAgent)) {
            return new RedirectResponse('https://apps.apple.com/pl/app/karty-lojalno%C5%9Bciowe-bkf/id6459477692');
        }
        // domyslny android
        return new RedirectResponse(
            'https://play.google.com/store/apps/details?id=pl.bkf.virtual_cards_mobile&hl=pl'
        );
    }
}
