<?php

namespace App\Normalizer;

use App\Entity\Loyalty\Clients;
use App\Service\Alert\AlertService;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class ClientNormalizer implements NormalizerInterface
{
    public function __construct(
        private ObjectNormalizer $normalizer,
        private AlertService $alertService
    ) {
    }

    public function normalize($object, string $format = null, array $context = [])
    {
        /** @var Clients $object */
        $alerts = $this->alertService
            ->setLocale($object->getOwner()->getConfig()->getLanguage())
            ->getClientAlerts($object);
        $object->setAlerts($alerts);
        $data = $this->normalizer->normalize($object, $format, $context);
        return $data;
    }

    public function supportsNormalization($data, string $format = null, array $context = []): bool
    {
        return  ($data instanceof Clients);
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            Clients::class => true,
        ];
    }
}
