<?php

namespace App\Controller\Public;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DeleteAccountInfoController extends AbstractController
{
    // #[Route('/delete_account_info', methods: ['GET'])]
    // public function showDeleteAccountInfo(): Response
    // {
    //     return $this->render('delete_account_info.html.twig');
    // }

    #[Route('/delete_account_info', methods: ['GET'])]
    public function getPrivacyPolicy(): Response
    {
        return $this->render(
            'terms.html.twig',
            [
                'title' => 'delete_account',
                'content' => $this->getFile('delete-account.html')
            ]
        );
    }

    private function getFile($file)
    {
        $filePath =
            $this->getParameter('kernel.project_dir') .
            "/public/assets/terms/" . $file;

        // <PERSON><PERSON>ła<PERSON><PERSON> pliku
        return file_get_contents($filePath);
    }
}
