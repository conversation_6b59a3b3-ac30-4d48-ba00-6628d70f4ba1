<?php

namespace App\Service\User;

use App\Entity\User;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use League\Bundle\OAuth2ServerBundle\Event\UserResolveEvent;

#[AsEventListener(event: 'league.oauth2_server.event.user_resolve', method: 'onUserResolve')]
final class UserResolveListener
{
    public function __construct(private UserProviderInterface $userProvider, private UserPasswordHasherInterface $userPasswordHasher)
    {
    }


    public function onUserResolve(UserResolveEvent $event): void
    {
        try {
            $user = $this->userProvider->loadUserByIdentifier($event->getUsername());
        } catch (AuthenticationException $e) {
            return;
        }

        if (!($user instanceof PasswordAuthenticatedUserInterface)) {
            return;
        }

        /** @var User $user */
        if (!$user->isEnabled() || !$user->isVerified()) {
            return;
        }

        if (!$this->userPasswordHasher->isPasswordValid($user, $event->getPassword())) {
            return;
        }

        $event->setUser($user);
    }
}
