<?php

namespace App\Service\Report\Data;

use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\TopUp;
use App\Entity\Loyalty\Transactions;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\ClientsRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Repository\Loyalty\TransactionsRepository;
use App\Service\Report\Model\Data;
use I2m\StandardTypes\Enum\CMSubscription;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoyaltyClientUsageReport extends ReportDataAbstract
{
    public function __construct(
        TranslatorInterface $translator,
        private ClientsRepository $clientRepository,
        private CardsRepository $cardsRepository,
        private TransactionsRepository $transactionsRepository,
        private TopUpRepository $topUpRepository
    ) {

        parent::__construct($translator);
    }

    public function getTitle(): string
    {
        return 'reports_client_cards_clients_email_header';
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::PREMIUM)) {
            return  new Data();
        }

        $items = [];
        $topUpItems = [];

        $params = $this->getCriteria();

        $client = $this->clientRepository->findOneBy([
            'owner' => $this->getOwner(),
            'id' => $params['clientId'] ]);
        $utcTz = new \DateTimeZone('GMT-0');
        $timeZone = new \DateTimeZone($this->getOwner()->getConfig()->getTimezone()->value);
        $dateFrom = $this->getDateCriteria('dateFrom', $timeZone);
        $dateTo = $this->getDateCriteria('dateTo', $timeZone)->setTime(23, 59, 59);

        $clientData = [
            ['name' => $this->trans('reports_client_cards_name'), 'data' => $client->getCompanyName()],
            ['name' => $this->trans('reports_client_cards_taxNumber'), 'data' => $client->getTaxNumber()],
            ['name' => $this->trans('reports_client_cards_regonNumber'), 'data' => $client->getRegon()],
            ['name' =>
                $this->trans('reports_client_cards_city'),
                'data' => "{$client->getPostCode()} {$client->getCity()}"
            ],
            ['name' => $this->trans('reports_client_cards_address'), 'data' => $client->getAddress()],
        ];

        $cards = $this->cardsRepository->getCards(
            owner:  $this->getOwner(),
            clientId:   $client->getId(),
            date_from:  $dateFrom,
            date_to: $dateTo,
        )['data'];

        foreach ($cards as $card) {
            $transactions = [];

                $transactionsList = $this->transactionsRepository->getHistory(
                    $this->getOwner(),
                    card: $card,
                    date_from: $dateFrom,
                    date_to: $dateTo,
                    page: null,
                    perPage: null,
                    orderBy: 'time',
                    orderDir: 'DESC',
                )['data'];

            foreach ($transactionsList as $trans) {
                $transCurrencySymbol = $trans->getCurrency()->getSymbol();
                /** @var Transactions $trans */
                $transactions[] = [
                    'cardNumber' => $trans->getCard()->getNumber(),
                    'type' => $trans->getType()->value,
                    'source' => $trans->getSource()->value,
                    'carwashName' => $trans->getCarwash()?->getLongName() ?? null,
                    'time' => (new \DateTime($trans->getTime()->format('c'), $utcTz))
                        ->setTimezone($timeZone)->format('Y-m-d H:i:s'),
                    'balance' => $this->formatCurrency($trans->getBalance(), $transCurrencySymbol),
                    'value' =>  $this->formatCurrency($trans->getValue(), $transCurrencySymbol),
                    'typeName' => $this->getTransactionTypeTrans(
                        type: $trans->getType()->value,
                        source: $trans->getSource()->value
                    ),
                    'stand' => "{$trans->getCarwash()?->getLongName()} / {$trans->getBayId()}"
                ];
            }


            $statsTransactions = $this->transactionsRepository->getStats(
                $card->getCurrency(),
                $card,
                null,
                $dateFrom,
                $dateTo,
            );
            $subtraction = $statsTransactions['SUBTRACTION'];

            $cardCurrencySymbol = $card->getCurrency()->getSymbol();
            $statsTopUp = $this->topUpRepository->getStats($card, $this->getOwner(), $dateFrom, $dateTo);
            $toSend = $this->formatCurrency($this->topUpRepository->getTotalTopUp($card), $cardCurrencySymbol);

            $balance = $this->formatCurrency($card->getBalance(), $cardCurrencySymbol);
            $payments = $this->formatCurrency(
                $subtraction['CAR_WASH'] + $subtraction['VACUUM_CLEANER'] + $subtraction['DISTRIBUTOR'],
                $cardCurrencySymbol
            );
            $topupsSum = $this->formatCurrency(
                $statsTopUp['ADDITION']["INTERNET"] + $statsTopUp['ADDITION']["MONEY_CHANGER"],
                $cardCurrencySymbol
            );

            $sumTopUpPromotion = $this->formatCurrency(($statsTopUp['PROMOTION']['INTERNET']
                + $statsTopUp['PROMOTION']['MONEY_CHANGER']), $cardCurrencySymbol);
            $internetTopUpPromotion =
                $this->formatCurrency($statsTopUp['PROMOTION']['INTERNET'], $cardCurrencySymbol);
            $topUpInternet =
                $this->formatCurrency($statsTopUp['ADDITION']['INTERNET'], $cardCurrencySymbol);
            $carwashTopUps =
                $this->formatCurrency($statsTopUp['ADDITION']['MONEY_CHANGER'], $cardCurrencySymbol);
            $carwashTopUpsPromotion =
                $this->formatCurrency($statsTopUp['PROMOTION']['MONEY_CHANGER'], $cardCurrencySymbol);

            /** @var Cards $card */
            $items[] = [
                'alias' => $card->getAlias(),
                'number' => $card->getNumber(),
                'balance' => "{$balance} (+{$toSend})",
                'lastContact' => $card->getLastContact()
                    ? (new \DateTime($card->getLastContact()->format('c'), $utcTz))
                        ->setTimezone($timeZone)->format('Y-m-d H:i:s') : null,
                'payments' => $payments,
                'topUps' => "{$topupsSum} (+{$sumTopUpPromotion})",
                'topUpInternet' => "{$topUpInternet} (+{$internetTopUpPromotion})",
                'topUpCarwash' => "{$carwashTopUps} (+{$carwashTopUpsPromotion})",
                'currencySymbol' => $cardCurrencySymbol,
                'transactions' => $transactions,
            ];
        }



            $topups = $this->topUpRepository->getHistory(
                owner: $this->getOwner(),
                date_from: $dateFrom,
                date_to: $dateTo,
                page: null,
                perPage: null,
                orderBy: 'ctime',
                orderDir: 'DESC',
                client: $client,
            )['data'];

        foreach ($topups as $topUp) {
            $topUpCurrencySymbol = $topUp->getCurrency()->getSymbol();
            /** @var TopUp $topUp */
            $topUpItems[] = [
                'ctime' => (new \DateTime($topUp->getCtime()->format('c'), $utcTz))
                    ->setTimezone($timeZone)->format('Y-m-d H:i:s'),
                'cardAlias' => $topUp->getCard()->getAlias(),
                'cardNumber' => $topUp->getCard()->getNumber(),
                'topUpValue' => $this->formatCurrency($topUp->getTopUpValue(), $topUpCurrencySymbol),
                'source' => $topUp->getSource()->value,
                'sourceName' => $this->getTopUpSourceTrans($topUp->getSource()->name),
                'type' => $topUp->getType()->value,
                'typeName' => $this->getTopUpTypeTrans($topUp->getType()->value),
                'invoiceNumber' => $topUp->getInvoice2()?->getNumber() ?? '-',
            ];
        }



        $data = new Data(
            data: [
                'cards' => $items,
                'topUps' => $topUpItems,
                'client' => $clientData,
            ]
        );

        return $data;
    }

    private function getTopUpSourceTrans(string $source): string
    {
        if ($source == 'MONEY_CHANGER') {
            return $this->trans('reports_client_cards_transactions_money_changer');
        }
        if ($source == 'INTERNET') {
            return $this->trans('reports_client_cards_transactions_internet');
        }

        return $source;
    }

    private function getTopUpTypeTrans(string $source): string
    {
        if ($source == 'ADDITION') {
            return $this->trans('reports_client_cards_transactions_topup');
        }
        if ($source == 'PROMOTION') {
            return $this->trans('reports_client_cards_promotion');
        }

        return $source;
    }

    private function getTransactionTypeTrans(string $type, string $source): string
    {
        if ($type == 'SUBTRACTION') {
            if ($source == 'INTERNET') {
                return $this->trans('reports_client_cards_transactions_payment_for_wash');
            }
            if ($source == 'VACUUM_CLEANER') {
                return $this->trans('reports_client_cards_transactions_payment_for_vacuum');
            }
            if ($source == 'DISTRIBUTOR') {
                return $this->trans('reports_client_cards_transactions_payment_from_distributor');
            }

            return $this->trans('reports_client_cards_transactions_payment_for_wash');
        }

        if ($type == 'ADDITION') {
            if ($source == 'MONEY_CHANGER') {
                return $this->trans('reports_client_cards_transactions_topup_from_money_changer');
            }
            if ($source == 'INTERNET') {
                return $this->trans('reports_client_cards_transactions_internet_topup');
            }
            if ($source == 'CAR_WASH') {
                return $this->trans('reports_client_cards_transactions_topup_from_carwash');
            }
            if ($source == 'DISTRIBUTOR') {
                return $this->trans('reports_client_cards_transactions_topup_from_distributor');
            }
            if ($source == 'purchase') {
                return $this->trans('reports_client_cards_transactions_purchase');
            }
        }

        if ($type == 'ALIGNMENT') {
            return $this->trans('reports_client_cards_transactions_balance_adjustment');
        }

        if ($type == 'PROMOTION') {
            return $this->trans('reports_client_cards_transactions_promotions');
        }

        return "{$type} - {$source}";
    }

    public function getTables(): array
    {
        $dreportData = $this->getData();
        if (!$dreportData->isGenerated()) {
            return [];
        }

        $tables = [];

        $data = $dreportData->getData();
        $topUpsData = $data['topUps'];
        $cardsData = $data['cards'];
        $clientData = $data['client'];

        $clientColumns = [
            new Column("name", "reports_client_cards_client", class: "align-left"),
            new Column("data", "", class: "align-left"),
        ];

        $tables[] = new Table(
            name: $this->trans('reports_client_cards_client'),
            columns: $clientColumns,
            items: $clientData,
        );

        $cardsColumns = [
            new Column("alias", "reports_client_cards_key", class: "align-left"),
            new Column("number", "reports_client_cards_cardnumber", class: "align-left"),
            new Column("balance", "reports_client_cards_card_funds", class: "align-right"),
            new Column("lastContact", "reports_client_cards_last_usage", class: "align-left"),
            new Column("payments", "reports_client_cards_transactions_payments", class: "align-right"),
            new Column("topUps", "reports_client_cards_transactions_topup", class: "align-right"),
            new Column("topUpInternet", "reports_client_cards_added_internet_topups", class: "align-right"),
            new Column("topUpCarwash", "reports_client_cards_carwash_topups", class: "align-right"),
        ];

        $tables[] = new Table(
            name: $this->trans('reports_client_cards_cards'),
            columns: $cardsColumns,
            items: $cardsData,
        );

        $topUpColumns = [
            new Column("ctime", "reports_client_cards_transaction_date", class: "align-left"),
            new Column("cardAlias", "reports_client_cards_key", class: "align-left"),
            new Column("cardNumber", "reports_client_cards_cardnumber", class: "align-right"),
            new Column("topUpValue", "reports_client_cards_value", class: "align-right"),
            new Column("sourceName", "reports_client_cards_source", class: "align-right"),
            new Column("typeName", "reports_client_cards_transaction_type", class: "align-right"),
            new Column("invoiceNumber", "reports_client_cards_invoice_number", class: "align-right"),
        ];

        $tables[] = new Table(
            name: $this->trans("reports_client_cards_transactions_topups"),
            columns: $topUpColumns,
            items: $topUpsData,
        );

        $transactionsColumns = [
            new Column("time", "reports_client_cards_transaction_date", class: "align-left"),
            new Column("typeName", "reports_client_cards_transaction_type", class: "align-left"),
            new Column("carwashName", "reports_client_cards_stand", class: "align-left"),
            new Column("value", "reports_client_cards_value", class: "align-right"),
            new Column("balance", "reports_client_cards_value_after_transaction", class: "align-right"),
        ];

        foreach ($cardsData as $card) {
            $transactions = $card['transactions'];

            if (!empty($transactions)) {
                $tables[] = new Table(
                    name: $this->trans('reports_client_cards_last_transactions') . ' ' . $card['number'],
                    columns: $transactionsColumns,
                    items: $transactions,
                );
            }
        }

        return $tables;
    }

    public function formatCurrency($value, string $currencySymbol)
    {

            return number_format($value, 2) . ' ' . $currencySymbol;
    }
}
