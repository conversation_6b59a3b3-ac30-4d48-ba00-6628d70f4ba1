<?php

namespace App\Controller\Public;

use App\Exception\User\InvalidTokenException;
use App\Exception\User\UserNotFoundException;
use App\Service\User\PasswordResetService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class PasswordResetController extends AbstractController
{
    public function __construct(
        private readonly PasswordResetService $passwordResetService,
    ) {
    }

    #[Route('/password-reset/request', methods: ['POST'], name: 'password_reset_request')]
    public function passwordResetRequest(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $email = trim($data['email'] ?? '');

        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $this->json(data: ['error' => 'Invalid email address'], status: JsonResponse::HTTP_BAD_REQUEST);
        }

        try {
            $this->passwordResetService->handlePasswordReset($email);
        } catch (UserNotFoundException $e) {
            return $this->json(['error' => $e->getMessage()], JsonResponse::HTTP_NOT_FOUND);
        }

        return $this->json(null, status: JsonResponse::HTTP_NO_CONTENT);
    }

    #[Route('/password-reset', methods: ['GET'], name: 'password_reset_form')]
    public function passwordResetForm(Request $request): Response
    {
        $token = trim($request->query->get('token'));

        try {
            $this->passwordResetService->validateToken($token);
        } catch (InvalidTokenException $e) {
            return $this->json(['error' => $e->getMessage()], JsonResponse::HTTP_BAD_REQUEST);
        }

        return $this->render('password_reset/password_reset_form.html.twig', [
            'token' => $token,
        ]);
    }


    #[Route('/password-reset/confirm', methods: ['POST'], name: 'password_reset_confirm')]
    public function passwordResetConfirm(Request $request): Response
    {
        $token = trim($request->request->get('token'));
        $newPassword = trim($request->request->get('password'));

        if (empty($token) || empty($newPassword)) {
            return $this->json(['error' => 'Token and password are required'], JsonResponse::HTTP_BAD_REQUEST);
        }

        try {
            $this->passwordResetService->confirmPasswordReset($token, $newPassword);
        } catch (InvalidTokenException | UserNotFoundException $e) {
            return $this->json(['error' => $e->getMessage()], JsonResponse::HTTP_BAD_REQUEST);
        }

        return $this->render('password_reset/password_reset_success.html.twig');
    }
}
