<?php

namespace App\Model\ClientApp;

class PayByCardToken
{
    private string $standCode;
    private float $value;
    private ?string $licensePlate = null;

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): PayByCardToken
    {
        $this->value = $value;
        return $this;
    }

    public function getStandCode(): string
    {
        return $this->standCode;
    }

    public function setStandCode(string $standCode): PayByCardToken
    {
        $this->standCode = $standCode;
        return $this;
    }

    public function getLicensePlate(): ?string
    {
        return $this->licensePlate;
    }

    public function setLicensePlate(?string $licensePlate): PayByCardToken
    {
        $this->licensePlate = $licensePlate;
        return $this;
    }
}
