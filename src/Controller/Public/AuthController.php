<?php

namespace App\Controller\Public;

use App\Exception\User\InvalidTokenException;
use App\Model\User\UserRegister;
use App\Service\User\UserExistException;
use App\Service\User\UserService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Translation\LocaleSwitcher;

class AuthController extends AbstractController
{
    public function __construct(
        private readonly UserService $userService,
        private readonly LocaleSwitcher $localeSwitcher,
    ) {
    }

    #[Route('/register', methods: ['POST'])]
    public function register(
        #[MapRequestPayload] UserRegister $userDto,
    ): JsonResponse {
        try {
            $this->userService->create($userDto);

            return $this->json(null, status: JsonResponse::HTTP_CREATED);
        } catch (UserExistException $e) {
            return $this->json(data: ['error' => $e->getMessage()], status: JsonResponse::HTTP_CONFLICT);
        }
    }

    #[Route('/register/confirm', methods: ['GET'], name: 'register_confirm')]
    public function registerConfirm(Request $request): Response
    {
        $token = trim($request->query->get('token'));

        try {
            $user =  $this->userService->confirmRegistration($token);

            return $this->localeSwitcher->runWithLocale($user->getLocale(), function () {
                return $this->render('registration/registration_confirmed.html.twig');
            });
        } catch (InvalidTokenException $e) {
            return $this->json(['error' => $e->getMessage()], JsonResponse::HTTP_BAD_REQUEST);
        }
    }

    #[Route('/invite/register', methods: ['GET'], name: 'invite_register')]
    public function inviteRegister(Request $request): Response
    {
        $token = trim($request->query->get('token'));
        $user = $this->userService->findUserByRegistrationToken($token);

        if (is_null($user)) {
            return $this->render('registration/invalid_token.html.twig');
        }

        $locale = $user->getLocale();
        $email = $user->getEmail();

        return $this->localeSwitcher->runWithLocale($locale, function () use ($email, $token) {
            return $this->render(
                'registration/registration_from_invitation.html.twig',
                [
                    'email' => $email,
                    'token' => $token,
                ],
            );
        });
    }


    #[Route('/invite/confirm', methods: ['POST'], name: 'invite_confirm')]
    public function inviteConfirm(
        Request $request,
        #[MapRequestPayload] UserRegister $userDto,
    ): Response {
        $token = trim($request->query->get('token'));
        try {
            $user =  $this->userService->confirmRegistrationFromInvitation($token, $userDto->getPlainPassword());

            $locale = $user->getLocale();
            $email = $userDto->getEmail();

            return $this->localeSwitcher->runWithLocale($locale, function () use ($email) {
                return $this->render(
                    'registration/registration_from_invitation_confirmed.html.twig',
                    [
                        'email' => $email,
                    ],
                );
            });
        } catch (InvalidTokenException $e) {
            return $this->json(['error' => $e->getMessage()], JsonResponse::HTTP_BAD_REQUEST);
        }
    }
}
