<?php

namespace App\Controller\Owner;

use App\Entity\Owners;
use App\Service\QrPrintGenerator\StickerService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;

class StickerController extends AbstractController
{
    #[Route('/api/owner/{id}/sticker/{code}', methods: ['GET'])]
    public function dowload(Owners $owner, string $code, StickerService $stickerService): BinaryFileResponse
    {
        // mozna dodac tez weryfikacje czy kod nalezy do myjni tego wlasciciela
        $filePath = $stickerService->generateSingle(
            ['code' => $code],
            $owner->getConfig()->getLanguage()
        );

        // Tworzenie odpowiedzi BinaryFileResponse
        $response = new BinaryFileResponse($filePath);
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            basename($filePath)
        );

        return $response;
    }
}
