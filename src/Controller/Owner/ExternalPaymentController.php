<?php

namespace App\Controller\Owner;

use App\Model\Date\DateTimeNullableRangeRequest;
use App\Repository\ExternalPayment\ExternalPaymentRepository;
use App\Service\SyncData\SyncDataService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;

class ExternalPaymentController extends AbstractController
{
    #[Route('/api/owner/{ownerId}/external_payments', methods: ['GET'])]
    public function getTransactions(
        int $ownerId,
        Request $request,
        ExternalPaymentRepository $externalPaymentRepository,
        SyncDataService $syncDataService
    ): JsonResponse {

        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $page = $request->query->getInt('page', 1);
        $itemsPerPage = $request->query->getInt('itemsPerPage', 50);
        $status = $request->get('status') ? explode(',', $request->get('status')) : null;
        $type = $request->get('type') ? explode(',', $request->get('type')) : null;
        $search = $request->get('search');

        // Check for invalid pagination and ordering
        if (!is_numeric($page) || $page < 1 || !is_numeric($itemsPerPage) || $itemsPerPage < 1) {
            return $this->json(['error' => 'Invalid pagination parameters'], JsonResponse::HTTP_BAD_REQUEST);
        }

        $dateRange = new DateTimeNullableRangeRequest(
            $request->get('dateFrom'),
            $request->get('dateTo'),
            $request->get('timezone')
        );

        $transactions = $externalPaymentRepository->getList(
            $owner,
            $dateRange->getDateFrom(),
            $dateRange->getDateTo(),
            $status,
            $type,
            page: $page,
            perPage: $itemsPerPage,
            search: $search,
        );

        return $this->json($transactions, JsonResponse::HTTP_OK, [], [
            'groups' => [
                "external_payment:list",
                "default:basic",
            ],
            'datetime_format' => 'Y-m-d H:i:s',
            'timezone' => $dateRange->getTimezone()
        ]);
    }
}
