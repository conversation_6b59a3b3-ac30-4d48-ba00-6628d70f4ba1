<?php

namespace App\Command;

use App\Repository\OwnerRepository;
use App\Service\Loyalty\CyclicTopUpManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'loyalty:import-config:cyclic-top-up',
    description: 'Import cyclic topUp configuration',
)]
class CyclicTopUpConfigImportCommand extends Command
{
    public function __construct(
        private CyclicTopUpManager $cyclicTopUpManager,
        private OwnerRepository $ownerRepository,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Import cyclic topUp configuration')
            ->addOption('ownerId', null, InputOption::VALUE_REQUIRED, 'owner', null)
            ->addOption('filePath', null, InputOption::VALUE_REQUIRED, 'owner', null);
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $ownerId = $input->getOption('ownerId');
        $filePath = $input->getOption('filePath');

        $io->note(sprintf('You passed an arguments: ownerId %s ; filePath %s', $ownerId, $filePath));

        $owner = $this->ownerRepository->findOneBy(['id' => $ownerId]);

        if (is_null($owner)) {
            $io->error(sprintf('Owner %s not found', $ownerId));
            return Command::FAILURE;
        }

        // Parsowanie pliku csv
        // @todo parametry wpisane na sztywno
        $response = $this->cyclicTopUpManager->getConfigFromFile(
            $filePath,
            $owner,
        );

        foreach ($response->errors as $error) {
            $io->error('Error in ' . $error['place'] . ' ; ' . $error['description'] . ' ; ' . $error['content']);
        }

        if (count($response->errors) !== 0) {
            return Command::FAILURE;
        }

        if (count($response->data) == 0) {
            $io->warning('File is empty');
            return Command::FAILURE;
        }

        // Dodawanie configów
        $io->note('Trying to add configuration from file');

        $this->cyclicTopUpManager->importConfigs(
            $response->data,
            $owner
        );

        $io->note(sprintf('Adds %s configs', count($response->data)));

        $io->success('Success');
        return Command::SUCCESS;
    }
}
