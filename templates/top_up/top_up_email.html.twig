{% extends 'base_email.html.twig' %}

{% block body %}
<tr>
    <td style="padding: 15px; background-color: #fff; color: #000">
    </td>
</tr>
<tr>
    <td style="padding: 0px 20px 0px 20px; background-color: #fff; color: #000">
        <h1 style="padding-bottom: 20px">
            {{ 'top_up.notification_title' | trans }} {{ app_name }}
        </h1>
        <p style="font-size: 1.2em">{{ 'top_up.notification_content' | trans({ '%ownerName%': ownerName }) | trans({ '%card%': card }) | trans({ '%value%': value }) | trans({ '%currency%': currency }) | raw }}</p>
        <p style="font-size: 1.2em">{{ 'top_up.notification_info' | trans }}</p>
    </td>
</tr>
<tr>
    <td style="padding: 0px 20px 10px 20px; background-color: #fff; color: #000">
        <p>
            {{ 'common.regards'|trans|raw }}
        </p>
    </td>
</tr>

{% endblock body %}