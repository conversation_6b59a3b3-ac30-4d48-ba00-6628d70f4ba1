<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241010133202 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE invoice (id INT AUTO_INCREMENT NOT NULL, issuer_id INT NOT NULL, client_id INT NOT NULL, number VARCHAR(32) DEFAULT NULL, number_formatted VARCHAR(32) DEFAULT NULL, sequential_number INT DEFAULT NULL, currency VARCHAR(8) NOT NULL, total_gross DOUBLE PRECISION NOT NULL, total_net DOUBLE PRECISION NOT NULL, total_tax DOUBLE PRECISION NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', invoice_date DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', payment_date DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', service_date DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', payment_to INT NOT NULL, file_path VARCHAR(255) DEFAULT NULL, language VARCHAR(8) NOT NULL, description LONGTEXT DEFAULT NULL, paid DOUBLE PRECISION NOT NULL, external_id VARCHAR(32) DEFAULT NULL, payment_status VARCHAR(255) DEFAULT NULL, kind VARCHAR(255) DEFAULT NULL, payment_type VARCHAR(255) DEFAULT NULL, INDEX IDX_90651744BB9D6FEE (issuer_id), INDEX IDX_9065174419EB6921 (client_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE invoice_position (id INT AUTO_INCREMENT NOT NULL, invoice_id INT NOT NULL, name VARCHAR(255) NOT NULL, quantity DOUBLE PRECISION NOT NULL, gross_price DOUBLE PRECISION NOT NULL, total_gross DOUBLE PRECISION NOT NULL, tax INT DEFAULT NULL, INDEX IDX_5904BEAD2989F1FD (invoice_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE invoice ADD CONSTRAINT FK_90651744BB9D6FEE FOREIGN KEY (issuer_id) REFERENCES owners (id)');
        $this->addSql('ALTER TABLE invoice ADD CONSTRAINT FK_9065174419EB6921 FOREIGN KEY (client_id) REFERENCES clients (id)');
        $this->addSql('ALTER TABLE invoice_position ADD CONSTRAINT FK_5904BEAD2989F1FD FOREIGN KEY (invoice_id) REFERENCES invoice (id)');
        $this->addSql('ALTER TABLE owner_config ADD invoice_type VARCHAR(255) DEFAULT NULL, ADD invoice_config JSON NOT NULL COMMENT \'(DC2Type:json)\', CHANGE company_name name VARCHAR(128) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE invoice DROP FOREIGN KEY FK_90651744BB9D6FEE');
        $this->addSql('ALTER TABLE invoice DROP FOREIGN KEY FK_9065174419EB6921');
        $this->addSql('ALTER TABLE invoice_position DROP FOREIGN KEY FK_5904BEAD2989F1FD');
        $this->addSql('DROP TABLE invoice');
        $this->addSql('DROP TABLE invoice_position');
        $this->addSql('ALTER TABLE owner_config DROP invoice_type, DROP invoice_config, CHANGE name company_name VARCHAR(128) DEFAULT NULL');
    }
}
