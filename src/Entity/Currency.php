<?php

namespace App\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use <PERSON>ymfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(readOnly: true, repositoryClass: \App\Repository\CurrencyRepository::class)]
class Currency
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private int $id;

    #[ORM\Column(enumType: \I2m\StandardTypes\Enum\Currency::class, length: 3)]
    #[Groups(['default:basic'])]
    private \I2m\StandardTypes\Enum\Currency $code;

    #[ORM\Column(type: 'string', length: 8, nullable: true)]
    #[Groups(['default:basic'])]
    private ?string $symbol = null;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2)]
    private string $rate;

    public function getId(): int
    {
        return $this->id;
    }

    public function getCode(): \I2m\StandardTypes\Enum\Currency
    {
        return $this->code;
    }

    public function setCode(\I2m\StandardTypes\Enum\Currency $code): self
    {
        $this->code = $code;
        return $this;
    }

    public function getRate(): float
    {
        return (float) $this->rate;
    }

    public function setRate(float $rate): self
    {
        $this->rate = number_format($rate, 2, '.', '');
        ;
        return $this;
    }

    public function getSymbol(): ?string
    {
        return $this->symbol;
    }

    public function setSymbol(?string $symbol): Currency
    {
        $this->symbol = $symbol;
        return $this;
    }
}
