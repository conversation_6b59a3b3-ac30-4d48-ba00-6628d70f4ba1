<?php

namespace App\Command;

use App\Repository\ExternalPayment\ExternalPaymentRepository;
use App\Repository\ExternalPayment\PaymentGateRepository;
use I2m\Payment\Enum\GateList;
use I2m\Payment\Enum\Status;
use I2m\Payment\Service\PaymentGateFactory;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'i2m:payment:corvus',
    description: 'Add a short description for your command',
)]
class I2mPaymentCorvusCommand extends Command
{
    public function __construct(
        private readonly ExternalPaymentRepository $externalPaymentRepository,
        private readonly PaymentGateFactory $paymentGateFactory,
        private readonly PaymentGateRepository $paymentGateRepository
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $gates = $this->paymentGateRepository->findBy(['type' => GateList::CORVUS]);
        $eps = $this->externalPaymentRepository->findBy(
            ['gate' => $gates, 'status' => [Status::WAITING, Status::REFUNDING]]
        );

        foreach ($eps as $ep) {
            $this->paymentGateFactory->callback($ep, null);
            $this->externalPaymentRepository->save($ep);
        }
        return Command::SUCCESS;
    }
}
