<?php

namespace App\Controller\ExternalPayment;

use App\Entity\ExternalPayment\ExternalPayment;
use App\Repository\ExternalPayment\ExternalPaymentLogRepository;
use App\Repository\ExternalPayment\ExternalPaymentRepository;
use App\Service\ExternalPayment\ExtPaymentGateService;
use App\Service\Loyalty\CardsManager;
use Detection\MobileDetect;
use I2m\Payment\Enum\Status;
use I2m\Payment\Service\CorvusPay\CorvusPayGate;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function Sentry\captureException;
use function Sentry\captureMessage;

class PaymentGateController extends AbstractController
{
    #[Route('/external_payment/gate/callback')]
    public function callback(
        Request $request,
        ExternalPaymentRepository $externalPaymentRepository,
        ExtPaymentGateService $paymentGateService,
    ): Response {

        try {
            $ep = $externalPaymentRepository->find($request->get('id'));
            $ep = $paymentGateService->handleStatus($ep, $request);
            return $this->json(['status' => 'success']);
        } catch (\Exception $e) {
            captureException($e);
            return $this->json(['status' => 'error']);
        }
    }

    #[Route('/external_payment/gate/{id}/success')]
    public function success()
    {
        $detector = new MobileDetect();
        if ($detector->isMobile()) {
            return $this->redirect('bkfpay://beloyal24.com/payment/success');
        }
        return $this->render('external_payment/success.html.twig');
    }
    #[Route('/external_payment/gate/{id}/error')]
    public function error()
    {
        $detector = new MobileDetect();
        if ($detector->isMobile()) {
            return $this->redirect('bkfpay://beloyal24.com/payment/error');
        }
        return $this->render('external_payment/error.html.twig');
    }

    #[Route('/external_payment/gate/{id}/corvus/form')]
    public function corvusInit(
        ExternalPayment $ep,
        CorvusPayGate $gate,
    ): Response {
        return new Response($gate->generateForm($ep));
    }

    // specjalne endpointy, dla corvus
    // ponieważ nie wysłają notyfikacji bezpośrednio do backendu
    // trzeba ustawić url to w panelu corvus
    #[Route('/external_payment/corvus/success')]
    public function corvusSuccess(
        Request $request,
        ExtPaymentGateService $paymentGateService,
        ExternalPaymentRepository $externalPaymentRepository
    ): Response {
        $ep = $externalPaymentRepository->find($request->get('order_number'));
        $ep = $paymentGateService->handleStatus($ep, $request);
        $detector = new MobileDetect();
        if ($detector->isMobile()) {
            return $this->redirect('bkfpay://beloyal24.com/payment/success');
        }
        return $this->render('external_payment/success.html.twig');
    }

    #[Route('/external_payment/corvus/cancel')]
    public function corvusCancel(
        Request $request,
        ExtPaymentGateService $paymentGateService,
        ExternalPaymentRepository $externalPaymentRepository
    ): Response {
        $ep = $externalPaymentRepository->find($request->get('order_number'));
        $ep = $paymentGateService->handleStatus($ep, $request);
        $detector = new MobileDetect();
        if ($detector->isMobile()) {
            return $this->redirect('bkfpay://beloyal24.com/payment/error');
        }
        return $this->render('external_payment/error.html.twig');
    }
}
