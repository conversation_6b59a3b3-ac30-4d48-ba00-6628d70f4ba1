stages:
  - test
  - deploy

test:phpunit:
  stage: test
  variables:
    APP_ENV: test
  script:
    - composer install
    - ./vendor/bin/phpcs ./src/ --extensions=php --standard=PSR12 --parallel=4 -p -n
    - ./vendor/bin/phpstan analyse --no-progress --memory-limit 1G
    - php bin/console doctrine:database:drop --if-exists --force --env=test
    - php bin/console doctrine:database:create --if-not-exists --env=test
    - php bin/console doctrine:schema:update --force --env=test
    - php bin/console doctrine:fixtures:load -n --env=test
    - php bin/console league:oauth2-server:create-client   test client_app_test app_secret --scope=USER --grant-type=password --env=test
    - php bin/console league:oauth2-server:create-client   test client_api_test api_secret --scope=API --grant-type=client_credentials --env=test
    - php ./bin/phpunit
  after_script:
    - php bin/console doctrine:database:drop --if-exists --force --env=test
  tags:
    - i2m-v2

deploy_loyalty:
  variables:
    APP_ENV: prod
    APP_DIR: /srv/${CI_PROJECT_NAME}
    REV_DIR: ${APP_DIR}/${CI_JOB_ID}
  stage: deploy
  script:
    - echo ${REV_DIR}
    - mkdir -p ${REV_DIR}
    - rsync -a --delete --exclude .git ./ ${REV_DIR}
    # composer php
    - cd ${REV_DIR}
    - composer install --optimize-autoloader
    - composer dump-env prod
    - composer dump-autoload --optimize
    - php bin/console cache:clear --env=prod --no-debug
    - php bin/console cache:warmup --env=prod
    - php bin/console doctrine:cache:clear-metadata
    - php bin/console doctrine:migrations:migrate --no-interaction --allow-no-migration
    - php bin/console i2m:version --out_file ${REV_DIR}/public/version.h
    # przepięcie środowiska
    - chmod -R 777 ${REV_DIR}
    - ln -sfn ${CI_JOB_ID} ${APP_DIR}/latest
  tags:
    - loyalty-prod
  only:
    - master
  needs: ["test:phpunit"]
