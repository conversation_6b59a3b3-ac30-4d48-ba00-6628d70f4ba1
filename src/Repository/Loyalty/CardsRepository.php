<?php

namespace App\Repository\Loyalty;

use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Loyalty\Enum\CardType;
use App\Entity\Loyalty\TopUp;
use App\Entity\Loyalty\Transactions;
use App\Entity\Owners;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Cards|null find($id, $lockMode = null, $lockVersion = null)
 * @method Cards|null findOneBy(array $criteria, array $orderBy = null)
 * @method Cards[]    findByEmail($email)
 * @method Cards[]    findAll()
 * @method Cards[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CardsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Cards::class);
    }

    public function getCards(
        ?string $search = null,
        ?string $email = null,
        ?Owners $owner = null,
        ?int $clientId = null,
        ?bool $isVirtual = null,
        ?bool $isActive = null,
        ?bool $hasAlias = null,
        ?bool $hasBalance = null,
        $page = 1,
        $perPage = 10,
        string $orderBy = 'lastContact',
        string $orderDir = 'DESC',
        ?\DateTimeInterface $date_from = null,
        ?\DateTimeInterface $date_to = null,
        ?bool $showTotal = null
    ) {
        $queryBuilder = $this->createQueryBuilder('c')
            ->leftJoin('c.client', 'client');

        if ($owner) {
            $queryBuilder
                ->andWhere('c.owner = :owner')
                ->setParameter('owner', $owner);
        }

        if ($email) {
            $queryBuilder
                ->andWhere('c.email = :email')
                ->setParameter('email', $email);
        }

        if ($clientId) {
            $queryBuilder
                ->andWhere('c.client = :client')
                ->setParameter('client', $clientId);
        }

        if ($isVirtual === true) {
            $queryBuilder
                ->andWhere('c.type = :type')
                ->setParameter('type', CardType::VIRTUAL);
        }

        if ($hasAlias === true) {
            $queryBuilder
                ->andWhere('LENGTH(c.alias) > 0');
        } elseif ($hasAlias === false) {
            $queryBuilder
                ->andWhere('(LENGTH(c.alias) = 0) or c.alias is null ');
        }

        if ($hasBalance === true) {
            $queryBuilder
                ->andWhere('c.balance > 0');
        } elseif ($hasBalance === false) {
            $queryBuilder
                ->andWhere('c.balance <= 0');
        }

        if ($isActive === true) {
            $queryBuilder
                ->andWhere('c.status = :status')
                ->setParameter(':status', CardStatus::ACTIVE)
            ;
        } elseif ($isActive === false) {
            $queryBuilder
                ->andWhere('c.status = :status')
                ->setParameter(':status', CardStatus::BLOCKED);
        }

        if ($search !== null) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->like('c.number', ':search'),
                    $queryBuilder->expr()->like('c.alias', ':search'),
                    $queryBuilder->expr()->like('c.email', ':search'),
                    $queryBuilder->expr()->like('client.companyName', ':search'),
                    $queryBuilder->expr()->like('client.taxNumber', ':search'),
                )
            )
                ->setParameter('search', "%$search%");
        }

        // szukam tylko kart użytych w danym okresie
        if (isset($date_from, $date_to)) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->exists(
                        $this->getEntityManager()->createQueryBuilder()
                            ->select('t')->from(Transactions::class, 't')
                            ->where('t.card = c AND t.time BETWEEN :date_from AND :date_to')
                    ),
                    $queryBuilder->expr()->exists(
                        $this->getEntityManager()->createQueryBuilder()
                            ->select('tp')->from(TopUp::class, 'tp')
                            ->where('tp.card = c AND tp.ctime BETWEEN :date_from AND :date_to')
                    ),
                ),
            )
                ->setParameter('date_from', $date_from)
                ->setParameter('date_to', $date_to);
        }

        $queryBuilder
            ->andWhere('c.status != :status')
            ->setParameter('status', CardStatus::DELETED);


        // zapytanie o całkowitą ilość rekordów jest bardzo długie
        // więc dajemy możliwość wyłączenia
        if ($showTotal) {
            $countQueryBuilder = clone $queryBuilder;
            $total = $countQueryBuilder->select('COUNT(c)')->getQuery()->getSingleScalarResult();
        } else {
            $total = null;
        }


        $queryBuilder->orderBy("c.$orderBy", $orderDir);

        $qb = $queryBuilder->setFirstResult(($page - 1) * $perPage)
            ->setMaxResults($perPage);
        ;

        return ['data' => $qb->getQuery()->getResult(), 'total' => $total];
    }

    public function getCardByToken(string $cardToken, ?string $email = null, ?Owners $owner = null): ?Cards
    {
        $cardArray = explode('-', $cardToken);

        if (count($cardArray) != 3) {
            return null;
        }


        if ($owner?->getId() && $cardArray[0] != $owner->getId()) {
            return  null;
        }

        $search = [
            'owner' => $cardArray[0],
            'number' => $cardArray[1],
            'token' => $cardArray[2]
        ];
        //$email ? $search['email'] = $email : null ;

        return $this->findOneBy($search);
    }

    public function getCardByNumber(string $number, Owners $owner): ?Cards
    {
        $search = [
            'number' => $number,
            'owner' => $owner
        ];

        return $this->findOneBy($search);
    }

    public function save(Cards $card)
    {
        $this->getEntityManager()->persist($card);
        $this->getEntityManager()->flush();
        return $card;
    }
}
