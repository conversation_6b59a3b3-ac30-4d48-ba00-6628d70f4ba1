<?php

namespace App\Service\Loyalty;

use App\Entity\Invoice\Invoice;
use App\Entity\Loyalty\Clients;
use App\Entity\Loyalty\Enum\ClientsInvoiceType;
use App\Entity\Loyalty\TopUp;
use App\Entity\Owners;
use App\Repository\Invoice\InvoiceRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Service\Email\EmailSender;
use I2m\StandardTypes\Enum\CMSubscription;
use I2m\StandardTypes\Enum\Currency;
use I2m\StandardTypes\Enum\Source;
use I2m\Invoices\Enum\InvoiceGeneratorType;
use I2m\Invoices\Enum\InvoiceKindType;
use I2m\Invoices\Enum\PaymentType;
use I2m\Invoices\Service\InvoiceService;
use Symfony\Component\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;

use function Sentry\captureException;

class InvoiceTopUpService
{
    public function __construct(
        private InvoiceService $invoiceService,
        private InvoiceRepository $invoiceRepository,
        private TranslatorInterface $translator,
        private TopUpRepository $topUpRepository,
        private EmailSender $emailSender
    ) {
    }

    public function afterChangerTopUp(TopUp $topUp): ?Invoice
    {
        try {
            $client = $topUp->getCard()->getClient();
            if ($client?->getInvoiceStrategy() === ClientsInvoiceType::AUTO_AFTER_TOP_UP) {
                return $this->generateSingle($topUp, PaymentType::Cash);
            }
        } catch (\Exception $e) {
            captureException($e);
        }
        return null;
    }

    public function generateSingle(
        TopUp $topUp,
        ?PaymentType $paymentMethod = null,
        ?string $paymentTerm = 'P0D',
        ?string $description = null,
    ): ?Invoice {
        $client = $topUp->getCard()->getClient();
        $transactionDate = \DateTimeImmutable::createFromInterface($topUp->getCtime());
        return $this->generateList($client, [$topUp], $transactionDate, $paymentMethod, $paymentTerm, $description);
    }

    /**
     * @param TopUp[] $topUps
     */
    public function generateList(
        Clients $client,
        array $topUps,
        \DateTimeImmutable $serviceDate,
        ?PaymentType $paymentMethod = null,
        ?string $paymentTerm = null,
        ?string $description = null,
    ): ?Invoice {
        if (!$this->checkIfCanByInvoiced($client)) {
            return null;
        }

        $owner = $client->getOwner();


        $vat = $owner->getConfig()->getVatTax();

        /** @var Translator $translator */
        $translator = $this->translator;
        $translator->setLocale($owner->getConfig()->getLanguage()->value);

        $invoice = $this->initInvoice(
            $owner,
            $client,
            $owner->getCurrency()->getCode(),
            $serviceDate
        );

        $invoice
            ->setPaymentType($paymentMethod ?? $client->getPaymentMethod())
            ->setPaymentTerm($paymentTerm ?? $client->getPaymentTerm());

        if ($description) {
            $invoice->setDescription($description);
        }

        if (!is_null($paymentTerm)) {
            $paymentData = $invoice->getInvoiceDate()->add((new \DateInterval($paymentTerm)));
            $invoice->setPaymentDate($paymentData);
        }

        $this->invoiceRepository->save($invoice);
        $paid = 0;
        foreach ($topUps as $topUp) {
            $card = $topUp->getCard();

            if ($topUp->getInvoice2()) {
                continue;
            }

            $description =
                $translator->trans('invoicing.card_recharge_message', ['{card}' => $card->getNumber(), '{date}' => $topUp->getCtime()->format('Y-m-d')]);

            if ($topUp->getSource() == Source::MONEY_CHANGER) {
                $description .= $translator->trans('invoicing.card_recharge_carwash', ['{carwash}' => $topUp->getCarwash()->getLongName()]);
                $paid += $topUp->getTopUpValue();
            }
            $invoice
                ->addPosition($description, 1, $topUp->getTopUpValue(), $vat);


            $topUp->setInvoice2($invoice);
            $this->topUpRepository->save($topUp);
        }

        $invoice->setPaid($paid);
        $this->invoiceRepository->save($invoice);

        $this->invoiceService->generate(
            $owner->getConfig()->getType(),
            $owner->getConfig()->getConfig()[$owner->getConfig()->getType()->value],
            $invoice
        );

        $this->invoiceRepository->save($invoice);

        $this->sendEmail(
            $invoice,
            $invoice->getClient()->getEmail(),
            $owner,
            $owner->getConfig()->getInvoiceCopyEmail() ? explode(';', $owner->getConfig()->getInvoiceCopyEmail()) : []
        );

        return $invoice;
    }

    private function initInvoice(Owners $owner, Clients $client, Currency $currency, \DateTimeImmutable $serviceDate): Invoice
    {
        $invoice = (new Invoice())
            ->setIssuer($owner)
            ->setCurrency($currency)
            ->setClient($client)
            ->setCreatedAt(new \DateTimeImmutable())
            ->setInvoiceDate(new \DateTimeImmutable())
            ->setLanguage($owner->getConfig()->getLanguage()->value)
            ->setKind(InvoiceKindType::vat)
            ->setServiceDate($serviceDate)
            ->setPaymentType(PaymentType::Cash)
        ;
        return $invoice;
    }

    private function checkIfCanByInvoiced(Clients $client)
    {
        if (!$client->getOwner()->getSubscription()->isGreaterEqualThan(CMSubscription::PREMIUM)) {
            return false;
        }

        if ($client->getOwner()->getConfig()->getInvoiceType() === InvoiceGeneratorType::Disabled) {
            return false;
        }

        if ($client->getInvoiceStrategy() === ClientsInvoiceType::BLOCK) {
            return false;
        }

        if (!$client->isInvocingOk()) {
            return false;
        }

        if (!$client->getOwner()->isInvocingOk()) {
            return false;
        }

        return true;
    }

    public function sendEmail(Invoice $invoice, string $email, Owners $owner, array $cc = [])
    {
        if ($invoice->getSendDate() === null) {
            $invoice->setSendDate(new \DateTimeImmutable('Now'));
            $this->invoiceRepository->save($invoice);
        }

        $file = $this->invoiceService->download($invoice);

        $this->emailSender->sendHtml(
            to: $email,
            subject: $this->translator->trans(
                id: 'invoicing.email_title',
                parameters: ['%issuer%' => $invoice->getIssuer()->getName()],
                locale: $invoice->getLanguage()
            ),
            templateName: 'invoicing/invoice_email.html.twig',
            replyTo: $owner->getConfig()->getSupportEmail(),
            context: [
                'invoice' => $invoice
            ],
            attachments: [$file],
            cc: $cc,
        );
    }
}
