<?php

namespace App\Tests\Owner;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class LoyaltyClientTest extends WebTestCase
{
    private KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
        //$kernel = self::bootKernel();
        $this->apiLogin();
    }

    private function apiLogin()
    {
        $this->client->request(
            'POST',
            "/token",
            [
                "grant_type" => "client_credentials",
                "client_id" => "client_api_test",
                "client_secret" => "api_secret"
            ]
        );
        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        $token = json_decode($this->client->getResponse()->getContent(), true)['access_token'];

        $this->client->setServerParameter('HTTP_Authorization', 'Bearer ' . $token);
    }

    /**
     * @test
     */
    public function createApiClient()
    {
        $response = $this->getResp("POST", "/api/owner/36814/clients", 200, [
            "email" =>"<EMAIL>"
        ]);

        $this->assertEquals("<EMAIL>", $response['email'] );
    }

    protected function getResp(string $method, string $uri, int $expectedStatus, array $data = []): mixed
    {
        $this->client->request(
            $method,
            $uri,
            [],
            [],
            ['HTTP_Accept' => "application/json",
                "CONTENT_TYPE" => "application/json"],
            json_encode($data)
        );

        $this->assertEquals($expectedStatus, $this->client->getResponse()->getStatusCode());
        return json_decode($this->client->getResponse()->getContent(), true);
    }
}
