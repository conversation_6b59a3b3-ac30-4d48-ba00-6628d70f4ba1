league_oauth2_server:
    authorization_server:
        private_key: '%env(resolve:OAUTH_PRIVATE_KEY)%'
        private_key_passphrase: '%env(resolve:OAUTH_PASSPHRASE)%'
        encryption_key: '%env(resolve:OAUTH_ENCRYPTION_KEY)%'
        access_token_ttl:     P30D
    resource_server:
        public_key: '%env(resolve:OAUTH_PUBLIC_KEY)%'
    scopes:
        available: ['API','USER']
        default: ['API']
    persistence:
        doctrine: null
