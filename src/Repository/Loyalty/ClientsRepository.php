<?php

namespace App\Repository\Loyalty;

use App\Entity\Cards;
use App\Entity\Loyalty\Clients;
use App\Entity\Loyalty\Enum\ClientsInvoiceType;
use App\Entity\Owners;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use I2m\StandardTypes\Enum\CMSubscription;
use I2m\Invoices\Enum\InvoiceGeneratorType;

/**
 * @extends ServiceEntityRepository<Clients>
 *
 * @method Clients|null find($id, $lockMode = null, $lockVersion = null)
 * @method Clients|null findOneBy(array $criteria, array $orderBy = null)
 * @method Clients[]    findAll()
 * @method Clients[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ClientsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Clients::class);
    }

    public function add(Clients $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Clients $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return Clients[] Returns an array of Clients objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('c.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?Clients
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
    public function save(Clients $client)
    {
        $this->getEntityManager()->persist($client);
        $this->getEntityManager()->flush();
    }

    public function getList(
        ?Owners $owner = null,
        ?int $page = 1,
        ?int $perPage = 10,
        ?string $search = null,
        ?string $invoiceStrategy = null,
        ?bool $sendSummaryReport = null
    ) {

        $queryBuilder = $this->createQueryBuilder('t')
            ->leftJoin('t.owner', 'owner')
        ;

        if ($owner) {
            $queryBuilder
                ->andWhere('t.owner = :owner')
                ->setParameter('owner', $owner);
        }

        if ($search) {
            $orX = $queryBuilder->expr()->orX();

            $orX->add($queryBuilder->expr()->like('LOWER(t.email)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(t.companyName)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(t.taxNumber)', ':search'));
            $orX->add($queryBuilder->expr()->like('LOWER(t.regon)', ':search'));

            $queryBuilder->andWhere($orX)
                ->setParameter('search', '%' . strtolower($search) . '%');
        }

        if ($invoiceStrategy) {
            $queryBuilder
                ->andWhere('t.invoiceStrategy = :invoiceStrategy')
                ->setParameter('invoiceStrategy', $invoiceStrategy);
        }

        if (!is_null($sendSummaryReport)) {
            $queryBuilder
                ->andWhere('t.sendSummaryReport = :sendSummaryReport')
                ->setParameter('sendSummaryReport', $sendSummaryReport);
        }

        $countQueryBuilder = clone $queryBuilder;
        $total = $countQueryBuilder->select('COUNT(t)')->getQuery()->getSingleScalarResult();


        $qb = $queryBuilder->setFirstResult(($page - 1) * $perPage)
            ->setMaxResults($perPage);
        ;


        return [
            'data' => $qb->getQuery()->getResult(),
            'total' => $total
        ];
    }

    /**
     * @return Clients[]
     */
    public function getForInvoicing(): array
    {
        $qb = $this->createQueryBuilder('c')
            ->leftJoin('c.owner', 'o')
            ->leftJoin('o.config', 'cfg')
        ;



        $qb
            ->where('o.subscription = :subscription')
            ->setParameter('subscription', CMSubscription::PREMIUM)
            ->andWhere('c.invoiceStrategy = :invoiceStrategy')
            ->setParameter('invoiceStrategy', ClientsInvoiceType::AGGREGATED_MONTH)
            ->andWhere('cfg.invoiceType != :type')
            ->setParameter('type', InvoiceGeneratorType::Disabled)
        ;

        return $qb->getQuery()->getResult();
    }


    /**
     * @return Clients[]
     */
    public function getForReport(): array
    {
        $qb = $this->createQueryBuilder('c')
            ->leftJoin('c.owner', 'o')
        ;

        $qb
            ->where('o.subscription = :subscription')
            ->setParameter('subscription', CMSubscription::PREMIUM)
            ->andWhere('c.sendSummaryReport = true')
        ;

        return $qb->getQuery()->getResult();
    }

    public function getClient(?int $id, Owners $owner = null): ?Clients
    {
        if (is_null($id)) {
            return null;
        }

        $qb = $this->createQueryBuilder('c')
            ->leftJoin('c.owner', 'o')
            ->where('c.id = :id')
            ->setParameter('id', $id);

        if ($owner) {
            $qb->andWhere('o.id = :ownerId')
                ->setParameter('ownerId', $owner->getId());
        }

        return $qb->getQuery()->getOneOrNullResult();
    }
}
