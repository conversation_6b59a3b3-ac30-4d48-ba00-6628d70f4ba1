<?php

namespace App\Entity\Loyalty;

use App\Entity\Loyalty\Enum\ClientsInvoiceType;
use App\Entity\Owners;
use App\Repository\Loyalty\ClientsRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\Invoices\Enum\PaymentType;
use I2m\Invoices\Interface\IClient;
use I2m\StandardTypes\Enum\Country;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: ClientsRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Clients implements IClient
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[ORM\Column(name: 'id', type: 'integer')]
    #[Groups(['default:basic', 'loyalty:client:owner', 'loyalty:client:user'])]
    private int $id;

    #[ORM\Column(type: 'datetime')]
    #[Groups(['loyalty:client:owner'])]
    private \DateTimeInterface $mtime;

    #[ORM\Column(type: 'datetime')]
    #[Groups(['loyalty:client:owner'])]
    private \DateTimeInterface $ctime;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Owners::class)]
    private Owners $owner;

    #[Assert\Email(message: "Invalid email format.")]
    #[ORM\Column(type: 'string', length: 64, nullable: true)]
    #[Groups(['default:basic','loyalty:client:owner', 'loyalty:client:user'])]
    private ?string $email = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[Groups(['loyalty:client:details', 'loyalty:client:user'])]
    private ?string $phone = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[Groups(['default:basic','loyalty:client:owner', 'loyalty:client:user'])]
    private ?string $taxNumber = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[Groups(['default:basic','loyalty:client:details', 'loyalty:client:user'])]
    private ?string $regon = null;

    #[ORM\Column(type: 'string', length: 128, nullable: true)]
    #[Groups(['default:basic', 'loyalty:client:owner', 'loyalty:client:user'])]
    private ?string $companyName = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(['default:basic', 'loyalty:client:owner', 'loyalty:client:user'])]
    private ?string $address = null;

    #[ORM\Column(type: 'string', length: 16, nullable: true)]
    #[Groups(['default:basic', 'loyalty:client:owner', 'loyalty:client:user'])]
    private ?string $postCode = null;

    #[ORM\Column(type: 'string', length: 128, nullable: true)]
    #[Groups(['default:basic', 'loyalty:client:owner', 'loyalty:client:user'])]
    private ?string $city = null;

    #[ORM\Column(enumType: Country::class, length: 32, nullable: true)]
    #[Groups(['default:basic', 'loyalty:client:owner', 'loyalty:client:user'])]
    private ?Country $country = null;

    #[ORM\Column(type: 'string', length: 64, nullable: true)]
    #[Groups(['default:basic', 'loyalty:client:owner', 'loyalty:client:user'])]
    private ?string $firstname = null;

    #[ORM\Column(type: 'string', length: 64, nullable: true)]
    #[Groups(['default:basic', 'loyalty:client:owner', 'loyalty:client:user'])]
    private ?string $lastname = null;

    #[ORM\Column(enumType: ClientsInvoiceType::class, nullable: true)]
    #[Groups(['loyalty:client:owner'])]
    private ?ClientsInvoiceType $invoiceStrategy = null;

    #[ORM\Column(type: 'float', nullable: true)]
    #[Groups(['loyalty:client:owner','loyalty:client:user'])]
    private ?float $discount = null;

    #[ORM\Column(type: 'string', length: 16, nullable: true)]
    #[Groups(['loyalty:client:owner'])]
    private ?string $paymentTerm = 'P0D';

    #[ORM\Column(enumType: PaymentType::class, length: 16, nullable: true)]
    #[Groups(['loyalty:client:owner'])]
    private ?PaymentType $paymentMethod = PaymentType::Cash;

    #[ORM\Column(type: 'boolean', nullable: true)]
    #[Groups(['loyalty:client:owner'])]
    private ?bool $sendSummaryReport = null;

    #[ORM\Column(nullable: true, type: "text")]
    #[Groups(['loyalty:client:owner'])]
    private ?string $description = null;

    #[Groups(['loyalty:client:owner'])]
    private array $alerts;

    public function getId(): int
    {
        return $this->id;
    }
    public function setId(int $id): static
    {
        $this->id = $id;
        return $this;
    }

    public function getMtime(): ?\DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(\DateTimeInterface $mtime): static
    {
        $this->mtime = $mtime;

        return $this;
    }

    public function getCtime(): ?\DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(\DateTimeInterface $ctime): static
    {
        $this->ctime = $ctime;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): static
    {
        $this->email = $email;

        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): static
    {
        $this->phone = $phone;

        return $this;
    }

    public function getTaxNumber(): ?string
    {
        return $this->taxNumber;
    }

    public function setTaxNumber(?string $taxNumber): static
    {
        $this->taxNumber = $taxNumber;

        return $this;
    }

    public function getLastname(): ?string
    {
        return $this->lastname;
    }

    public function setLastname(?string $lastname): static
    {
        $this->lastname = $lastname;

        return $this;
    }

    /**
     * @deprecated use getName() instead
     */
    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    /**
     * @deprecated use setName() instead
     */
    public function setCompanyName(?string $companyName): static
    {
        $this->companyName = $companyName;

        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): static
    {
        $this->address = $address;

        return $this;
    }

    public function getPostCode(): ?string
    {
        return $this->postCode;
    }

    public function setPostCode(?string $postCode): static
    {
        $this->postCode = $postCode;

        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): static
    {
        $this->city = $city;

        return $this;
    }

    public function getCountry(): ?Country
    {
        return
            $this->country ??
            $this->getOwner()->getConfig()?->getCountry();
    }

    public function setCountry(?Country $country): static
    {
        $this->country = $country;

        return $this;
    }

    public function getOwner(): ?Owners
    {
        return $this->owner;
    }

    public function setOwner(?Owners $owner): static
    {
        $this->owner = $owner;

        return $this;
    }

    public function getInvoiceStrategy(): ?ClientsInvoiceType
    {
        return $this->invoiceStrategy;
    }

    public function invoicingEnabled(): bool
    {
        return $this->invoiceStrategy !== ClientsInvoiceType::BLOCK;
    }

    public function setInvoiceStrategy(?ClientsInvoiceType $invoiceStrategy): static
    {
        $this->invoiceStrategy = $invoiceStrategy;

        return $this;
    }

    public function getDiscount(): ?float
    {
        return $this->discount;
    }

    public function setDiscount(?float $discount): static
    {
        $this->discount = $discount;

        return $this;
    }

    public function getRegon(): ?string
    {
        return $this->regon;
    }

    public function setRegon(?string $regon): static
    {
        $this->regon = $regon;

        return $this;
    }

    public function getPaymentTerm(): ?string
    {
        return $this->paymentTerm ?? $this->getOwner()->getConfig()->getDefaultPaymentTerms();
    }

    public function setPaymentTerm(?string $paymentTerm): static
    {
        $this->paymentTerm = $paymentTerm;

        return $this;
    }

    public function getPaymentMethod(): ?PaymentType
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(?PaymentType $paymentMethod): static
    {
        $this->paymentMethod = $paymentMethod;

        return $this;
    }

    public function isSendSummaryReport(): ?bool
    {
        return $this->sendSummaryReport;
    }

    public function setSendSummaryReport(?bool $sendSummaryReport): static
    {
        $this->sendSummaryReport = $sendSummaryReport;

        return $this;
    }

    public function getFirstname()
    {
        return $this->firstname;
    }

    public function setFirstname($firstname)
    {
        $this->firstname = $firstname;
        return $this;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps(): void
    {
        if (!isset($this->ctime)) {
            $this->ctime = new \DateTime('now');
        }
        $this->mtime = new \DateTime('now');
    }

    public function getName(): string
    {
        return $this->getCompanyName();
    }

    public function setName(string $name): static
    {
        $this->setCompanyName($name);
        return $this;
    }

    public function isInvocingOk()
    {
        return
            !empty($this->taxNumber) &&
            !empty($this->companyName);
    }

    public function getAlerts(): array
    {
        return $this->alerts;
    }

    public function setAlerts(array $alerts): Clients
    {
        $this->alerts = $alerts;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }
}
