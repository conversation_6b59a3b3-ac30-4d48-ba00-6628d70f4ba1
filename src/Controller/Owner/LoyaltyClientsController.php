<?php

namespace App\Controller\Owner;

use App\Entity\Loyalty\Clients;
use App\Repository\Loyalty\ClientsRepository;
use App\Repository\OwnerRepository;
use App\Service\Report\Data\LoyaltyClientUsageReport;
use App\Service\Report\ReportService;
use App\Service\SyncData\SyncDataService;
use I2m\Reports\Enum\FileExtention;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class LoyaltyClientsController extends AbstractController
{
    #[Route('/api/owner/{ownerId}/clients', methods: ["POST"])]
    public function createClient(
        ValidatorInterface $validator,
        SerializerInterface $serializer,
        Request $request,
        ClientsRepository $clientsRepository,
        int $ownerId,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $client = $serializer->deserialize($request->getContent(), Clients::class, 'json');
        $client->setOwner($owner);

        $errors = $validator->validate($client);

        if (count($errors) > 0) {
            return $this->json([], JsonResponse::HTTP_BAD_REQUEST);
        }

        $clientsRepository->save($client);
        return $this->json(
            $client,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'loyalty:client:owner',
                    "alert:info",
                    "default:basic",
                ],
            ]
        );
    }

    #[Route('/api/owner/{ownerId}/client/{id}', methods: ["PUT"/** deprecated */, "PATCH"])]
    public function updateClient(
        ValidatorInterface $validator,
        int $id,
        int $ownerId,
        SerializerInterface $serializer,
        Request $request,
        ClientsRepository $clientsRepository,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $client = $clientsRepository->findOneBy(['id' => $id, 'owner' => $owner]);
        if (is_null($client)) {
            throw new NotFoundHttpException("client not found");
        }
        $client = $serializer->deserialize(
            $request->getContent(),
            Clients::class,
            'json',
            ['object_to_populate' => $client]
        );

        $errors = $validator->validate($client);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $errorMessages[$error->getPropertyPath()] = $error->getMessage();
            }

            return $this->json($errorMessages, JsonResponse::HTTP_BAD_REQUEST);
        }

        $client->setOwner($owner);
        $clientsRepository->save($client);
        return $this->json(
            $client,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'loyalty:client:owner',
                    "alert:info",
                    "default:basic",
                ],
            ]
        );
    }

    #[Route('/api/owner/{ownerId}/client/{id}', methods: ["GET"])]
    public function getClient(
        ClientsRepository $clientsRepository,
        SyncDataService $syncDataService,
        int $id,
        int $ownerId,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $client = $clientsRepository->findOneBy(['id' => $id, 'owner' => $owner]);

        if (is_null($client)) {
            $this->json(["client not found"], JsonResponse::HTTP_NOT_FOUND);
        }

        return $this->json(
            $client,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'loyalty:client:owner',
                    "alert:info",
                    "default:basic",
                ],
            ]
        );
    }

    #[Route('/api/owner/{ownerId}/client/{id}/report', methods: ["GET"])]
    public function getReport(
        ClientsRepository $clientsRepository,
        SyncDataService $syncDataService,
        ReportService $reportService,
        Request $request,
        int $id,
        int $ownerId,
    ): BinaryFileResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $client = $clientsRepository->findOneBy(['id' => $id, 'owner' => $owner]);

        if (is_null($client)) {
            $this->json(["client not found"], JsonResponse::HTTP_NOT_FOUND);
        }
        $ext = FileExtention::from($request->get('ext', 'pdf'));
        $criteria = [
            'dateFrom' => $request->get('dateFrom'),
            'dateTo' => $request->get('dateTo'),
            'clientId' => $client->getId(),
        ];

        $path = $reportService->getFile(
            LoyaltyClientUsageReport::class,
            $criteria,
            $client->getOwner(),
            $ext
        );
        return $this->file($path, "report.$ext->value", ResponseHeaderBag::DISPOSITION_INLINE);
    }


    #[Route('/api/owner/{ownerId}/clients', methods: ["GET"])]
    public function getList(
        ClientsRepository $clientsRepository,
        SyncDataService $syncDataService,
        Request $request,
        int $ownerId,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $page = $request->query->get('page', 1);
        $itemsPerPage = $request->query->get('itemsPerPage', 50);
        $search = $request->query->get('search');
        $invoiceStrategy = $request->query->get('strategy');
        $report = $request->query->get('report');
        $clients = $clientsRepository->getList($owner, $page, $itemsPerPage, $search, $invoiceStrategy, $report);

        return $this->json(
            $clients,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'loyalty:client:owner',
                    "alert:info",
                    "default:basic"
                ],
            ]
        );
    }
}
