<?php

namespace App\Entity;

use App\Entity\Carwash\CarwashConnectionStatus;
use DateTime;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Doctrine\Common\Collections\Criteria;
use Symfony\Component\Serializer\Annotation\SerializedName;

#[ORM\Table(name: 'carwash')]
#[ORM\Entity(repositoryClass: \App\Repository\CarwashRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Carwash
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'NONE')]
    #[ORM\Column(name: 'id', type: 'integer')]
    #[Groups(['default:basic'])]
    private int $id;

    #[ORM\Column(name: 'name', type: 'string', length: 256, nullable: true)]
    #[Groups(['default:basic'])]
    private ?string $name;

    #[ORM\ManyToOne(targetEntity: Owners::class)]
    #[Groups(['carwash:list'])]
    private ?Owners $owner = null;

    #[ORM\Column(name: 'ctime', type: 'datetime', options: ['default' => 'CURRENT_TIMESTAMP'])]
    private DateTimeInterface $ctime;

    #[ORM\Column(name: 'mtime', type: 'datetime', nullable: true, columnDefinition: 'DATETIME on update CURRENT_TIMESTAMP')]
    private ?DateTimeInterface $mtime = null;

    #[ORM\Column(type: 'integer', nullable: false, unique: true)]
    #[Groups(['default:basic'])]
    private int $sn;

    #[ORM\Column(nullable: true)]
    #[Groups(['carwash:list'])]
    private ?float $lat = null;
    #[ORM\Column(nullable: true)]
    #[Groups(['carwash:list'])]
    private ?float $lon = null;
    #[ORM\Column(nullable: true, type: "text")]
    #[Groups(['carwash:list'])]
    private ?string $description = null;

    #[ORM\OneToMany(targetEntity: CarwashConnectionStatus::class, mappedBy: 'carwash')]
    private $connectionStatus;

    #[Groups(['carwash:list'])]
    private array $stands = [];

    #[Groups(['carwash:list'])]
    private ?array $cwApi;

    #[Groups(['carwash:list'])]
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $address;

    #[ORM\Column(type: 'boolean')]
    #[Groups(['carwash:list'])]
    private bool $showOnMap = false;

    public function __construct()
    {
        $this->connectionStatus = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): Carwash
    {
        $this->id = $id;
        return $this;
    }



    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getOwner(): ?Owners
    {
        return $this->owner;
    }

    public function setOwner(?Owners $owner): self
    {
        $this->owner = $owner;

        return $this;
    }
    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps(): void
    {
        if (!isset($this->ctime)) {
            $this->ctime  = new \DateTime('now');
        }
        $this->mtime = new \DateTime('now');
    }


    public function getSn(): int
    {
        return $this->sn;
    }

    public function setSn(int $sn): self
    {
        $this->sn = $sn;

        return $this;
    }

    public function getCtime(): ?\DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(\DateTimeInterface $ctime): static
    {
        $this->ctime = $ctime;

        return $this;
    }

    public function getMtime(): ?\DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(?\DateTimeInterface $mtime): static
    {
        $this->mtime = $mtime;

        return $this;
    }

    /**
     * @return Collection<int, CarwashConnectionStatus>
     */
    public function getConnectionStatus(): Collection
    {
        return $this->connectionStatus;
    }

    #[Groups(['carwash:list'])]
    public function getLongName()
    {
        return $this->getSn() . ' - ' . $this->getName();
    }

    public function getLat(): ?float
    {
        return $this->lat;
    }

    public function setLat(?float $lat): Carwash
    {
        $this->lat = $lat;
        return $this;
    }

    public function getLon(): ?float
    {
        return $this->lon;
    }

    public function setLon(?float $lon): Carwash
    {
        $this->lon = $lon;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): Carwash
    {
        $this->description = $description;
        return $this;
    }

    public function getStands(): array
    {
        return $this->stands;
    }

    public function addStand($stand): Carwash
    {
        $this->stands[] = $stand;
        return $this;
    }

    public function getCwApi(): ?array
    {
        return $this->cwApi;
    }

    public function setCwApi(?array $cwApi): Carwash
    {
        $this->cwApi = $cwApi;
        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getShowOnMap(): bool
    {
        return $this->showOnMap;
    }

    public function setShowOnMap(bool $showOnMap): self
    {
        $this->showOnMap = $showOnMap;

        return $this;
    }
}
