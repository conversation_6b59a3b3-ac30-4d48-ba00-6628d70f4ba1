<?php

namespace App\Command;

use App\Entity\Carwash;
use App\Entity\Carwash\EdgeDevice;
use App\Entity\Owners;
use App\Repository\Carwash\CarwashLogRepository;
use App\Repository\Carwash\EdgeDeviceRepository;
use App\Repository\CarwashRepository;
use App\Repository\Loyalty\TransactionsRepository;
use App\Repository\OwnerRepository;
use App\Service\SyncData\SyncDataService;
use Bkf\Connector\Service\BkfApi\Repository\DeviceRepository;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use I2m\Connectors\Service\CarwashApi\CarwashApiCarwashService;
use I2m\Connectors\Service\CMApi\CMApi;
use I2m\StandardTypes\Enum\CMSubscription;
use I2m\StandardTypes\Enum\EdgeDeviceType;
use I2m\StandardTypes\Enum\TimeZoneEnum;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\HttpClient\Exception\ClientException;
use Symfony\Component\Stopwatch\Stopwatch;

#[AsCommand(
    name: 'i2m:sync:carwash',
)]
class I2mSyncCarwashCommand extends Command
{
    use LockableTrait;

    public function __construct(
        private LoggerInterface $logger,
        private CarwashRepository $carwashRepository,
        private CarwashApiCarwashService $carwashApiCarwashService,
        private DeviceRepository $bkfDeviceRepository,
        private TransactionsRepository $loyaltyTransRepository,
        private EntityManagerInterface $em,
        private CarwashLogRepository $carwashLogRepository,
        private EdgeDeviceRepository $edgeDeviceRepository,
        private OwnerRepository $ownerRepository,
        private CMApi $CMApi,
        private SyncDataService $syncDataService,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $date = date_format(new DateTime(), 'Y-m-d H:i:s');
        if (!$this->lock($this->getName() .  "-" . getenv('APP_ENV'))) {
            $this->logger->warning('Trwa juz inna komenda: ' . $this->getName());
            return Command::SUCCESS;
        }

        $stopwatch = new Stopwatch();
        $stopwatch->start('BkfCarwashAnalyze');

        $io = new SymfonyStyle($input, $output);


        /**
         * głowna logika,.
         */

        $packageCounter = 0;
        $packageCounter += $this->syncOwners();
        $packageCounter += $this->syncCarwashesCwApi();
        $packageCounter += $this->syncBkfApi();



        /**
         * podsumowanie.
         */
        $event = $stopwatch->stop('BkfCarwashAnalyze');
        $io->success('Ilosc odebranych pakietów ' . $packageCounter . " \n" . $event);
        $this->logger->notice(self::getName() . " - id: $date: " .
                " - Zakonczylem, Ilosc odebranych pakietów "
                . $packageCounter . " " . $event);

        $this->release();
        return Command::SUCCESS;
    }

    protected function syncOwners(): int
    {
        $page = 1;
        $limit = 100;
        $packageCounter = 0;

        do {
            $subscribers = $this->CMApi->getSubscribers($page, $limit)->data;

            if (!count($subscribers)) {
                return $packageCounter;
            }

            foreach ($subscribers as $subscriber) {
                $packageCounter++;
                $this->logger->notice(self::getName() .
                    " Subscriber {$subscriber->getName()} id: {$subscriber->getOwnerBkf()}");
                $owner = $this->syncDataService->getOwner($subscriber->getOwnerBkf());
                if (is_null($owner)) {
                    continue;
                }

                $owner
                    ->setSubscription($subscriber->getSubscription())
                    ->setName($subscriber->getName())
                    ->setCmId($subscriber->getId())

                ;
                $currency = $this->syncDataService->getCurrency($subscriber->getCurrency());
                if ($currency) {
                    $owner->setCurrency($currency);
                }

                $owner->getConfig()
                    ->setName($subscriber->getName())
                    ->setTaxNumber($subscriber->getTaxNumber())
                    ->setRegon($subscriber->getRegon())
                    ->setAddress($subscriber->getAddress())
                    ->setCity($subscriber->getCity())
                    ->setPostCode($subscriber->getPostCode())
                    ->setCountry($subscriber->getCountry())
                    ->setTimezone(TimeZoneEnum::from($subscriber->getTimezone()->getLocation()))
                ;

                $this->ownerRepository->save($owner);
            }
            $page++;
        } while (1);
    }

    protected function syncCarwashesCwApi(): int
    {
        $page = 1;
        $limit = 100;
        $packageCounter = 0;

        do {
            $bkfApiCarwashes = $this->carwashApiCarwashService->getList([], $page, $limit)->getData();

            if (!count($bkfApiCarwashes)) {
                return $packageCounter;
            }

            $this->logger->notice(self::getName() . " - pobranych urządzeń: " . count($bkfApiCarwashes) . "/$page");
            foreach ($bkfApiCarwashes as $bkfDevice) {
                $packageCounter++;
                $this->logger->notice(
                    self::getName() .
                    " Urządzenie sn {$bkfDevice->getSn()} plc: {$bkfDevice->getPlcSn()}" .
                    " owner: {$bkfDevice->getOwner()?->getId()}"
                );
                $this->updateDevice($bkfDevice);
                $this->em->flush();
                $this->em->clear();
            }
            $page++;
        } while (1);
    }
    protected function updateDevice(\I2m\Connectors\Model\CarwashApi\Carwash $device): ?Carwash
    {
        if (is_null($device->getOwner())) {
            return null;
        }
        $owner = $this->ownerRepository->find($device->getOwner()->getId());
        $carwash = $this->carwashRepository->findOneBySn($device->getSn());
        if (is_null($carwash) && is_null($owner)) {
            return null;
        }
        $carwash = $this->updateCarwash($device);
        $this->changeCarwashOwner($carwash, $owner);

        $edgeDevice = $this->updateEdgeDevice($device);
        $edgeDevice?->setCarwash($carwash);

        $this->em->flush();


        return $carwash;
    }

    protected function changeCarwashOwner(Carwash $carwash, ?Owners $owner_new): void
    {
        $owner_old = $carwash->getOwner();
        // jesli wlasiciele zgodni to nic nie robie
        if ($owner_old === $owner_new) {
            return;
        }


        if (!$owner_old) {
            $this->notify(
                $carwash,
                "Zmieniono właściciela dla myjni {$carwash->getSn()}" .
                " z " . $this->getOwnerString($owner_old) .
                " na " . $this->getOwnerString($owner_new)
            );
            $carwash->setOwner($owner_new);
            return;
        }

        // sprawdzam czy na tej myjni wykorzystywane są karty lojalnościowe
        $cardNum = $this->loyaltyTransRepository->count(['carwash' => $carwash]);

        if ($cardNum > 0) {
            if (
                ($owner_old->getSubscription() !== CMSubscription::FREE) ||
                ($owner_new?->getSubscription() !== CMSubscription::FREE)
            ) {
                $this->notify(
                    $carwash,
                    "Nie udało się zmienić właściciela myjni {$carwash->getSn()}" .
                    " z " . $this->getOwnerString($owner_old) .
                    " na " . $this->getOwnerString($owner_new) .
                    " ponieważ jest $cardNum transakcji lojalnościowych na tej myjni",
                );
                return;
            } else {
                $this->notify(
                    $carwash,
                    "Zmieniono właściciela dla myjni {$carwash->getSn()}" .
                    " z " . $this->getOwnerString($owner_old) .
                    " na " . $this->getOwnerString($owner_new) .
                    " jest $cardNum transakcji lojalnościowych na tej myjni ale właściciele nie mają subskrypcji CM",
                );
                $carwash->setOwner($owner_new);
                return;
            }
        }

        $this->notify(
            $carwash,
            "Zmieniono właściciela dla myjni {$carwash->getSn()}" .
            " z " . $this->getOwnerString($owner_old) .
            " na " . $this->getOwnerString($owner_new) .
            " na tej myjni nie ma transakcji lojalnościowych",
        );
        $carwash->setOwner($owner_new);
    }

    protected function notify(?Carwash $carwash, string $message, bool $sentry = true): void
    {
        $this->carwashLogRepository->notice($carwash, "SYNC-BKFAPI", null, $message, $sentry);
    }

    protected function getOwnerString(?Owners $owner_old): string
    {
        return !is_null($owner_old) ?
            "{$owner_old->getName()} ({$owner_old->getId()}) : {$owner_old->getSubscription()->value} "
            : "brak właściciela ";
    }

    private function updateCarwash(\I2m\Connectors\Model\CarwashApi\Carwash $device): Carwash
    {
        $carwash = $this->carwashRepository->findOneBy(['sn' => $device->getSn()]);
        $newName = $device->getName();

        if (!$carwash) {
            $carwash = new Carwash();
            $carwash
                ->setId($device->getId())
                ->setSn($device->getSn());
            $this->em->persist($carwash);
        }

        $carwash->setName($newName);


        $this->carwashRepository->save($carwash);


        return $carwash;
    }

    private function updateEdgeDevice(\I2m\Connectors\Model\CarwashApi\Carwash $device): ?EdgeDevice
    {
        $plcSn = $device->getPlcSn();
        if (is_null($plcSn)) {
            return null;
        }

        $edgeDevice =
            $this->edgeDeviceRepository->create(EdgeDeviceType::PLC, $plcSn);

        $edgeDevice
                ->setMac($device->getPlcMac());
        $this->edgeDeviceRepository->save($edgeDevice);

        return $edgeDevice;
    }

    protected function syncBkfApi(): int
    {
        $page = 1;
        $limit = 100;
        $packageCounter = 0;
        do {
            try {
                $bkfApiCarwashes = $this->bkfDeviceRepository->getDevicesByGroups(
                    [
                        "Myjnia",
                        "Portal",
                        "Dezynfekcja",
                        "Gamm-bud Brain"
                    ],
                    $page,
                    $limit
                );
            } catch (ClientException $e) {
                if ($e->getCode() == 404) {
                    return $packageCounter;
                }

                throw ($e);
            }

            $this->logger->notice(self::$defaultName . " - pobranych urządzeń: " . count($bkfApiCarwashes) . "/$page");
            foreach ($bkfApiCarwashes as $bkfDevice) {
                $packageCounter++;
                $carwash = $this->carwashRepository->findOneBy(['sn' => $bkfDevice->getSerialNumber()]);
                if (is_null($carwash)) {
                    continue;
                }
                $this->logger->notice(
                    self::getName() .
                    " Urządzenie sn {$bkfDevice->getSerialNumber()} plc: {$bkfDevice->getPlc()}" .
                    " owner: {$bkfDevice->getNetwork()?->getId()}" .
                    " position: {$bkfDevice->getLatitude()},{$bkfDevice->getLongitude()}"
                );

                $carwash
                    ->setLat($bkfDevice->getLatitude())
                    ->setLon($bkfDevice->getLongitude())
                    ->setAddress($bkfDevice->getAddress())
                ;
                $this->carwashRepository->save($carwash);
            }
            $page++;
        } while (1);
    }
}
