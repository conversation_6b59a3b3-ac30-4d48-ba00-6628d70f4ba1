<?php

namespace App\Service\Loyalty;

use App\Entity\Loyalty\Enum\TransactionType;
use App\Entity\Loyalty\TopUp;
use App\Entity\Loyalty\Transactions;
use App\Repository\Loyalty\TransactionsRepository;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsEntityListener;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Events;

#[AsEntityListener(event: Events::preFlush, method: 'handle', entity: TopUp::class)]
class TopUpListener
{
    public function __construct(
        private EntityManagerInterface $em,
        private TransactionsRepository $transactionsRepository
    ) {
    }

    public function handle(TopUp $topUp)
    {


        if ($topUp->getCard()?->isVirtual() && $topUp->getValueInCredit()) {
            $card = $topUp->getCard();
            $new_balance = $this->transactionsRepository->calculateBalance($card) + $topUp->getValueInCredit();
            $transaction = new Transactions();
            $transaction->setCard($card);
            $transaction->setOwner($card->getOwner());
            $transaction->setCurrency($card->getCurrency());
            $transaction->setCarwash(null);
            $transaction->setType(TransactionType::from($topUp->getType()->value));
            $transaction->setSource($topUp->getSource());
            $transaction->setBalanceInCredit($new_balance);
            $transaction->setValueInCredit($topUp->getValueInCredit());
            $transaction->setTime(new \DateTime());

            $card->setBalance($new_balance);
            $this->em->persist($transaction);

            $topUp->setValueInCredit(0);
            $this->em->flush();
        }
    }
}
