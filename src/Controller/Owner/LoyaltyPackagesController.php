<?php

namespace App\Controller\Owner;

use App\Entity\ExternalPayment\Enum\PackageStatus;
use App\Entity\ExternalPayment\PaymentPackages;
use App\Repository\ExternalPayment\PaymentPackagesRepository;
use App\Service\SyncData\SyncDataService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

class LoyaltyPackagesController extends AbstractController
{
    #[Route('/api/owner/{ownerId}/packages', methods: ["GET"])]
    public function getList(
        PaymentPackagesRepository $packagesRepository,
        SyncDataService $syncDataService,
        Request $request,
        int $ownerId,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $page = $request->query->get('page', 1);
        $itemsPerPage = $request->query->get('itemsPerPage', 50);
        $showTotal = $request->query->get('showTotal', true);
        $packages = $packagesRepository->getList($owner, $page, $itemsPerPage, $showTotal);

        return $this->json(
            $packages,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    "default:basic"
                ],
            ]
        );
    }

    #[Route('/api/owner/{ownerId}/package/{id}', methods: ["PUT"])]
    public function updatePackage(
        int $id,
        int $ownerId,
        SerializerInterface $serializer,
        Request $request,
        PaymentPackagesRepository $packagesRepository,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $package = $packagesRepository->findOneBy(['id' => $id, 'owner' => $owner]);

        if (is_null($package)) {
            $this->json(["package not found"], JsonResponse::HTTP_NOT_FOUND);
        }

        $package = $serializer->deserialize(
            $request->getContent(),
            PaymentPackages::class,
            'json',
            ['object_to_populate' => $package]
        );

        $package->setOwner($owner);
        $packagesRepository->save($package);

        return $this->json(
            $package,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'default:basic',
                ],
            ]
        );
    }

    #[Route('/api/owner/{ownerId}/package/{id}', methods: ["GET"])]
    public function getPackage(
        SyncDataService $syncDataService,
        PaymentPackagesRepository $packagesRepository,
        int $id,
        int $ownerId,
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $package = $packagesRepository->findOneBy(['id' => $id, 'owner' => $owner]);

        if (is_null($package)) {
            $this->json(["package not found"], JsonResponse::HTTP_NOT_FOUND);
        }

        return $this->json(
            $package,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    "default:basic",
                ],
            ]
        );
    }

    #[Route('/api/owner/{ownerId}/package/{id}', methods: ["DELETE"])]
    public function deletePackage(
        int $id,
        int $ownerId,
        PaymentPackagesRepository $packagesRepository,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }
        $package = $packagesRepository->findOneBy(['id' => $id, 'owner' => $owner]);

        if (is_null($package)) {
            $this->json(["package not found"], JsonResponse::HTTP_NOT_FOUND);
        }

        $package->setStatus(PackageStatus::DELETED);
        $packagesRepository->save($package);

        return $this->json(
            $package,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'default:basic',
                ],
            ]
        );
    }


    #[Route('/api/owner/{ownerId}/package', methods: ["POST"])]
    public function addPackage(
        int $ownerId,
        SerializerInterface $serializer,
        Request $request,
        PaymentPackagesRepository $packagesRepository,
        SyncDataService $syncDataService
    ): JsonResponse {
        $owner = $syncDataService->getOwner($ownerId);
        if (is_null($owner)) {
            throw new NotFoundHttpException("owner not found");
        }

        $package = $serializer->deserialize(
            $request->getContent(),
            PaymentPackages::class,
            'json',
        );

        $package->setOwner($owner);
        $packagesRepository->save($package);

        return $this->json(
            $package,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'default:basic',
                ],
            ]
        );
    }
}
