<?php

namespace App\Service\ExternalPayment;

use App\Entity\ExternalPayment\ExternalPayment;
use App\Entity\Loyalty\TopUp;
use App\Entity\Owners;
use App\Entity\User;
use App\Repository\ExternalPayment\ExternalPaymentRepository;
use App\Repository\ExternalPayment\PaymentGateRepository;
use App\Service\Loyalty\CardsManager;
use I2m\Payment\Enum\Action;
use I2m\Payment\Enum\Status;
use I2m\Payment\Service\PaymentGateFactory;
use Symfony\Component\HttpFoundation\Request;

use function Sentry\captureMessage;

class ExtPaymentGateService
{
    public function __construct(
        private readonly ExternalPaymentRepository $externalPaymentRepository,
        private PaymentGateRepository $paymentGateRepository,
        private readonly PaymentGateFactory $paymentGateFactory,
        private CardsManager $cardsManager,
    ) {
    }

    public function init(
        Owners $owner,
        User $user,
        float $value
    ): ExternalPayment {

        $gate = $this->paymentGateRepository->findOneBy(['owner' => $owner]);

        $externalPayment = (new ExternalPayment())
            ->setOwner($owner)
            ->setStatus(Status::INITIATED)
            ->setInitiatedTimestamp(new \DateTime())
            ->setUser($user)
            ->setValue($value)
            ->setGate($gate);
        $this->externalPaymentRepository->save($externalPayment);

        try {
            $this->paymentGateFactory->initPayment($externalPayment);
        } catch (\Exception $e) {
            $this->externalPaymentRepository->save($externalPayment);
            throw $e;
        }

        $this->externalPaymentRepository->save($externalPayment);

        return $externalPayment;
    }

    public function handleStatus(ExternalPayment $ep, Request $request): ExternalPayment
    {
        $callback = $this->paymentGateFactory->callback($ep, $request);
        if ($callback->getAction() === Action::PAYMENT_CONFIRMED) {
            $this->cardsManager->confirmTopUps($ep);
        }

        $this->externalPaymentRepository->save($ep);
        return $ep;
    }
}
