<?php

namespace App\Service\Loyalty;

use App\Entity\Carwash;
use App\Entity\Currency;
use App\Entity\Loyalty\Cards;
use App\Model\Loyalty\AskRevalue;
use App\Repository\Carwash\CarwashLogRepository;
use App\Repository\CurrencyRepository;

class Hrk2EuroService
{
    # wszystkie myjnie z fiskalizajcą
    # +  35276 +35264 karty lojalnościowe.

    private const HRK_EURO_OWNERS = [
        36122,//AUTOSPA pakšec CM: <EMAIL>,5
        3716, //KROMA CM: <EMAIL>,5
        36376,//Vincetić CM: <EMAIL>,13
        35920,//MediaBox, FUIS, KROMA, CW 4 T 4 CM: <EMAIL>,13
        31808,//Crnekovic CM: <EMAIL>,13
        35972,//PREGRA<PERSON>, KROMA, CW 4 T 4 CM: <EMAIL>,
        36730,//REMSS CM: <EMAIL>,13
        35369,//<EMAIL> CM: <EMAIL>,
        43190,//RICARDO D.O.O. CM: <EMAIL>,13
        42796,//IVAN BRAJKO CM: <EMAIL>,13
        43191,//TEO D.O.O. CM: <EMAIL>,
        36814,// konto M.WOJTAS (testy 150)
        34710,// <EMAIL>
    ];


    public function __construct(
        private CarwashLogRepository $carwashLogRepository,
        private CurrencyRepository $currencyRepository
    ) {
    }

    /**
     * funkcja zwraca wartość kredytu do doładowania karty lub null gdy kredyt powinien być wysłany standardowo
     */
    public function getRevalue(?AskRevalue $askRevalue, Cards $card, Carwash $carwash): ?float
    {
        // myjnia nie nadaje danych niezbędnych do konwersji
        if (is_null($askRevalue)) {
            return null;
        }

        if (!isset($askRevalue->currency)) {
            return null;
        }

        $actualCurrency = $this->currencyRepository->getCurrency($askRevalue->currency);

        if (
            !$this->shouldBeConverted($card->getOwner()->getId())
        ) {
            // myjnie nie podlega konwesji juz nic nie robimy
            $this->changeCurrency($carwash, $card, $actualCurrency);
            return null;
        }

        if ($card->getOwner()->getCurrency()->getId() != $actualCurrency->getId()) {
            $this->carwashLogRepository->notice(
                $carwash,
                "LOYALTY",
                $card->getNumber(),
                "na myjni błędnie ustawiona waluta, powinno być EUR a jest {$askRevalue->currency}",
                true
            );
        }

        if (($askRevalue->credit < 0.01)) { // 0.01 to najmniejsza wartość kredytu
            // jesli nie ma kredytu to możemy śmiało przepinac walutę
            $this->changeCurrency($carwash, $card, $actualCurrency);
            return null;
        }

        // karta przeznaczona do konwersji
        if ($card->getCurrency()?->getCode() == 'HRK') {
            $credit = $askRevalue->credit;
            $correction = round($this->hrk2Euro($credit) / 0.5 - $askRevalue->credit, 2);

            $this->carwashLogRepository->notice(
                $carwash,
                "LOYALTY",
                $card->getNumber(),
                "rekalkulacja kredytu: {$askRevalue->credit} o $correction",
                false
            );
            return $correction;
        }

        // karta jest juz przekonwertowana
        // myjnia i karta działają jeszcze na HRK
        return null;
    }
    public function ackConvert(Cards $card, Carwash $carwash): void
    {
        $currency = $this->currencyRepository->getCurrency("EUR");
        $this->changeCurrency($carwash, $card, $currency);
        $card->setAdditInfo(null);
    }
    public function changeCurrency(Carwash $carwash, Cards $card, ?Currency $currency): void
    {
        // nie zmianiemy waluty jesli nie jest poprawnie wybrana
        if (
            is_null($currency) || // brak waluty
            $currency->getId() == 0 // kredyty
        ) {
            return;
        }

        if ($currency->getCode() !== $card->getCurrency()?->getCode()) {
            $this->carwashLogRepository->notice(
                $carwash,
                "LOYALTY",
                $card->getNumber(),
                "zmiana waluty karty z : {$card->getCurrency()?->getCode()->value}" .
                " na {$currency->getCode()->value}"
            );
            $card->setCurrency($currency);
        }
    }

    public function shouldBeConverted(?int $owner_id): bool
    {
        return in_array($owner_id, self::HRK_EURO_OWNERS);
    }

    public static function hrk2Euro(float $credit): float
    {
        return round($credit / 7.5345, 2);
    }
}
