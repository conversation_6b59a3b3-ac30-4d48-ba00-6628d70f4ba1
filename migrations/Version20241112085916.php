<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241112085916 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE owner_config CHANGE language language VARCHAR(8) NOT NULL');
        $this->addSql('ALTER TABLE user CHANGE language language VARCHAR(8) NOT NULL');
    }

    public function down(Schema $schema): void
    {
    }
}
