<?php

namespace App\Command;

use App\Entity\Enum\Languages;
use App\Repository\Loyalty\ClientsRepository;
use App\Service\Report\Data\LoyaltyClientUsageReport;
use App\Service\Report\ReportService;
use App\Service\QrPrintGenerator\StickerService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpKernel\KernelInterface;

#[AsCommand(
    name: 'sticker:generate',
    description: 'Add a short description for your command',
)]
class StickerGenerateCommand extends Command
{
    public function __construct(
        private StickerService $stickerService,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                name: 'start',
                mode: InputOption::VALUE_REQUIRED,
                description: 'Starting number of the series'
            )
            ->addOption(
                name: 'length',
                mode: InputOption::VALUE_OPTIONAL,
                description: 'Length of the series',
                default: 100
            )
            ->addOption(
                name: 'lang',
                mode: InputOption::VALUE_OPTIONAL,
                default: 'pl'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $start = $input->getOption('start');
        $length = $input->getOption('length');
        $lang = $input->getOption('lang');

        $end = $start + $length - 1;
        $exportPath = "stickers/{$start}_{$end}";
        $this->stickerService->genrateSeries(
            $start,
            $end,
            Languages::from($lang),
            $exportPath
        );

        $this->stickerService->zipDirectory($exportPath, $exportPath . '.zip');

        $io->success('Success, naklejki skopiuj do https://drive.google.com/drive/folders/1OQO-8wZLVUcORLMj2I-yzXbK-JQzExre');

        return Command::SUCCESS;
    }
}
