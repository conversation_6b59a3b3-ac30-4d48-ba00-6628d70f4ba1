<?php

namespace App\Command;

use App\Entity\Loyalty\Cards;
use App\Entity\Loyalty\Enum\TopUpStatus;
use App\Entity\Owners;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\CyclicTopUpRepository;
use App\Repository\Loyalty\TopUpRepository;
use App\Service\Loyalty\CardsManager;
use App\Entity\Loyalty\CyclicTopUp;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use DateTimeInterface;

use function Sentry\captureMessage;

#[AsCommand(
    name: 'loyalty:create:cyclic-top-up',
    description: 'Add a short description for your command',
)]
class CyclicTopUpCommand extends Command
{
    public function __construct(
        private CyclicTopUpRepository $cyclicTopUpRepository,
        private TopUpRepository $topUpRepository,
        private CardsRepository $cardsRepository,
        private CardsManager $cardsManager,
        private EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('arg1', InputArgument::OPTIONAL, 'Argument description')
            ->addOption('option1', null, InputOption::VALUE_NONE, 'Option description')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $arg1 = $input->getArgument('arg1');

        if ($arg1) {
            $io->note(sprintf('You passed an argument: %s', $arg1));
        }

        $periodEnd = (new \DateTime('last day of previous month'))->setTime(23, 59, 59);

        $page = 1;
        do {
            $list = $this->cyclicTopUpRepository->getList(
                page: $page,
                perPage: 20,
                isActive: true,
            );

            $cyclicTopUps = $list['data'];

            foreach ($cyclicTopUps as $cyclicTopUp) {
                /** @var CyclicTopUp $cyclicTopUp */

                $card = $cyclicTopUp->getCard();
                $owner = $card?->getOwner();
                if (is_null($owner)) {
                    continue;
                }

                if ($owner->getSubscription()->value != 'premium') {
                    continue;
                }

                $topUpsToSend = $this->topUpRepository->getTotalTopUpCredits($card)
                    * $card->getCurrency()?->getRate();

                $io->writeln("\t owner: {$owner->getId()} id: {$cyclicTopUp->getId()}" .
                    " card: {$card->getNumber()}" .
                    " balance {$card->getBalance()} {$card->getCurrency()->getCode()->value} + $topUpsToSend (to send)" .
                    " ctp: {$cyclicTopUp->getType()} {$cyclicTopUp->getValue()} {$cyclicTopUp->getCurrency()->getCode()->value}");

                // sprawdzczy należy zrobić dołądowanie
                if ($this->checkIfCyclicTopUpReady($cyclicTopUp, $periodEnd, $output) != 1) {
                    continue;
                }

                $this->makeTopUp($owner, $cyclicTopUp, $periodEnd, $output);
            }
            $page++;
        } while (!empty($list['data']));

        $io->success('Success');

        return Command::SUCCESS;
    }

    private function checkIfCyclicTopUpReady(
        CyclicTopUp $cyclicTopUp,
        DateTimeInterface $periodEnd,
        OutputInterface $output
    ): int {

        $periodMonth = $periodEnd->format('Y-m');
        if (!$cyclicTopUp->isActive()) {
            // cykliczne dołdowanie nie jest aktywne
            $output->writeln("\t\tcykliczne dołdowanie nie jest aktywne");
            return 0;
        }

        if ($cyclicTopUp->getStartTime() > $periodEnd) {
            // cykliczne doładowanie ma rozpocząć się później niż okres dla którego robimy doładowanie
            $output->writeln("\t\tcykliczne doładowanie ma rozpocząć się później niż okres dla którego robimy doładowanie");
            return 0;
        }

        if ($cyclicTopUp->getEndTime() < $periodEnd) {
            // cykliczne doładowanie utraciło już swoją ważność
            $output->writeln("\t\tcykliczne doładowanie utraciło już swoją ważność");
            return 0;
        }

        if (
            !is_null($cyclicTopUp->getLastPeriod()) &&
            $cyclicTopUp->getLastPeriod()->format('Y-m') == $periodMonth
        ) {
                    // czykliczne doładowanie została już zrealizowane wcześniej dla tego okresu
            $output->writeln("\t\tczykliczne doładowanie została już zrealizowane wcześniej dla tego okresu");
                    return 0;
        }
                return 1;
    }

    public function makeTopUp(
        Owners $owner,
        CyclicTopUp $cyclicTopUp,
        DateTimeInterface $topUpDate,
        OutputInterface $output
    ): ?CyclicTopUp {
        $type = $cyclicTopUp->getType();
        $cardToken = $cyclicTopUp->getCard()->getToken();
        /** @var Cards $actualCard */
        $actualCard = $this->cardsRepository->findOneBy(
            [
                'token' => $cardToken,
            ]
        );

        $cyclicTopUpUpdate = (new CyclicTopUp())->setLastCall(new \DateTime());
        $cyclicTopUp->setLastCall(new \DateTime());
        $this->cyclicTopUpRepository->save($cyclicTopUp);

        switch ($type) {
            case 'ADD':
                $toTopUpValue = $cyclicTopUp->getValue();
                break;
            case 'ALIGN':
                $this->entityManager->refresh($actualCard);
                $topUpsToSend = $this->topUpRepository->getTotalTopUpCredits($actualCard)
                    * $actualCard->getCurrency()?->getRate();

                $toTopUpValue = $cyclicTopUp->getValue() - $actualCard->getBalance() - $topUpsToSend;

                if ($toTopUpValue < 0.01) { // czasami float pokazuje bardzo małe wartości.
                    $output->writeln("\t\tbalance karty jest wystarczający, różnica {$toTopUpValue}");
                    $cyclicTopUpUpdate->setLastPeriod($topUpDate);

                    $cyclicTopUp->setLastCall(new \DateTime());
                    $this->cyclicTopUpRepository->save($cyclicTopUp);

                    return $cyclicTopUp;
                }
                break;
            default:
                captureMessage("nieznany typ doładowania cyklicznego $type");
                return $cyclicTopUp;
        }

        $output->writeln("\t\tdoładowanie na kwotę $toTopUpValue -{$cyclicTopUp->getDiscount()} %");

        $this->cardsManager->addTopUpWithBonus(
            card: $actualCard,
            toTopUpValue: $toTopUpValue,
            status: TopUpStatus::ACTIVE,
            addedBy: "cyclic top up",
            discount: $cyclicTopUp->getDiscount(),
            cyclicTopUp: $cyclicTopUp
        );
//        $this->cardsManager->addTopUp(
//            value: $topUpCredits,
//            type: TopUpType::ADDITION,
//            source: Source::INTERNET,
//            owner: $owner,
//            card: $actualCard,
//            addedBy: "cyclic top up",
//            cyclicTopUp: $cyclicTopUp,
//        );
//
//        if ($bonusCredits !== 0) {
//            $this->cardsManager->addTopUp(
//                value: $bonusCredits,
//                type: TopUpType::PROMOTION,
//                source: Source::INTERNET,
//                owner: $owner,
//                card: $actualCard,
//                addedBy: "cyclic top up",
//                cyclicTopUp: $cyclicTopUp,
//            );
//        }

        $cyclicTopUp->setLastPeriod($topUpDate);
        $this->cyclicTopUpRepository->save($cyclicTopUp);

        return $cyclicTopUp;
    }
}
