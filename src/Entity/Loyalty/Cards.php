<?php

namespace App\Entity\Loyalty;

use App\Entity\Currency;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Loyalty\Enum\CardType;
use App\Entity\Owners;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

use function Sentry\captureMessage;

#[ORM\UniqueConstraint(name: 'cards_number_key', columns: ['number', 'owner_id'])]
#[ORM\Entity(repositoryClass: \App\Repository\Loyalty\CardsRepository::class)]
#[ORM\HasLifecycleCallbacks]
//#[ORM\EntityListeners(['App\Service\Loyalty\CardsListener'])]
class Cards
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['default:basic', "loyalty:cards:user",'loyalty:cards:list',])]
    private int $id;

    #[Groups(['loyalty:cards:list'])]
    private ?array $stats = [];

    #[ORM\Column(name: 'number', type: 'string', length: 32, nullable: true)]
    #[Groups(['default:basic', "loyalty:cards:user",'loyalty:cards:list'])]
    private ?string $number;

    #[ORM\Column(name: 'alias', type: 'string', length: 128, nullable: true)]
    #[Groups(['default:basic', 'loyalty:cards:list', 'loyalty:cards:write'])]
    private ?string $alias = null;

    #[ORM\Column(name: 'balance', type: 'decimal', precision: 10, scale: 2, nullable: true)]
    private $balance = null;

    #[ORM\Column(nullable: false, enumType: CardStatus::class)]
    #[Groups(['default:basic', "loyalty:cards:user",'loyalty:cards:list', 'loyalty:cards:write'])]
    private CardStatus $status = CardStatus::ACTIVE;

    #[ORM\Column(nullable: false, enumType: CardType::class)]
    #[Groups(['default:basic', "loyalty:cards:user",'loyalty:cards:list'])]
    private CardType $type = CardType::MIFARE;

    #[ORM\Column(name: 'ctime', type: 'datetime', options: ['default' => 'CURRENT_TIMESTAMP'])]
    #[Groups(["loyalty:cards:user",'loyalty:cards:list'])]
    private DateTimeInterface $ctime;

    #[ORM\Column(name: 'mtime', type: 'datetime', nullable: true, columnDefinition: 'DATETIME on update CURRENT_TIMESTAMP')]
    private DateTimeInterface $mtime;

    #[ORM\Column(name: 'last_contact', type: 'datetime', nullable: true)]
    #[Groups(['default:basic',"loyalty:cards:user", 'loyalty:cards:list'])]
    private ?DateTimeInterface $lastContact = null;

    #[ORM\ManyToOne(targetEntity: Clients::class)]
    #[Groups(['loyalty:cards:list',"loyalty:cards:user","loyalty:cards:client", 'loyalty:cards:write'])]
    private ?Clients $client = null;

    #[ORM\ManyToOne(targetEntity: Owners::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['loyalty:cards:list', "loyalty:cards:user", 'loyalty:cards:owner'])]
    private Owners $owner;

    #[ORM\ManyToOne(targetEntity: Currency::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['loyalty:cards:list', "loyalty:cards:user", 'default:basic'])]
    private ?Currency $currency = null;

    #[ORM\Column(name: 'additInfo', type: 'string', length: 128, nullable: true)]
    #[Groups(['default:basic', 'loyalty:cards:list'])]
    private ?string $additInfo = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(['default:basic', 'loyalty:cards:list', 'loyalty:cards:write'])]
    private ?string $email = null;

    #[ORM\OneToMany(targetEntity: CyclicTopUp::class, mappedBy: 'card')]
    private $cyclicTopUps;

    #[ORM\Column(type: 'string', length: 128, nullable: true)]
    private ?string $token = null;

    #[ORM\Column(type: 'integer', nullable: true)]
    private ?int $cvv = null;

    #[ORM\Column(nullable: true, type: "text")]
    #[Groups(['default:basic', 'loyalty:cards:list', 'loyalty:cards:write'])]
    private ?string $description = null;

    #[Groups(['loyalty:cards:list'])]
    private ?string $fullComment = null;

    public function __construct()
    {
        $this->cyclicTopUps = new ArrayCollection();
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * Card number (ex. C7B7516E)
     */
    public function getNumber(): ?string
    {
        return $this->number;
    }

    /**
     * @param string|null $number
     * @return Cards
     */
    public function setNumber(?string $number): self
    {
        $this->number = $number;

        return $this;
    }

    /**
     * Card alias (ex. My card)
     */
    public function getAlias(): ?string
    {
        return $this->alias;
    }

    /**
     * @param string|null $alias
     * @return Cards
     */
    public function setAlias(?string $alias): self
    {
        $this->alias = $alias;

        return $this;
    }

    #[Groups(['loyalty:cards:list', "loyalty:cards:user"])]
    public function getBalance(): ?float
    {
        return $this->balance * $this->getCurrency()?->getRate();
    }

    public function getBalanceInCredit(): ?float
    {
        return $this->balance;
    }


    public function decBalance(float $value): ?float
    {
        return $this->balance -= $value;
    }


    /**
     * @param float|null $balance
     * @return Cards
     */
    public function setBalance(?float $balance): self
    {
        $this->balance = $balance;

        return $this;
    }

    public function getStatus(): ?CardStatus
    {
        return $this->status;
    }

    public function setStatus(?CardStatus $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCtime(): ?DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(?DateTimeInterface $ctime): self
    {
        $this->ctime = $ctime;

        return $this;
    }

    public function getMtime(): ?DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(?DateTimeInterface $mtime): self
    {
        $this->mtime = $mtime;

        return $this;
    }

    public function getClient(): ?Clients
    {
        return $this->client;
    }

    public function setClient(?Clients $client): self
    {
        $this->client = $client;

        return $this;
    }

    /**
     * @return Owners|null
     */
    public function getOwner(): ?Owners
    {
        return $this->owner;
    }

    /**
     * @param Owners|null $owner
     * @return Cards
     */
    public function setOwner(?Owners $owner): self
    {
        $this->owner = $owner;

        return $this;
    }

    public function getLastContact(): ?DateTimeInterface
    {
        return $this->lastContact;
    }

    public function setLastContact(?DateTimeInterface $lastContact): void
    {
        $this->lastContact = $lastContact;
    }

    #[ORM\PrePersist]
    public function updatedTimestamps(): void
    {
        if (!isset($this->ctime)) {
            $this->setCtime(new \DateTime('now'));
        }

        if ($this->getToken() == null) {
            $this->generateCrypto();
        }

        if (empty($this->number)) {
            $this->number = $this->generateCardNumber();
            $this->setVirtual(true);
        }
    }

    public function generateCardNumber(): string
    {
        $time = time();

            $check = base_convert("" . crc32("" . $time) % 36, 10, 36);
            return
                'V'
                . strtoupper(str_pad(base_convert("" . $time, 10, 36), 6, '0', STR_PAD_LEFT)
                . $check);
    }

    public function getCurrency(): ?Currency
    {
        return $this->currency ?? $this->getOwner()->getCurrency();
    }


    public function setCurrency(?Currency $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getAdditInfo(): ?string
    {
        return $this->additInfo;
    }

    public function setAdditInfo(?string $additInfo): self
    {
        $this->additInfo = $additInfo;
        return $this;
    }

    public function isVirtual(): bool
    {
        return $this->type === CardType::VIRTUAL;
    }

    public function setVirtual(?bool $isVirtual): self
    {
        $this->type = $isVirtual ? CardType::VIRTUAL : CardType::MIFARE;
        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function verifyBalance($balance): bool
    {
        if ($balance != $this->balance) {
            captureMessage(
                "dla karty wirtualnej {$this->getNumber()} ({$this->getId()})" .
                "błędny balance: {$this->balance} != $balance"
            );
            $this->balance = $balance;
            return false;
        }
        return true;
    }

    public function modifyBalance($value): float
    {
        $this->balance += $value;
        return $this->balance;
    }

    /**
     * @return Collection<int, CyclicTopUp>
     */
    public function getCyclicTopUps(): Collection
    {
        return $this->cyclicTopUps;
    }

    public function getToken(): ?string
    {
        return $this->token;
    }
    public function getStats(): ?array
    {
        return $this->stats;
    }

    public function setToken(?string $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function getCvv(): ?int
    {
        return $this->cvv;
    }

    public function setCvv(?int $cvv): self
    {
        $this->cvv = $cvv;

        return $this;
    }

    public function generateCrypto()
    {
        if (!$this->token) {
            $this->token = bin2hex(random_bytes(8));
        }

        if (!$this->cvv) {
            $this->cvv = random_int(0, 999);
        }
    }

    #[Groups(['loyalty:cards:list','loyalty:cards:user'])]
    public function getCardToken(): ?string
    {
        return  implode('-', [
            $this->getOwner()->getId() ,
            $this->getNumber(),
            $this->token]);
    }

    public function setStats(?array $stats): Cards
    {
        $this->stats = $stats;
        return $this;
    }


    public function addCyclicTopUp(CyclicTopUp $cyclicTopUp): static
    {
        if (!$this->cyclicTopUps->contains($cyclicTopUp)) {
            $this->cyclicTopUps->add($cyclicTopUp);
            $cyclicTopUp->setCard($this);
        }

        return $this;
    }

    public function removeCyclicTopUp(CyclicTopUp $cyclicTopUp): static
    {
        if ($this->cyclicTopUps->removeElement($cyclicTopUp)) {
            // set the owning side to null (unless already changed)
            if ($cyclicTopUp->getCard() === $this) {
                $cyclicTopUp->setCard(null);
            }
        }

        return $this;
    }

    public function getType(): CardType
    {
        return $this->type;
    }

    #[Groups(["loyalty:cards:user"])]
    public function isRechargeable()
    {

        return $this->getOwner()?->isRechargeable() ?? false;
    }
    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getFullComment(): ?string
    {
        return $this->fullComment;
    }

    public function setFullComment(?string $fullComment): Cards
    {
        $this->fullComment = $fullComment;
        return $this;
    }
}
