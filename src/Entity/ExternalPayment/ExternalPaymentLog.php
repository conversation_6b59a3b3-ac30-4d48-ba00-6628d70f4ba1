<?php

namespace App\Entity\ExternalPayment;

use App\Repository\ExternalPayment\ExternalPaymentLogRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ExternalPaymentLogRepository::class)]
#[ORM\HasLifecycleCallbacks]
class ExternalPaymentLog
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(nullable: false)]
    private \DateTime $ctime;

    #[ORM\Column(nullable: true)]
    private ?array $addInfo = null;

    #[ORM\Column()]
    private string $comment;

    #[ORM\ManyToOne()]
    private ?ExternalPayment $externalPayment = null;

    public function getId()
    {
        return $this->id;
    }

    public function getCtime(): \DateTime
    {
        return $this->ctime;
    }
    public function setCtime(\DateTime $ctime): self
    {
        $this->ctime = $ctime;
        return $this;
    }

    public function getAddInfo(): ?array
    {
        return $this->addInfo;
    }

    public function setAddInfo(?array $addInfo): self
    {
        $this->addInfo = $addInfo;
        return $this;
    }

    public function getComment(): string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;
        return $this;
    }

    public function getExternalPayment(): ?ExternalPayment
    {
        return $this->externalPayment;
    }


    public function setExternalPayment(?ExternalPayment $externalPayment): self
    {
        $this->externalPayment = $externalPayment;
        return $this;
    }
}
