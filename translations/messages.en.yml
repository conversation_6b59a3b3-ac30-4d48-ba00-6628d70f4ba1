sign_up: Sign up
password: Password
repeat_password: Repeat password
passwords_mismatch: Provided passwords are not identical
common.try_our_app: Try our app
common.greetings: Hello!
common.regards: Best Regards, <br/>The I2M Team
common.success: Success!
common.error: Error!
registration.title: Confirm Registration
registration.thank_you_for_registering: 'Thank you for registering with <b>%appName%</b> app.<br/>Confirm your registration by clicking the button below:'
registration.your_account_registered: Your account has been registered.<br/>Log in using the credentials provided during registration.
registration.confirm_registration: CONFIRM REGISTRATION
registration.user_exists: User with email {email} already exists
registration.passwords_must_match: The password fields must be the same
registration.set_password: Set password for your account
registration.complete: Complete registration
registration.completed: Registration completed
registration.completed_message: 'Your account has been successfully created with the email:'
registration.download_app_log_in: Download our app and log in using your credentials.
registration.i_accept: I accept
registration.terms_of_use: terms of use
registration.privacy_policy: Privacy policy
registration.invalid_token: Invalid token
registration.invalid_token_message: Registration link is not valid anymore
password_reset.title: Password Reset
password_reset.password_reset_needed: 'Your password for the <b>%appName%</b> needs to be reset. Reset your password by clicking the button below:'
password_reset.reset_password: RESET PASSWORD
password_reset.new_password: New password
password_reset.confirm_password: Confirm password
password_reset.success_message: Password has been reset
password_reset.instructions_to_login: You can use new password to login
password_reset.passwords_must_match: Passwords must match
password_reset.invalid_characters: Password contains invalid characters
password_reset.error_min_length: Password must have at least 8 characters
external_payment.your_payment_accepted: Your payment was accepted
external_payment.your_payment_fail: Your payment failed
invoicing.card_recharge_message: 'Card recharge number: {card}'
invoicing.card_recharge_carwash: ', made at car wash {carwash}'
invoicing.email_title: Your invoice for BKFPay TopUp issued by %issuer%
invoicing.email_body: You will find your invoice issued on %issue-date% with the number %number% for the amount of %amount% %currency% in the attachment
delete_account: Delete account
terms_of_use: Terms of use
privacy_policy: Privacy policy
google_play_badge_alt: Get in on Google Play
app_store_badge_alt: Download on the App Store
invitation.to_app: Invitation to %appName%
invitation.welcome_to_app: Welcome to %appName%!
invitation.info: <b>%ownerName%</b> has invited you to use a convenient way to pay for your car wash using %appName%.
invitation.card_info: A <b>virtual loyalty card</b> has been assigned to your account.
invitation.register_now: 'Create a free account in our app to use it:'
invitation.create_account: Create account
top_up.notification_title: Loyalty card top-up
top_up.notification_content: <b>%ownerName%</b> has topped up your loyalty card <b>%card%</b> with <b>%value% %currency%</b>.
top_up.notification_info: To check the details, use the application.
invitation.install.title: Install BKF Pay
invitation.install.subtitle: 'See how close you are to using the most convenient form of payment for washing at your favorite car wash:'
invitation.install.step_one: create and configure an account
invitation.install.step_two: download the app
invitation.install.step_three: pay conveniently for washing
invitation.payment.title: 'Pay for your car wash with BKF Pay: 3 simple steps'
invitation.payment.step_one: scan the code at the carwash stand
invitation.payment.step_two: select amount and payment method
invitation.payment.step_three: start washing your car
invitation.company.title: Convenient company settlements
invitation.company.subtitle: Do you own a business, does your company have a fleet of vehicles? Thanks to BKF Pay, using the car wash and settling costs is exceptionally simple. Various ways of topping up the account, automatic invoicing, user management are just some of the functionalities that await your company.
invitation.company.step_one: configurable cyclic top-ups
invitation.company.step_two: automatic invoicing
invitation.company.step_three: user management
stand-code: Stand code
alert.client_report_without_email.title: Client without email
alert.client_report_without_email.text: Cyclic reports set but no email address
alert.client_wrong_tax_number.title: Wrong tax number
alert.client_wrong_tax_number.text: Please correct tax number
reports_client_cards_end_time: Generate Time
reports_client_cards_company_name: Name
reports_client_cards_company_nip: TAX ID
reports_client_cards_company_address: Address
reports_client_cards_clients_email_header: Customer cards use report
reports_client_cards_client: Client
reports_client_cards_name: Name
reports_client_cards_taxNumber: Tax identification number
reports_client_cards_city: City
reports_client_cards_address: Address
reports_client_cards_cards: Cards
reports_client_cards_key: Card
reports_client_cards_cardnumber: Card number
reports_client_cards_card_funds: Funds on the card
reports_client_cards_last_usage: Last usage
reports_client_cards_transactions_topups: Top ups
reports_client_cards_transaction_date: Transaction date
reports_client_cards_value: Value
reports_client_cards_source: Source
reports_client_cards_sources_CAR_WASH: Stand
reports_client_cards_sources_DEFAULT: Unknown
reports_client_cards_sources_DISTRIBUTOR: Distributor
reports_client_cards_sources_INTERNET: Internet
reports_client_cards_sources_MONEY_CHANGER: Money changer
reports_client_cards_sources_SCRIPT: Server
reports_client_cards_sources_UNKNOWN: Unknown
reports_client_cards_sources_VACUUM_CLEANER: Vaccum cleaner
reports_client_cards_transaction_type: Transaction type
reports_client_cards_transactions_balance_adjustment: Adjustment
reports_client_cards_transactions_internet: From internet
reports_client_cards_transactions_internet_topup: Top up from the internet
reports_client_cards_transactions_money_changer: From money changer
reports_client_cards_transactions_payment: Payment
reports_client_cards_transactions_payment_for_vacuum: Vacuum cleaner Payment
reports_client_cards_transactions_payment_for_wash: Payment for carwash
reports_client_cards_transactions_payment_from_distributor: Payment for distributor
reports_client_cards_transactions_payments: Payments
reports_client_cards_transactions_promotions: Promotion
reports_client_cards_transactions_purchase: Purchase
reports_client_cards_transactions_topup: Top up
reports_client_cards_transactions_topup_from_carwash: Top up from car wash
reports_client_cards_transactions_topup_from_distributor: Top up from distributor
reports_client_cards_transactions_topup_from_money_changer: Money changer top up
reports_client_cards_stand: Stand
reports_client_cards_value_after_transaction: Balance after transaction
reports_client_cards_promotion: Promotion
reports_client_cards_added_internet_topups: Internet top up sum
reports_client_cards_carwash_topups: Carwash TopUps
reports_client_cards_invoice_number: Invoice no.
reports_client_cards_last_transactions: Last transactions - Card
reports_client_cards_report_for_period: Client card usage for period from {from} to {to}
common_inPeriod: In period
alert_incorrect_email_title: Client's email adress is incorrect
alert_incorrect_email_text: Emil adress is incorrect, please check and correct.
alert_incorrect_tax_number_title: Incorrect tax number or country
alert_incorrect_tax_number_text: Invoicing is turn on but tax numer or country is incorrect.
owner_email_user_create_new_card: User {email} create new card {cardNumber} at stand {standCode} on carwash {carwashName}.
error_cards_fetch_failed: Error while retrieving card list
error_payment_at_stand_failed: Error while paying at the stand
