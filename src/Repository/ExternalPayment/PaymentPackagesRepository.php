<?php

namespace App\Repository\ExternalPayment;

use App\Entity\ExternalPayment\PaymentPackages;
use App\Entity\Loyalty\Enum\CardStatus;
use App\Entity\Owners;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<PaymentPackages>
 */
class PaymentPackagesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PaymentPackages::class);
    }

    public function save(PaymentPackages $entity): PaymentPackages
    {
        $this->getEntityManager()->persist($entity);
        $this->getEntityManager()->flush();
        return $entity;
    }

    public function getList(
        Owners $owner,
        $page = 1,
        $perPage = 10,
        bool $showTotal = true,
    ) {
        $queryBuilder = $this->createQueryBuilder('p');

        $queryBuilder
            ->andWhere('p.owner = :owner')
            ->setParameter('owner', $owner);

        $queryBuilder
            ->andWhere('p.status != :status')
            ->setParameter('status', CardStatus::DELETED);

        if ($showTotal) {
            $countQueryBuilder = clone $queryBuilder;
            $total = $countQueryBuilder->select('COUNT(p)')->getQuery()->getSingleScalarResult();
        } else {
            $total = null;
        }

        $qb = $queryBuilder->setFirstResult(($page - 1) * $perPage)
            ->setMaxResults($perPage)->orderBy('p.value', 'ASC');
        ;

        return ['data' => $qb->getQuery()->getResult(), 'total' => $total];
    }

    //    /**
    //     * @return PaymentPackages[] Returns an array of PaymentPackages objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('p.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?PaymentPackages
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
