<?php

namespace App\Service\Report;

use App\Entity\Owners;
use App\Service\Report\Data\ReportDataFactory;
use I2m\Reports\Enum\FileExtention;
use I2m\Reports\Service\Document\DocumentGeneratorFactory;

class ReportService
{
    public function __construct(
        private ReportDataFactory $reportDataFactory,
        private DocumentGeneratorFactory $documentGeneratorFactory,
    ) {
    }


    public function getFile(string $reportName, array $criteria, Owners $owner, FileExtention $ext): string
    {

        $reportData = $this->reportDataFactory->getReport(
            $reportName,
            $criteria,
            $owner
        );

        $timezone = $owner->getConfig()->getTimezone()->value;
        return $this
            ->documentGeneratorFactory
            ->getGenerator($ext)
            ->getDocument(
                new \DateTime("now", new \DateTimeZone($timezone)),
                $reportData
            )->getPath();
    }
}
