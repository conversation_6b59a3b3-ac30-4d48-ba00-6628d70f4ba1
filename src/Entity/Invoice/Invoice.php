<?php

namespace App\Entity\Invoice;

use App\Entity\Loyalty\Clients;
use App\Entity\Loyalty\Enum\ClientsInvoiceType;
use App\Entity\OwnerConfig;
use App\Entity\Owners;
use App\Repository\Invoice\InvoiceRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\Invoices\Enum\InvoiceKindType;
use I2m\Invoices\Enum\PaymentStatus;
use I2m\Invoices\Enum\PaymentType;
use I2m\Invoices\Interface\IInvoice;
use I2m\Invoices\Interface\IIssuer;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: InvoiceRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Invoice implements IInvoice
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['default:basic','invoices:owner'])]
    private ?int $id = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Owners $issuer = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['invoices:owner'])]
    private ?Clients $client = null;

    #[ORM\Column(length: 32, nullable: true)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?string $number = null;

    #[ORM\Column(length: 32, nullable: true)]
    private ?string $numberFormatted = null;

    #[ORM\Column(nullable: true)]
    private ?int $sequentialNumber = null;

    #[ORM\Column(length: 8, enumType: Currency::class)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?Currency $currency = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?float $totalGross = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?float $totalNet = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?float $totalTax = null;

    #[ORM\Column]
    #[Groups(['default:basic','invoices:owner'])]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column]
    #[Groups(['default:basic','invoices:owner'])]
    private ?\DateTimeImmutable $invoiceDate = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?\DateTimeImmutable $paymentDate = null;

    #[ORM\Column]
    #[Groups(['default:basic','invoices:owner'])]
    private ?\DateTimeImmutable $serviceDate = null;
    #[ORM\Column(nullable: true)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?string $paymentTerm = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $cloudPath = null;

    #[ORM\Column(length: 8)]
    private string $language;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?float $paid = null;

    #[ORM\Column(length: 32, nullable: true)]
    private ?string $externalId = null;

    #[ORM\Column(enumType: PaymentStatus::class, nullable: true)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?PaymentStatus $paymentStatus = null;
    #[ORM\Column(enumType: InvoiceKindType::class, nullable: true)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?InvoiceKindType $kind = null;

    #[ORM\Column(enumType: PaymentType::class, nullable: true)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?PaymentType $paymentType = null;

    /**
     * @var Collection<int, InvoicePosition>
     */
    #[ORM\OneToMany(targetEntity: InvoicePosition::class, mappedBy: 'invoice', cascade: ['persist', 'remove'])]
    private Collection $positions;

    #[ORM\Column(nullable: true)]
    #[Groups(['default:basic','invoices:owner'])]
    private ?\DateTimeImmutable $sendDate = null;

    public function __construct()
    {
        $this->positions = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getIssuer(): ?OwnerConfig
    {
        return $this->issuer->getConfig();
    }

    public function setIssuer(?Owners $issuer): static
    {
        $this->issuer = $issuer;

        return $this;
    }

    public function getClient(): ?Clients
    {
        return $this->client;
    }

    public function setClient(?Clients $client): static
    {
        $this->client = $client;

        return $this;
    }

    public function getNumber(): ?string
    {
        return $this->number;
    }

    public function setNumber(?string $number): static
    {
        $this->number = $number;

        return $this;
    }

    public function getNumberFormatted(): ?string
    {
        return $this->numberFormatted;
    }

    public function setNumberFormatted(?string $numberFormatted): static
    {
        $this->numberFormatted = $numberFormatted;

        return $this;
    }

    public function getSequentialNumber(): ?int
    {
        return $this->sequentialNumber;
    }

    public function setSequentialNumber(?int $number): static
    {
        $this->sequentialNumber = $number;

        return $this;
    }

    public function getCurrency(): ?Currency
    {
        return $this->currency;
    }

    public function setCurrency(Currency $currency): static
    {
        $this->currency = $currency;

        return $this;
    }

    public function getTotalGross(): float
    {
        return $this->totalGross;
    }

    public function setTotalGross(float $totalGross): static
    {
        $this->totalGross = $totalGross;

        return $this;
    }

    public function getTotalNet(): float
    {
        return $this->totalNet;
    }

    public function setTotalNet(float $totalNet): static
    {
        $this->totalNet = $totalNet;

        return $this;
    }

    public function getTotalTax(): float
    {
        return $this->totalTax;
    }

    public function setTotalTax(float $totalTax): static
    {
        $this->totalTax = $totalTax;

        return $this;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getInvoiceDate(): \DateTimeImmutable
    {
        return $this->invoiceDate;
    }

    public function setInvoiceDate(\DateTimeImmutable $invoiceDate): static
    {
        $this->invoiceDate = $invoiceDate;

        return $this;
    }

    public function getPaymentDate(): ?\DateTimeImmutable
    {
        return $this->paymentDate;
    }

    public function setPaymentDate(?\DateTimeImmutable $paymentDate): static
    {
        $this->paymentDate = $paymentDate;

        return $this;
    }

    public function getCloudPath(): ?string
    {
        return $this->cloudPath;
    }

    public function setCloudPath(?string $cloudPath): static
    {
        $this->cloudPath = $cloudPath;

        return $this;
    }

    public function getLanguage(): string
    {
        return $this->language;
    }

    public function setLanguage(string $language): static
    {
        $this->language = $language;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getPaid(): ?float
    {
        return $this->paid;
    }

    public function setPaid(?float $paid): static
    {
        $this->paid = $paid;

        return $this;
    }

    public function getServiceDate(): ?\DateTimeImmutable
    {
        return $this->serviceDate;
    }

    public function setServiceDate(?\DateTimeImmutable $serviceDate): Invoice
    {
        $this->serviceDate = $serviceDate;
        return $this;
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(string $id): Invoice
    {
        $this->externalId = $id;
        return $this;
    }

    public function getPaymentStatus(): ?PaymentStatus
    {
        return $this->paymentStatus;
    }

    public function setPaymentStatus(?PaymentStatus $paymentStatus): Invoice
    {
        $this->paymentStatus = $paymentStatus;
        return $this;
    }

    public function getKind(): InvoiceKindType
    {
        return $this->kind;
    }

    public function setKind(InvoiceKindType $kind): Invoice
    {
        $this->kind = $kind;
        return $this;
    }

    public function getPaymentType(): ?PaymentType
    {
        return $this->paymentType;
    }

    public function setPaymentType(?PaymentType $paymentType): Invoice
    {
        $this->paymentType = $paymentType;
        return $this;
    }

    /**
     * @return Collection<int, InvoicePosition>
     */
    public function getPositions(): Collection
    {
        return $this->positions;
    }

    public function addPosition(string $name, float $quantity, float $grossPrice, ?int $tax): static
    {

        $position = (new InvoicePosition())
                        ->setName($name)
                        ->setQuantity($quantity)
                        ->setGrossPrice($grossPrice)
                        ->setTax($tax);
        ;

        if (!$this->positions->contains($position)) {
            $this->positions->add($position);
            $position->setInvoice($this);
        }

        return $this;
    }

    public function removePosition(InvoicePosition $position): static
    {
        if ($this->positions->removeElement($position)) {
            // set the owning side to null (unless already changed)
            if ($position->getInvoice() === $this) {
                $position->setInvoice(null);
            }
        }

        return $this;
    }

    #[ORM\PreFlush]
    public function updatedTimestamps(): void
    {
    }

    public function getAdditionalInfo(): ?string
    {
        return null;
    }

    public function getPaymentTerm(): ?\DateInterval
    {
        return $this->paymentTerm ? new \DateInterval($this->paymentTerm) : null;
    }

    public function setPaymentTerm(?string $interval): Invoice
    {
        $this->paymentTerm = $interval;
        return $this;
    }

    public function getSendDate(): ?\DateTimeImmutable
    {
        return $this->sendDate;
    }

    public function setSendDate(\DateTimeImmutable $sendDate): static
    {
        $this->sendDate = $sendDate;

        return $this;
    }

    public function getStatus(): PaymentStatus
    {
        // do implementacji
        return PaymentStatus::Pending;
    }

    public function getDiscount(): ?int
    {
        return null;
    }
}
