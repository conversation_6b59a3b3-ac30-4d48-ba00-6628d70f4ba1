<?php

namespace App\Service\QrPrintGenerator;

use App\Entity\Enum\Languages;
use chillerlan\QRCode\Common\EccLevel;
use chillerlan\QRCode\QRCode;
use chillerlan\QRCode\QROptions;
use Knp\Snappy\Pdf;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;
use ZipArchive;

abstract class AbstractQrPrintGenerator
{
    public function __construct(
        private readonly Environment $templating,
        private readonly TranslatorInterface $translator,
        private readonly Pdf $pdfPrinter,
    ) {
    }

    abstract protected function qrCodeLink($code): string;

    abstract protected function overlayTemplate(): string;

    abstract protected function overlayPageParameter(): array;

    abstract protected function pdfBackgroundPath(Languages $language): ?string;



    public function generateSingle(array $params, Languages $language)
    {
        $pdfPrintBackgroundPath = $this->pdfBackgroundPath($language);

        return $this->generatePdf(
            $pdfPrintBackgroundPath,
            $this->qrCodeLink($params['code']),
            $language->value,
            $params
        );
    }

    /**
     * raturns qr image in base64
     */
    private function generateSvg(string $qrCodeUrl): string
    {
        $options = new QROptions([
            'version'    => 4,
            'eccLevel'   => EccLevel::M,
            'imageBase64' => false,
        ]);

        $qrcode = new QRCode($options);

        return $qrcode->render($qrCodeUrl);
    }

    public function generatePdf(
        string $backgroundPdfPath,
        string $qrCodeUrl,
        string $language,
        array $params = [],
    ): ?string {
        $code = array_key_exists('code', $params) ? $params['code'] : null;

        $qrImage = $this->generateSvg($qrCodeUrl);

        $html = $this->templating->render(
            $this->overlayTemplate(),
            [
                'params' => $params,
                'label' => $this->translator->trans('stand-code', locale: $language),
                'qr_code_image' => $qrImage,
            ]
        );
        $tmpPdfPath = $this->getTmpPdfPath();

        $this->pdfPrinter->generateFromHtml(
            $html,
            $tmpPdfPath,
            $this->overlayPageParameter(),
        );

        $newPdfFile = $this->getTmpPdfPath();
        $newPdfFileVectorized = $this->getTempDir() . DIRECTORY_SEPARATOR . $code . ".pdf";

        if (file_exists($tmpPdfPath)) {
            $output = [];
            $return_var = null;

            // merge pdf's
            $command = 'pdftk ' . $tmpPdfPath . ' background ' . $backgroundPdfPath . ' output ' . $newPdfFile;
            exec(
                $command,
                $output,
                $return_var
            );

            if ($return_var > 0) {
                throw new \Exception(
                    "Pdftk command error $command. Incorect return status: $return_var\n" .
                    "output: " . json_encode($output),
                    $return_var
                );
            }

            // font to vector
            $command = 'gs -dNOPAUSE -q -sDEVICE=pdfwrite -sColorConversionStrategy=CMYK '
                . '-ddProcessColorModel=/DeviceCMYK -dNoOutputFonts -sOutputFile='
                . $newPdfFileVectorized . ' -dBATCH ' . $newPdfFile;
            exec(
                $command,
                $output,
                $return_var
            );

            if ($return_var > 0) {
                throw new \Exception("Ghostscript command error $command. Incorect return status: " . $return_var, $return_var);
            }
        }

        return file_exists($newPdfFileVectorized) ? $newPdfFileVectorized : null;
    }

    private function getTmpPdfPath()
    {
        $dir = $this->getTempDir();
        return $dir . DIRECTORY_SEPARATOR .  uniqid() . '.pdf';
    }

    public function getTempDir(): string
    {
        $dir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . "stickers";
        if (!file_exists($dir)) {
            mkdir(directory: $dir, recursive: true);
        }
        return $dir;
    }

    public function zipDirectory(string $sourceDir, string $zipFilePath): bool
    {
        if (!is_dir($sourceDir)) {
            throw new \InvalidArgumentException("Katalog źródłowy nie istnieje: $sourceDir");
        }

        $zip = new ZipArchive();
        if ($zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
            throw new \RuntimeException("Nie udało się utworzyć pliku ZIP: $zipFilePath");
        }

        $sourceDir = realpath($sourceDir);
        $sourceDirLength = strlen($sourceDir) + 1;

        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($sourceDir, \FilesystemIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($files as $file) {
            if (!$file->isFile()) {
                continue;
            }

            $filePath = $file->getRealPath();
            $relativePath = substr($filePath, $sourceDirLength);

            $zip->addFile($filePath, $relativePath);
        }

        return $zip->close();
    }
}
