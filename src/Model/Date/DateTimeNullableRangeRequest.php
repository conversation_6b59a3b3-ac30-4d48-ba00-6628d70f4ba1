<?php

namespace App\Model\Date;

use DateTime;
use DateTimeInterface;
use DateTimeZone;
use Symfony\Component\Validator\Constraints as Assert;

class DateTimeNullableRangeRequest
{
    /**
     * @Assert\DateTime(format="Y-m-d")
     */
    private ?string $dateFrom;

    /**
     * @Assert\DateTime(format="Y-m-d")
     */
    private ?string $dateTo;

    /**
     * @Assert\NotNull
     */
    private $timezone;

    public function __construct($dateFrom, $dateTo, $timezone)
    {
        $this->dateFrom = $dateFrom;
        $this->dateTo = $dateTo;
        $this->timezone = $timezone ?? 'GMT-0';
    }

    public function getDateFrom(): ?DateTimeInterface
    {
        if (null === $this->dateFrom) {
            return null;
        }
        $utcTimezone = new DateTimeZone('GMT-0');
        $dateFrom = new DateTime($this->dateFrom, new DateTimeZone($this->timezone));
        $dateFrom->setTime(0, 0);
        return $dateFrom->setTimezone($utcTimezone);
    }

    public function getDateTo(): ?DateTimeInterface
    {
        if (null === $this->dateTo) {
            return null;
        }
        $utcTimezone = new DateTimeZone('GMT-0');
        $dateTo = new DateTime($this->dateTo, new DateTimeZone($this->timezone));
        $dateTo->setTime(23, 59, 59);
        return $dateTo->setTimezone($utcTimezone);
    }

    public function getTimezone(): DateTimeZone
    {
        return new DateTimeZone($this->timezone);
    }

    public function getOffsetTimezone(): string
    {
        $date = new DateTime('now', new DateTimeZone('GMT-0'));
        $offset = $this->getTimezone()->getOffset($date) / 3600;
        $sign = $offset < 0 ? '-' : '+';
        $offset = abs($offset);

        return sprintf("%s%'.02d:00", $sign, $offset);
    }
}
