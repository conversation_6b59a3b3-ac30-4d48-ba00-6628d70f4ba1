<?php

namespace App\Normalizer;

use App\Entity\Loyalty\TopUp;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class TopUpNormalizer implements NormalizerInterface
{
    public function __construct(
        private ObjectNormalizer $normalizer
    ) {
    }

    public function normalize($object, string $format = null, array $context = [])
    {
        /** @var TopUp $object */
        $object->setCtime($this->getLocalTime($object->getCtime(), $context['timezone'] ?? null));
        $data = $this->normalizer->normalize($object, $format, $context);
        return $data;
    }

    public function supportsNormalization($data, string $format = null, array $context = []): bool
    {
        return  ($data instanceof TopUp);
    }

    public function getLocalTime(?\DateTimeInterface $utcTime, ?\DateTimeZone $timeZone = null): ?\DateTimeInterface
    {
        if ($timeZone && $utcTime) {
            $ct = \DateTime::createFromInterface($utcTime);
            return $ct->setTimezone($timeZone);
        }
        return $utcTime;
    }
    public function getSupportedTypes(?string $format): array
    {
        return [
            TopUp::class => true,
        ];
    }
}
