<?php

namespace App\Entity;

use App\Entity\Enum\Languages;
use App\Entity\Loyalty\Clients;
use App\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\StandardTypes\Interface\IUser;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\HasLifecycleCallbacks]
class User implements UserInterface, PasswordAuthenticatedUserInterface, IUser
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['default:basic'])]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    #[Groups(['default:basic'])]
    private string $email;

    #[ORM\Column(length: 255)]
    private string $password;

    #[ORM\Column(type: Types::JSON)]
    private array $roles = [];

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $ctime = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $mtime = null;


    #[ORM\Column]
    private bool $enabled = true;

    #[ORM\Column]
    private bool $verified = false;

    #[ORM\Column(nullable: true)]
    private ?string $registrationToken = null;

    #[ORM\Column(nullable: true)]
    private ?string $passwordResetToken = null;


    #[ORM\Column(length: 8, enumType: Languages::class)]
    #[Groups(['default:basic'])]
    private Languages $language = Languages::EN;


    #[ORM\OneToMany(targetEntity: LicensePlate::class, mappedBy: 'user')]
    private Collection $licensePlates;

    public function __construct()
    {
        $this->licensePlates = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): static
    {
        $this->email = $email;

        return $this;
    }

    public function getRoles(): array
    {
        return $this->roles;
    }

    public function setRoles(array $roles): static
    {
        $this->roles = $roles;

        return $this;
    }

    public function getCtime(): ?\DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(\DateTimeInterface $ctime): static
    {
        $this->ctime = $ctime;

        return $this;
    }

    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function eraseCredentials()
    {
        // TODO: Implement eraseCredentials() method.
    }

    public function getUserIdentifier(): string
    {
        return $this->email;
    }

    public function getMtime(): ?\DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(?\DateTimeInterface $mtime): static
    {
        $this->mtime = $mtime;
        return $this;
    }

    public function setPassword(string $password): static
    {
        $this->password = $password;
        return $this;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled): static
    {
        $this->enabled = $enabled;
        return $this;
    }

    public function isVerified(): bool
    {
        return $this->verified;
    }

    public function setVerified(bool $verified): static
    {
        $this->verified = $verified;
        return $this;
    }

    public function getRegistrationToken(): ?string
    {
        return $this->registrationToken;
    }

    public function setRegistrationToken(?string $registrationToken): static
    {
        $this->registrationToken = $registrationToken;
        return $this;
    }

    public function getPasswordResetToken(): ?string
    {
        return $this->passwordResetToken;
    }

    public function setPasswordResetToken(?string $passwordResetToken): static
    {
        $this->passwordResetToken = $passwordResetToken;
        return $this;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps(): void
    {
        if (!isset($this->ctime)) {
            $this->ctime  = new \DateTime('now');
        }
        $this->mtime = new \DateTime('now');
    }

    public function getLocale(): string
    {
        return $this->language->value ?? Languages::EN->value;
    }

    public function getLanguage(): Languages
    {
        return $this->language ?? Languages::EN;
    }

    public function setLanguage(Languages $language): User
    {
        $this->language = $language;
        return $this;
    }

    public function getClient(): ?Clients
    {
        return null;
    }

    public function getLicensePlates(): Collection
    {
        return $this->licensePlates;
    }

    public function addLicensePLate(LicensePlate $licensePlate): self
    {
        if (!$this->licensePlates->contains($licensePlate)) {
            $this->licensePlates[] = $licensePlate;
            $licensePlate->setUser($this);
        }

        return $this;
    }

    public function removeLicensePlate(LicensePlate $licensePlate): static
    {
        if ($this->licensePlates->removeElement($licensePlate)) {
            // set the owning side to null (unless already changed)
            if ($licensePlate->getUser() === $this) {
                $licensePlate->setUser(null);
            }
        }

        return $this;
    }
}
