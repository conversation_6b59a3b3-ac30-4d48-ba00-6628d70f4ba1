<?php

namespace App\Service\Loyalty;

use App\Entity\Loyalty\CyclicTopUp;
use App\Entity\Owners;
use App\Repository\Loyalty\CardsRepository;
use App\Repository\Loyalty\CyclicTopUpRepository;

class CyclicTopUpManager
{
    public function __construct(
        private CardsRepository $cardsRepository,
        private CyclicTopUpRepository $cyclicTopUpRepository,
    ) {
    }

    public function importConfigs(
        array $configs,
        Owners $owner
    ) {
        foreach ($configs as $config) {
            $card = $this->cardsRepository->findOneBy(['number' => $config['cardNumber'], 'owner' => $owner]);
            $tmpConfig = (new CyclicTopUp())
                ->setCard($card)
                ->setType($config['type'])
                ->setDiscount($config['discount'])
                ->setComment($config['comment'])
                ->setStartTime(new \DateTime($config['startDate']))
                ->setEndTime(new \DateTime($config['endDate']))
                ->setIsActive($config['isActive'])
                ->setValue($config['value'])
                ->setAddedBy('import command')
            ;

            $this->cyclicTopUpRepository->save($tmpConfig);
        }
    }

    public function getConfigFromFile(string $filePath, Owners $owner): object
    {
        $today = (new \DateTime())->format('Y-m-d H:i');

        $ownerId = $owner->getId();
        $csvData = [];
        $lineNumber = 0;
        $errors = [];
        $handle = fopen($filePath, 'r');
        while (($lineData = fgets($handle)) !== false) {
            $lineNumber++;

            $data = str_getcsv($lineData, ';');
            if (count($data) != 2) {
                $errors[] = $this->getError($lineNumber, 'Could not parse line', $lineData);
                continue;
            }

            [$cardNumber, $value] = $data;

            if (strlen($cardNumber) != 8) {
                $errors[] = $this->getError($lineNumber, 'Card number is incorrect', $lineData);
                continue;
            }

            if (!is_numeric($value)) {
                $errors[] = $this->getError($lineNumber, 'Value is not a number', $lineData);
                continue;
            }

            $card = $this->cardsRepository->findOneBy(['number' => $cardNumber, 'owner' => $ownerId]);

            if (is_null($card)) {
                $errors[] = $this->getError($lineNumber, 'Card not found', $lineData);
            }

            $csvData[] = [
                'ownerId' => $ownerId,
                'cardNumber' => $cardNumber,
                'cardToken' => $card?->getToken() ?? null,
                'value' => $value,
                'type' => 'ALIGN',
                'startDate' => '2024-10-31',
                'endDate' => '2025-12-31',
                'comment' => 'imported at ' . $today,
                'discount' => 0,
                'isActive' => true,
            ];
        }
        fclose($handle);

        return (object) [
            'data' => $csvData,
            'errors' => $errors
        ];
    }

    private function getError(int $lineNumber, string $description, ?string $lineContent = null): array
    {
        return [
            'place' => "line: $lineNumber",
            'description' => $description,
            'content' => !is_null($lineContent) ? "raw data: $lineContent" : '',
        ];
    }
}
