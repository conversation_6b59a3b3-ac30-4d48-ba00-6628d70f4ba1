<?php

namespace App\Entity;

use App\Repository\LicensePlateRepository;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use S<PERSON>fony\Component\Serializer\Annotation as Serializer;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Table]
#[ORM\Entity(repositoryClass: LicensePlateRepository::class)]
#[UniqueEntity(fields: ['User', 'number'], message: 'There is already an account with this email')]
class LicensePlate
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Serializer\Groups(['default:basic'])]
    private ?int $id = null;

    #[ORM\Column]
    #[Serializer\Groups(['default:basic'])]
    private string $number;

    #[ORM\Column]
    #[Serializer\Groups(['default:basic'])]
    private string $name;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: User::class, inversedBy: 'licensePlates')]
    private ?User $user;



    public function setId(int $id): LicensePlate
    {
        $this->id = $id;

        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNumber(): string
    {
        return $this->number;
    }

    public function setNumber(string $number): LicensePlate
    {
        $this->number = $number;
        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): LicensePlate
    {
        $this->user = $user;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): LicensePlate
    {
        $this->name = $name;
        return $this;
    }
}
