<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250205130223 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE owner_config ADD description LONGTEXT DEFAULT NULL, ADD term_of_use LONGTEXT DEFAULT NULL, ADD auto_register_cards TINYINT(1) DEFAULT NULL, CHANGE timezone timezone VARCHAR(64) NOT NULL');
        $this->addSql('ALTER TABLE transactions ADD license_plate VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
