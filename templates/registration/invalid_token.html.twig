{% extends 'base.html.twig' %}

{% block title %}{{ 'registration.invalid_token' | trans }} - {{ app_name }}{% endblock %}

{% block fos_user_content %}
    <main class="registration-page">
        <div class="registration-container">
            <div class="brand-header">
                <img src="{{ asset('assets/images/logo.png') }}" class="brand-logo" alt="{{ app_name }} Logo"/>
            </div>
            
            <div class="registration-card">
                <div class="card-header">
                    <h1>{{ 'registration.invalid_token' | trans }}</h1>
                    <p class="error-message">{{ 'registration.invalid_token_message' | trans }}</p>
                </div>
            </div>
        </div>
    </main>

    <style>
        .registration-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #ef5350 0%, #e53935 100%);
            display: flex;
            justify-content: center;
            font-family: system-ui, -apple-system, sans-serif;
        }

        .registration-container {
            padding-top: 10vh;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }

        .brand-header {
            text-align: center;
            margin-bottom: 1rem;
        }

        .brand-logo {
            height: 60px;
            width: auto;
        }

        .registration-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            overflow: hidden;
        }

        .card-header {
            padding: 2rem 2rem 2.5rem;
            text-align: center;
        }

        .card-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 1.5rem;
        }

        .error-icon {
            color: #ef5350;
            margin-bottom: 1rem;
        }

        .error-message {
            margin: 0;
            color: #4b5563;
            font-size: 1rem;
            line-height: 1.5;
        }

        @media (max-width: 480px) {
            .registration-page {
                padding: 1rem;
            }
            
            .registration-container {
                padding-top: 5vh;
            }

            .card-header {
                padding: 1.5rem 1.5rem 2rem;
            }
        }
    </style>
{% endblock fos_user_content %}