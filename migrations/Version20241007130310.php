<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241007130310 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE external_payment (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, owner_id INT DEFAULT NULL, gate_id INT DEFAULT NULL, initiated_timestamp DATETIME NOT NULL, confirmed_timestamp DATETIME DEFAULT NULL, value DOUBLE PRECISION NOT NULL, status VARCHAR(255) NOT NULL, additional_data JSON DEFAULT NULL COMMENT \'(DC2Type:json)\', external_id LONGTEXT DEFAULT NULL, digest VARCHAR(255) DEFAULT NULL, INDEX IDX_B48A5D4EA76ED395 (user_id), INDEX IDX_B48A5D4E7E3C61F9 (owner_id), INDEX IDX_B48A5D4E897F2CF6 (gate_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE external_payment_log (id INT AUTO_INCREMENT NOT NULL, external_payment_id INT DEFAULT NULL, ctime DATETIME NOT NULL, add_info JSON DEFAULT NULL COMMENT \'(DC2Type:json)\', comment VARCHAR(255) NOT NULL, INDEX IDX_D09BE4861E3D2C69 (external_payment_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE payment_gate (id INT AUTO_INCREMENT NOT NULL, owner_id INT NOT NULL, type VARCHAR(16) NOT NULL, config JSON NOT NULL COMMENT \'(DC2Type:json)\', comment VARCHAR(255) NOT NULL, currency VARCHAR(255) NOT NULL, logo LONGTEXT DEFAULT NULL, UNIQUE INDEX UNIQ_99A80FE07E3C61F9 (owner_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE external_payment ADD CONSTRAINT FK_B48A5D4EA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE external_payment ADD CONSTRAINT FK_B48A5D4E7E3C61F9 FOREIGN KEY (owner_id) REFERENCES owners (id)');
        $this->addSql('ALTER TABLE external_payment ADD CONSTRAINT FK_B48A5D4E897F2CF6 FOREIGN KEY (gate_id) REFERENCES payment_gate (id)');
        $this->addSql('ALTER TABLE external_payment_log ADD CONSTRAINT FK_D09BE4861E3D2C69 FOREIGN KEY (external_payment_id) REFERENCES external_payment (id)');
        $this->addSql('ALTER TABLE payment_gate ADD CONSTRAINT FK_99A80FE07E3C61F9 FOREIGN KEY (owner_id) REFERENCES owners (id)');
        $this->addSql('ALTER TABLE top_up ADD payment_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE top_up ADD CONSTRAINT FK_34813B764C3A3BB FOREIGN KEY (payment_id) REFERENCES external_payment (id)');
        $this->addSql('CREATE INDEX IDX_34813B764C3A3BB ON top_up (payment_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE top_up DROP FOREIGN KEY FK_34813B764C3A3BB');
        $this->addSql('ALTER TABLE external_payment DROP FOREIGN KEY FK_B48A5D4EA76ED395');
        $this->addSql('ALTER TABLE external_payment DROP FOREIGN KEY FK_B48A5D4E7E3C61F9');
        $this->addSql('ALTER TABLE external_payment DROP FOREIGN KEY FK_B48A5D4E897F2CF6');
        $this->addSql('ALTER TABLE external_payment_log DROP FOREIGN KEY FK_D09BE4861E3D2C69');
        $this->addSql('ALTER TABLE payment_gate DROP FOREIGN KEY FK_99A80FE07E3C61F9');
        $this->addSql('DROP TABLE external_payment');
        $this->addSql('DROP TABLE external_payment_log');
        $this->addSql('DROP TABLE payment_gate');
        $this->addSql('DROP INDEX IDX_34813B764C3A3BB ON top_up');
        $this->addSql('ALTER TABLE top_up DROP payment_id');
    }
}
